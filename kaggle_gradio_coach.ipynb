%%capture
# Gemma 3N için
!pip install unsloth
!pip install --no-deps --upgrade transformers
!pip install --no-deps --upgrade timm

# Gradio ve diğer kütüphaneler
!pip install gradio
!pip install opencv-python
!pip install pillow

print("✅ Tüm kütüphaneler yüklendi!")

# Gemma integration modülünü oluştur
gemma_code = '''
import os
import gc
import torch
import numpy as np
from typing import List, Dict, Any, Optional

try:
    from unsloth import FastModel
    GEMMA_AVAILABLE = True
    print("✅ Gemma 3N kütüphaneleri hazır")
except ImportError:
    GEMMA_AVAILABLE = False
    print("⚠️ Simülasyon modunda çalışılacak")

class GemmaCoach:
    def __init__(self):
        self.model = None
        self.tokenizer = None
        self.model_loaded = False
        self.device = "cuda" if torch.cuda.is_available() else "cpu"
        
        if GEMMA_AVAILABLE and torch.cuda.is_available():
            self.load_model()
    
    def load_model(self):
        try:
            print("🦥 Gemma 3N yükleniyor...")
            self.model, self.tokenizer = FastModel.from_pretrained(
                model_name="unsloth/gemma-3n-E4B-it",
                dtype=None,
                max_seq_length=1024,
                load_in_4bit=True,
                full_finetuning=False,
            )
            self.model_loaded = True
            print("✅ Gemma 3N hazır!")
        except Exception as e:
            print(f"❌ Model hatası: {e}")
            self.model_loaded = False
    
    def generate_response(self, situation, context):
        if not self.model_loaded:
            return self._simulate_response(situation, context)
        
        try:
            system_prompt = """Sen pozitif bir AI eğitim koçusun. Öğrencileri motive et, 
            kısa ve etkili Türkçe mesajlar ver. Emoji kullan."""
            
            user_prompt = f"Durum: {situation}, Süre: {context.get('duration', 0)} dk"
            
            messages = [
                {"role": "system", "content": [{"type": "text", "text": system_prompt}]},
                {"role": "user", "content": [{"type": "text", "text": user_prompt}]}
            ]
            
            inputs = self.tokenizer.apply_chat_template(
                messages, add_generation_prompt=True, tokenize=True,
                return_dict=True, return_tensors="pt"
            ).to(self.device)
            
            with torch.no_grad():
                output_ids = self.model.generate(
                    **inputs, max_new_tokens=64, temperature=0.8,
                    top_p=0.9, do_sample=True
                )
            
            response = self.tokenizer.decode(
                output_ids[0][inputs['input_ids'].shape[-1]:], 
                skip_special_tokens=True
            )
            
            # Cleanup
            del inputs, output_ids
            torch.cuda.empty_cache()
            gc.collect()
            
            return response.strip()
            
        except Exception as e:
            print(f"⚠️ Inference hatası: {e}")
            return self._simulate_response(situation, context)
    
    def _simulate_response(self, situation, context):
        import random
        responses = {
            'start': ["🎉 Harika! Çalışma başladı. Başarılar!", "💪 Motive olun! Verimli bir seans geçirelim."],
            'focused': ["👍 Mükemmel! Devam edin.", "🎯 Odaklanmanız harika!"],
            'distracted': ["🔔 Dikkatinizi toplayın.", "🧘 Odaklanma zamanı!"],
            'break': ["☕ Mola zamanı!", "🚶 Biraz hareket edin."],
            'end': ["🎊 Tebrikler! Harika çalıştınız.", "✨ Başarılı bir seans!"]
        }
        return random.choice(responses.get(situation, responses['focused']))

# Global coach
coach = GemmaCoach()
'''

# Kodu çalıştır
exec(gemma_code)
print("🤖 AI Koç sistemi hazır!")

import gradio as gr
import cv2
import numpy as np
import time
import threading
from datetime import datetime

# Global değişkenler
coaching_active = False
start_time = None
session_stats = {'study_time': 0, 'total_sessions': 0}

def analyze_frame(frame):
    """Frame analizi"""
    if frame is None:
        return "Kamera yok", False
    
    # Yüz tespiti
    face_cascade = cv2.CascadeClassifier(cv2.data.haarcascades + 'haarcascade_frontalface_default.xml')
    gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
    faces = face_cascade.detectMultiScale(gray, 1.1, 4)
    
    if len(faces) > 0:
        # Yüz çerçevesi çiz
        for (x, y, w, h) in faces:
            cv2.rectangle(frame, (x, y), (x+w, y+h), (0, 255, 0), 2)
            cv2.putText(frame, "Odakli", (x, y-10), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)
        
        attention = np.random.randint(75, 95)
        return f"✅ Odaklanmış (%{attention})", True
    else:
        return "⚠️ Yüz tespit edilemedi", False

def start_session():
    """Çalışma seansını başlat"""
    global coaching_active, start_time
    
    if coaching_active:
        return "⚠️ Zaten aktif!", "Seans devam ediyor", "0 dk", "Aktif"
    
    coaching_active = True
    start_time = time.time()
    session_stats['total_sessions'] += 1
    
    # AI koç yanıtı
    feedback = coach.generate_response('start', {'duration': 0})
    
    return "🟢 Aktif", feedback, "0 dk", "Başladı"

def stop_session():
    """Seansı durdur"""
    global coaching_active, start_time
    
    if not coaching_active:
        return "🔴 Aktif değil", "Seans başlatılmadı", "0 dk", "Durdu"
    
    duration = int((time.time() - start_time) / 60) if start_time else 0
    session_stats['study_time'] += duration
    
    coaching_active = False
    start_time = None
    
    feedback = coach.generate_response('end', {'duration': duration})
    feedback += f"\n\n📊 Bu seans: {duration} dk | Toplam: {session_stats['study_time']} dk"
    
    return "🔴 Durdu", feedback, f"{duration} dk", "Tamamlandı"

def get_status():
    """Mevcut durumu al"""
    if not coaching_active:
        return "🔴 Pasif", "Başlamak için butona tıklayın", "0 dk", "Bekliyor"
    
    duration = int((time.time() - start_time) / 60) if start_time else 0
    
    # Periyodik geri bildirim
    if duration > 25:
        situation = 'break'
        status = "Mola Zamanı"
    elif duration > 0 and duration % 10 == 0:
        situation = 'focused'
        status = "Devam Ediyor"
    else:
        situation = 'focused'
        status = "Aktif"
    
    feedback = coach.generate_response(situation, {'duration': duration})
    
    return f"🟢 Aktif - {duration} dk", feedback, f"{duration} dk", status

def process_webcam(frame):
    """Webcam işleme"""
    if frame is None:
        return None, "Kamera bağlantısı yok"
    
    analysis, face_detected = analyze_frame(frame)
    
    # Coaching aktifse ve yüz tespit edilmediyse uyar
    if coaching_active and not face_detected:
        cv2.putText(frame, "Kamera karsisina gecin!", (10, 30), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 0, 255), 2)
    
    return frame, analysis

print("✅ Fonksiyonlar hazır!")

# Gradio arayüzü oluştur
with gr.Blocks(
    title="🎓 Gemma 3N AI Eğitim Koçu",
    theme=gr.themes.Soft(),
    css=".main-header {text-align: center; color: #2E86AB; margin: 20px;}"
) as app:
    
    gr.HTML("""
    <div class="main-header">
        <h1>🎓 Gemma 3N AI Eğitim Koçu</h1>
        <p><em>"AI öğretmenlerin yerini almıyor - öğrencileri kendilerinden daha iyi tanıyan arkadaşlar oluyor."</em></p>
        <p>🌐 <strong>Gradio Web Arayüzü ile Kolay Erişim!</strong></p>
    </div>
    """)
    
    with gr.Row():
        with gr.Column(scale=2):
            gr.HTML("<h3>📹 Çalışma Alanınız</h3>")
            webcam = gr.Image(
                sources=["webcam"],
                streaming=True,
                label="Web Kamerası",
                height=400
            )
            
            camera_status = gr.Textbox(
                label="📊 Kamera Analizi",
                value="Kamera bekleniyor...",
                interactive=False
            )
        
        with gr.Column(scale=2):
            gr.HTML("<h3>🎮 Kontrol Paneli</h3>")
            
            with gr.Row():
                start_btn = gr.Button("🚀 Başlat", variant="primary", size="lg")
                stop_btn = gr.Button("⏹️ Durdur", variant="stop", size="lg")
                status_btn = gr.Button("📊 Güncelle", variant="secondary")
            
            session_status = gr.Textbox(
                label="📈 Seans Durumu",
                value="🔴 Koçluk Aktif Değil",
                interactive=False
            )
            
            gr.HTML("<h3>📊 İstatistikler</h3>")
            
            study_time = gr.Textbox(
                label="⏱️ Çalışma Süresi",
                value="0 dakika",
                interactive=False
            )
            
            motivation = gr.Textbox(
                label="💪 Durum",
                value="Hazır",
                interactive=False
            )
    
    # AI Koç Mesaj Alanı
    gr.HTML("<h3>🤖 AI Koçunuz</h3>")
    ai_feedback = gr.Textbox(
        label="💬 AI Koç Mesajları",
        value="Merhaba! Size eşlik etmeye hazırım. Çalışmaya başlamak için 'Başlat' butonuna tıklayın.",
        lines=4,
        interactive=False
    )
    
    # Event handlers
    start_btn.click(
        fn=start_session,
        outputs=[session_status, ai_feedback, study_time, motivation]
    )
    
    stop_btn.click(
        fn=stop_session,
        outputs=[session_status, ai_feedback, study_time, motivation]
    )
    
    status_btn.click(
        fn=get_status,
        outputs=[session_status, ai_feedback, study_time, motivation]
    )
    
    webcam.stream(
        fn=process_webcam,
        inputs=[webcam],
        outputs=[webcam, camera_status],
        time_limit=60
    )
    
    # Kullanım kılavuzu
    gr.HTML("""
    <div style="margin-top: 30px; padding: 20px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); 
                border-radius: 15px; color: white;">
        <h4>🎯 Nasıl Kullanılır:</h4>
        <ol style="margin-left: 20px;">
            <li>Web kameranızın çalıştığından emin olun</li>
            <li>"🚀 Başlat" butonuna tıklayın</li>
            <li>Kamera karşısında normal şekilde çalışın</li>
            <li>AI koçunuz sizi izleyecek ve motivasyon mesajları verecek</li>
            <li>İstatistiklerinizi takip edin</li>
            <li>Bitirdiğinizde "⏹️ Durdur" butonuna tıklayın</li>
        </ol>
        <p><strong>💡 İpucu:</strong> En iyi sonuç için kameranın sizi net görebildiğinden emin olun.</p>
        <p><strong>🔗 Paylaşım:</strong> Bu sayfanın linkini arkadaşlarınızla paylaşabilirsiniz!</p>
    </div>
    """)

# Uygulamayı başlat
print("🌐 Gradio web arayüzü başlatılıyor...")
print("🎓 AI Eğitim Koçunuz hazırlanıyor...")

app.launch(
    server_name="0.0.0.0",  # Tüm IP'lerden erişim
    server_port=7860,       # Gradio standart port
    share=True,             # Genel erişim linki
    debug=False,            # Production için
    show_error=True,        # Hataları göster
    quiet=False             # Logları göster
)