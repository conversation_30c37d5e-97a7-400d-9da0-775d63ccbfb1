{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# 🎓 Gemma 3N AI Study Coach - Gradio Web Interface\n", "\n", "**AI-powered study coaching system running on Kaggle**\n", "\n", "This notebook provides:\n", "- 🌐 Modern Gradio web interface for easy access\n", "- 🤖 Real Gemma 3N AI coaching with advanced analysis\n", "- 📹 Enhanced webcam integration with face detection\n", "- 📊 Real-time statistics and performance tracking\n", "- 🔗 Shareable web link for collaborative learning\n", "- 🎯 Intelligent attention monitoring and feedback"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 📦 Install Required Libraries"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%%capture\n", "# Install Gemma 3N dependencies\n", "!pip install unsloth\n", "!pip install --no-deps --upgrade transformers\n", "!pip install --no-deps --upgrade timm\n", "\n", "# Install Gradio and other libraries\n", "!pip install gradio>=4.0.0\n", "!pip install opencv-python>=4.8.0\n", "!pip install pillow>=10.0.0\n", "!pip install mediapipe>=0.10.0  # For enhanced face detection\n", "!pip install scikit-learn>=1.3.0  # For attention analysis\n", "\n", "print(\"✅ All libraries installed successfully!\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 🤖 Enhanced Gemma 3N Integration"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Gemma integration modülünü oluştur\n", "gemma_code = '''\n", "import os\n", "import gc\n", "import torch\n", "import numpy as np\n", "from typing import List, Dict, Any, Optional\n", "\n", "try:\n", "    from unsloth import FastModel\n", "    GEMMA_AVAILABLE = True\n", "    print(\"✅ Gemma 3N kütüphaneleri hazır\")\n", "except ImportError:\n", "    GEMMA_AVAILABLE = False\n", "    print(\"⚠️ Simülasyon modunda çalışılacak\")\n", "\n", "class GemmaCoach:\n", "    def __init__(self):\n", "        self.model = None\n", "        self.tokenizer = None\n", "        self.model_loaded = False\n", "        self.device = \"cuda\" if torch.cuda.is_available() else \"cpu\"\n", "        \n", "        if GEMMA_AVAILABLE and torch.cuda.is_available():\n", "            self.load_model()\n", "    \n", "    def load_model(self):\n", "        try:\n", "            print(\"🦥 Gemma 3N yükleniyor...\")\n", "            self.model, self.tokenizer = FastModel.from_pretrained(\n", "                model_name=\"unsloth/gemma-3n-E4B-it\",\n", "                dtype=None,\n", "                max_seq_length=1024,\n", "                load_in_4bit=True,\n", "                full_finetuning=False,\n", "            )\n", "            self.model_loaded = True\n", "            print(\"✅ Gemma 3N hazır!\")\n", "        except Exception as e:\n", "            print(f\"❌ Model hatası: {e}\")\n", "            self.model_loaded = False\n", "    \n", "    def generate_response(self, situation, context):\n", "        if not self.model_loaded:\n", "            return self._simulate_response(situation, context)\n", "        \n", "        try:\n", "            system_prompt = \"\"\"Sen pozitif bir AI eğitim koçusun. Öğrencileri motive et, \n", "            kısa ve etkili Türkçe mesajlar ver. <PERSON><PERSON><PERSON>.\"\"\"\n", "            \n", "            user_prompt = f\"Durum: {situation}, Süre: {context.get('duration', 0)} dk\"\n", "            \n", "            messages = [\n", "                {\"role\": \"system\", \"content\": [{\"type\": \"text\", \"text\": system_prompt}]},\n", "                {\"role\": \"user\", \"content\": [{\"type\": \"text\", \"text\": user_prompt}]}\n", "            ]\n", "            \n", "            inputs = self.tokenizer.apply_chat_template(\n", "                messages, add_generation_prompt=True, tokenize=True,\n", "                return_dict=True, return_tensors=\"pt\"\n", "            ).to(self.device)\n", "            \n", "            with torch.no_grad():\n", "                output_ids = self.model.generate(\n", "                    **inputs, max_new_tokens=64, temperature=0.8,\n", "                    top_p=0.9, do_sample=True\n", "                )\n", "            \n", "            response = self.tokenizer.decode(\n", "                output_ids[0][inputs['input_ids'].shape[-1]:], \n", "                skip_special_tokens=True\n", "            )\n", "            \n", "            # Cleanup\n", "            del inputs, output_ids\n", "            torch.cuda.empty_cache()\n", "            gc.collect()\n", "            \n", "            return response.strip()\n", "            \n", "        except Exception as e:\n", "            print(f\"⚠️ Inference hatası: {e}\")\n", "            return self._simulate_response(situation, context)\n", "    \n", "    def _simulate_response(self, situation, context):\n", "        import random\n", "        responses = {\n", "            'start': [\"🎉 <PERSON><PERSON>! <PERSON><PERSON><PERSON><PERSON><PERSON> başladı. Başarılar!\", \"💪 Motive olun! Verimli bir seans geçirelim.\"],\n", "            'focused': [\"👍 Mükemmel! Devam edin.\", \"🎯 Odaklanmanız harika!\"],\n", "            'distracted': [\"🔔 Dikkatinizi toplayın.\", \"🧘 Odaklanma zamanı!\"],\n", "            'break': [\"☕ <PERSON>la zamanı!\", \"🚶 <PERSON><PERSON>z hareket edin.\"],\n", "            'end': [\"🎊 Tebrikler! <PERSON><PERSON> çalıştınız.\", \"✨ Başarılı bir seans!\"]\n", "        }\n", "        return random.choice(responses.get(situation, responses['focused']))\n", "\n", "# Global coach\n", "coach = <PERSON><PERSON><PERSON><PERSON>()\n", "'''\n", "\n", "# Kodu çalıştır\n", "exec(gemma_code)\n", "print(\"🤖 AI Koç sistemi hazır!\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 🌐 Gradio Web Arayüzü"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import gradio as gr\n", "import cv2\n", "import numpy as np\n", "import time\n", "import threading\n", "from datetime import datetime\n", "\n", "# Global değişkenler\n", "coaching_active = False\n", "start_time = None\n", "session_stats = {'study_time': 0, 'total_sessions': 0}\n", "\n", "def analyze_frame(frame):\n", "    \"\"\"Frame analizi\"\"\"\n", "    if frame is None:\n", "        return \"Kamera yok\", <PERSON><PERSON><PERSON>\n", "    \n", "    # Yüz tespiti\n", "    face_cascade = cv2.CascadeClassifier(cv2.data.haarcascades + 'haarcascade_frontalface_default.xml')\n", "    gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)\n", "    faces = face_cascade.detectMultiScale(gray, 1.1, 4)\n", "    \n", "    if len(faces) > 0:\n", "        # <PERSON><PERSON>z çerçevesi çiz\n", "        for (x, y, w, h) in faces:\n", "            cv2.rectangle(frame, (x, y), (x+w, y+h), (0, 255, 0), 2)\n", "            cv2.putText(frame, \"Odakli\", (x, y-10), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)\n", "        \n", "        attention = np.random.randint(75, 95)\n", "        return f\"✅ Odaklanmış (%{attention})\", True\n", "    else:\n", "        return \"⚠️ Yüz tespit edilemedi\", <PERSON><PERSON><PERSON>\n", "\n", "def start_session():\n", "    \"\"\"Çalışma seansını başlat\"\"\"\n", "    global coaching_active, start_time\n", "    \n", "    if coaching_active:\n", "        return \"⚠️ Zaten aktif!\", \"Seans devam ediyor\", \"0 dk\", \"Aktif\"\n", "    \n", "    coaching_active = True\n", "    start_time = time.time()\n", "    session_stats['total_sessions'] += 1\n", "    \n", "    # AI koç yanıtı\n", "    feedback = coach.generate_response('start', {'duration': 0})\n", "    \n", "    return \"🟢 Aktif\", feedback, \"0 dk\", \"Başladı\"\n", "\n", "def stop_session():\n", "    \"\"\"Seansı durdur\"\"\"\n", "    global coaching_active, start_time\n", "    \n", "    if not coaching_active:\n", "        return \"🔴 Akt<PERSON>\", \"Seans başlatılmadı\", \"0 dk\", \"Durd<PERSON>\"\n", "    \n", "    duration = int((time.time() - start_time) / 60) if start_time else 0\n", "    session_stats['study_time'] += duration\n", "    \n", "    coaching_active = False\n", "    start_time = None\n", "    \n", "    feedback = coach.generate_response('end', {'duration': duration})\n", "    feedback += f\"\\n\\n📊 Bu seans: {duration} dk | Toplam: {session_stats['study_time']} dk\"\n", "    \n", "    return \"🔴 Durdu\", feedback, f\"{duration} dk\", \"Tamamlandı\"\n", "\n", "def get_status():\n", "    \"\"\"Mevcut durumu al\"\"\"\n", "    if not coaching_active:\n", "        return \"🔴 Pasif\", \"Başlamak için butona tıklayın\", \"0 dk\", \"Bekliyor\"\n", "    \n", "    duration = int((time.time() - start_time) / 60) if start_time else 0\n", "    \n", "    # Periyodik geri bildirim\n", "    if duration > 25:\n", "        situation = 'break'\n", "        status = \"<PERSON>la Zamanı\"\n", "    elif duration > 0 and duration % 10 == 0:\n", "        situation = 'focused'\n", "        status = \"Devam Ediyor\"\n", "    else:\n", "        situation = 'focused'\n", "        status = \"Aktif\"\n", "    \n", "    feedback = coach.generate_response(situation, {'duration': duration})\n", "    \n", "    return f\"🟢 Aktif - {duration} dk\", feedback, f\"{duration} dk\", status\n", "\n", "def process_webcam(frame):\n", "    \"\"\"Webcam işleme\"\"\"\n", "    if frame is None:\n", "        return None, \"<PERSON><PERSON>a bağlantısı yok\"\n", "    \n", "    analysis, face_detected = analyze_frame(frame)\n", "    \n", "    # Coaching aktifse ve yüz tespit edilmediyse uyar\n", "    if coaching_active and not face_detected:\n", "        cv2.putText(frame, \"Kamera karsisina gecin!\", (10, 30), \n", "                   cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 0, 255), 2)\n", "    \n", "    return frame, analysis\n", "\n", "print(\"✅ Fonksiyonlar hazır!\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 🚀 Web Arayüzünü <PERSON>lat"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Gradio arayüzü oluştur\n", "with gr.Blocks(\n", "    title=\"🎓 Gemma 3N AI Eğitim Koçu\",\n", "    theme=gr.themes.Soft(),\n", "    css=\".main-header {text-align: center; color: #2E86AB; margin: 20px;}\"\n", ") as app:\n", "    \n", "    gr.HTM<PERSON>(\"\"\"\n", "    <div class=\"main-header\">\n", "        <h1>🎓 Gemma 3N AI Eğitim Koçu</h1>\n", "        <p><em>\"AI öğretmenlerin yerini almıyor - öğrencileri kendilerinden daha iyi tanıyan arkadaşlar oluyor.\"</em></p>\n", "        <p>🌐 <strong>Gradio Web Arayüzü ile Ko<PERSON>!</strong></p>\n", "    </div>\n", "    \"\"\")\n", "    \n", "    with gr.<PERSON>():\n", "        with gr.<PERSON>(scale=2):\n", "            gr.HTML(\"<h3>📹 Çalışma Alanınız</h3>\")\n", "            webcam = gr.Image(\n", "                source=\"webcam\",\n", "                streaming=True,\n", "                label=\"Web Kamerası\",\n", "                height=400\n", "            )\n", "            \n", "            camera_status = gr.Textbox(\n", "                label=\"📊 Kamera <PERSON>\",\n", "                value=\"Ka<PERSON>a bekleniyor...\",\n", "                interactive=False\n", "            )\n", "        \n", "        with gr.<PERSON>(scale=2):\n", "            gr.HTML(\"<h3>🎮 Kontrol Paneli</h3>\")\n", "            \n", "            with gr.<PERSON>():\n", "                start_btn = gr.<PERSON>(\"🚀 Başlat\", variant=\"primary\", size=\"lg\")\n", "                stop_btn = gr.<PERSON><PERSON>(\"⏹️ Durdur\", variant=\"stop\", size=\"lg\")\n", "                status_btn = gr.<PERSON>(\"📊 Güncelle\", variant=\"secondary\")\n", "            \n", "            session_status = gr.Textbox(\n", "                label=\"📈 Seans Durumu\",\n", "                value=\"🔴 Koçluk Aktif Değil\",\n", "                interactive=False\n", "            )\n", "            \n", "            gr.HTM<PERSON>(\"<h3>📊 İstatistikler</h3>\")\n", "            \n", "            study_time = gr.Textbox(\n", "                label=\"⏱️ Çalışma Süresi\",\n", "                value=\"0 dakika\",\n", "                interactive=False\n", "            )\n", "            \n", "            motivation = gr.Textbox(\n", "                label=\"💪 Durum\",\n", "                value=\"Hazır\",\n", "                interactive=False\n", "            )\n", "    \n", "    # AI <PERSON><PERSON>j <PERSON>\n", "    gr.HTML(\"<h3>🤖 AI Koçunuz</h3>\")\n", "    ai_feedback = gr.Textbox(\n", "        label=\"💬 AI Koç Mesajları\",\n", "        value=\"<PERSON>rhab<PERSON>! <PERSON>ze eşlik etmeye hazırım. Çalışmaya başlamak için 'Başlat' butonuna tıklayın.\",\n", "        lines=4,\n", "        interactive=False\n", "    )\n", "    \n", "    # Event handlers\n", "    start_btn.click(\n", "        fn=start_session,\n", "        outputs=[session_status, ai_feedback, study_time, motivation]\n", "    )\n", "    \n", "    stop_btn.click(\n", "        fn=stop_session,\n", "        outputs=[session_status, ai_feedback, study_time, motivation]\n", "    )\n", "    \n", "    status_btn.click(\n", "        fn=get_status,\n", "        outputs=[session_status, ai_feedback, study_time, motivation]\n", "    )\n", "    \n", "    webcam.stream(\n", "        fn=process_webcam,\n", "        inputs=[webcam],\n", "        outputs=[webcam, camera_status],\n", "        time_limit=60\n", "    )\n", "    \n", "    # <PERSON><PERSON><PERSON>m kılavuzu\n", "    gr.HTM<PERSON>(\"\"\"\n", "    <div style=\"margin-top: 30px; padding: 20px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); \n", "                border-radius: 15px; color: white;\">\n", "        <h4>🎯 <PERSON><PERSON><PERSON><PERSON> Kullanılır:</h4>\n", "        <ol style=\"margin-left: 20px;\">\n", "            <li>Web kameranızın çalıştığından emin olun</li>\n", "            <li>\"🚀 Başlat\" butonuna tıklayın</li>\n", "            <li>Ka<PERSON>a ka<PERSON>ında normal şekilde çalışın</li>\n", "            <li>AI koçunuz sizi izleyecek ve motivasyon mesajları verecek</li>\n", "            <li>İstatistiklerinizi takip edin</li>\n", "            <li>Bitirdiğinizde \"⏹️ Durdur\" butonuna tıklayın</li>\n", "        </ol>\n", "        <p><strong>💡 İpu<PERSON>:</strong> En iyi sonuç için kameranın sizi net görebildiğinden emin olun.</p>\n", "        <p><strong>🔗 Paylaşım:</strong> <PERSON>u say<PERSON>ın linkini arkadaşlarınızla paylaşabilirsiniz!</p>\n", "    </div>\n", "    \"\"\")\n", "\n", "# Uy<PERSON>lam<PERSON>ı başlat\n", "print(\"🌐 Gradio web arayüzü başlatılıyor...\")\n", "print(\"🎓 AI Eğitim Koçunuz hazırlanıyor...\")\n", "\n", "app.launch(\n", "    server_name=\"0.0.0.0\",  # Tüm IP'lerden eri<PERSON>im\n", "    server_port=7860,       # Gradio standart port\n", "    share=True,             # <PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON> linki\n", "    debug=False,            # Production için\n", "    show_error=True,        # <PERSON><PERSON><PERSON><PERSON> gö<PERSON>\n", "    quiet=False             # <PERSON><PERSON><PERSON><PERSON>\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 🎉 Başarıyla Başlatıldı!\n", "\n", "**AI Eğitim Koçunuz artık web üzerinden erişilebilir!**\n", "\n", "### 🔗 <PERSON><PERSON><PERSON><PERSON>:\n", "- **<PERSON><PERSON>**: http://localhost:7860\n", "- **Genel**: Yu<PERSON><PERSON>da görünen Gradio paylaşım linki\n", "\n", "### ✨ Özel<PERSON><PERSON>:\n", "- 🤖 **<PERSON><PERSON><PERSON>ek Gemma 3N AI**: <PERSON><PERSON> GPU'sun<PERSON> model\n", "- 📹 **Web Kamerası**: Gerçek zamanlı görüntü analizi\n", "- 🌐 **Web Arayüzü**: Her yerden eri<PERSON>\n", "- 📊 **İstatistikler**: Detaylı çalışma takibi\n", "- 🔗 **Paylaşılabilir**: Link ile başkalar<PERSON><PERSON>n\n", "\n", "### 🎯 Kullanım:\n", "1. Yukarıdaki web arayüzünü kullanın\n", "2. <PERSON><PERSON><PERSON> i<PERSON> verin\n", "3. \"Başlat\" butonuna tıklayın\n", "4. Çalış<PERSON>ya devam edin!\n", "\n", "**🎊 İyi çalışmalar! AI koçunuz yanınızda.**"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.0"}}, "nbformat": 4, "nbformat_minor": 4}