{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# 🎯 Basit AI Koç Demo\n", "\n", "Bu notebook, Gemma 3N ile basit bir AI koç örneği gösterir.\n", "<PERSON><PERSON><PERSON> o<PERSON> da test edebilirsiniz!"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Basit test - Gemma 3<PERSON> olmadan\n", "import time\n", "import random\n", "from datetime import datetime\n", "\n", "def simple_ai_coach(situation):\n", "    \"\"\"Basit AI koç simülasyonu\"\"\"\n", "    \n", "    responses = {\n", "        \"başlangıç\": [\n", "            \"🎉 Harika! Çalışma seansınız başladı. Başarılar!\",\n", "            \"💪 Motive olun! Bu seansı verimli geçirelim.\",\n", "            \"🚀 Hazırsın<PERSON>z! Hedeflerinize odaklanın.\"\n", "        ],\n", "        \"devam\": [\n", "            \"👍 Harika gidiyorsunuz! Devam edin.\",\n", "            \"🎯 Odaklanmanız çok iyi. Böyle devam!\",\n", "            \"⭐ Performansınız mükemmel!\"\n", "        ],\n", "        \"mola\": [\n", "            \"☕ Kısa bir mola zamanı gelmiş olabilir.\",\n", "            \"🧘 <PERSON><PERSON><PERSON> ne<PERSON>, sonra devam edelim.\",\n", "            \"🚶 <PERSON><PERSON><PERSON>a kalkıp biraz hareket edin.\"\n", "        ],\n", "        \"bitiş\": [\n", "            \"🎊 Tebrikler! Harika bir çalışma seansıydı.\",\n", "            \"✨ Bugün çok iyi çalıştınız. <PERSON>r duyun!\",\n", "            \"🏆 Başarılı bir gün geçirdiniz!\"\n", "        ]\n", "    }\n", "    \n", "    return random.choice(responses.get(situation, responses[\"devam\"]))\n", "\n", "# Test\n", "print(\"🤖 AI Koç Test:\")\n", "print(\"<PERSON><PERSON><PERSON><PERSON><PERSON>:\", simple_ai_coach(\"ba<PERSON><PERSON><PERSON><PERSON>\"))\n", "print(\"Devam:\", simple_ai_coach(\"devam\"))\n", "print(\"Mola:\", simple_ai_coach(\"mola\"))\n", "print(\"Bitiş:\", simple_ai_coach(\"bitiş\"))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# <PERSON><PERSON>t ç<PERSON>ışma takibi\n", "class SimpleStudyTracker:\n", "    def __init__(self):\n", "        self.start_time = None\n", "        self.is_active = False\n", "        self.total_time = 0\n", "        \n", "    def start_session(self):\n", "        self.start_time = time.time()\n", "        self.is_active = True\n", "        print(\"📚\", simple_ai_coach(\"ba<PERSON><PERSON><PERSON><PERSON>\"))\n", "        \n", "    def get_status(self):\n", "        if not self.is_active:\n", "            return \"❌ Çalışma aktif değil\"\n", "            \n", "        elapsed = int((time.time() - self.start_time) / 60)\n", "        \n", "        if elapsed < 5:\n", "            return f\"⏱️ {elapsed} dakika - \" + simple_ai_coach(\"devam\")\n", "        elif elapsed < 25:\n", "            return f\"⏱️ {elapsed} dakika - \" + simple_ai_coach(\"devam\")\n", "        else:\n", "            return f\"⏱️ {elapsed} dakika - \" + simple_ai_coach(\"mola\")\n", "    \n", "    def end_session(self):\n", "        if self.is_active:\n", "            elapsed = int((time.time() - self.start_time) / 60)\n", "            self.total_time += elapsed\n", "            self.is_active = False\n", "            print(f\"✅ Seans bitti: {elapsed} dakika\")\n", "            print(\"🎉\", simple_ai_coach(\"bitiş\"))\n", "            return elapsed\n", "        return 0\n", "\n", "# <PERSON><PERSON>\n", "tracker = SimpleStudyTracker()\n", "print(\"✅ Basit çalışma takibi hazır!\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Demo kullanım\n", "print(\"🎮 Demo Başlıyor...\\n\")\n", "\n", "# Çal<PERSON>şmayı başlat\n", "tracker.start_session()\n", "\n", "# Birkaç durum kontrolü simüle et\n", "for i in range(3):\n", "    time.sleep(2)  # 2 saniye bekle\n", "    print(f\"\\n[{datetime.now().strftime('%H:%M:%S')}] {tracker.get_status()}\")\n", "\n", "# Seansı bitir\n", "print(\"\\n\" + \"=\"*50)\n", "tracker.end_session()\n", "\n", "print(\"\\n🎯 Demo tamamlandı! Gerçek notebook'u Kaggle'da çalıştırın.\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 🚀 <PERSON><PERSON><PERSON>\n", "\n", "Bu basit demo çalıştıysa, ş<PERSON><PERSON> ana notebook'u Kaggle'da çalıştırın:\n", "\n", "1. `gemma3n_education_coach.ipynb` dos<PERSON><PERSON><PERSON><PERSON>\n", "2. <PERSON><PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON>\n", "3. Gerçek AI koçunuzla çalışmaya başlayın!\n", "\n", "**Not**: <PERSON><PERSON>'da internet erişimi ve kamera izinleri gereklidir."]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.0"}}, "nbformat": 4, "nbformat_minor": 4}