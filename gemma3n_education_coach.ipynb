%%capture
# Gemma 3N için gerekli kütüphaneler
!pip install unsloth
!pip install --no-deps --upgrade transformers
!pip install --no-deps --upgrade timm

# Web kamerası ve ses için
!pip install opencv-python
!pip install ipywidgets
!pip install IPython

print("✅ Tüm kütüphaneler yüklendi!")

import os
from unsloth import FastModel
import torch
import gc
import time
from datetime import datetime

print("🦥 Gemma 3N modeli yükleniyor...")

model, tokenizer = FastModel.from_pretrained(
    model_name = "unsloth/gemma-3n-E4B-it",
    dtype = None,
    max_seq_length = 1024,
    load_in_4bit = True,
    full_finetuning = False,
)

print("✅ Model hazır! AI koçunuz aktif.")

def ai_coach_analyze(messages, max_new_tokens=128):
    """AI koç analizi yapar"""
    inputs = tokenizer.apply_chat_template(
        messages,
        add_generation_prompt=True,
        tokenize=True,
        return_dict=True,
        return_tensors="pt"
    ).to("cuda")
    
    output_ids = model.generate(
        **inputs,
        max_new_tokens=max_new_tokens,
        temperature=1.0,
        top_p=0.95,
        top_k=64,
        do_sample=True
    )
    
    generated_text = tokenizer.decode(
        output_ids[0][inputs['input_ids'].shape[-1]:], 
        skip_special_tokens=True
    )
    
    # Bellek temizliği
    del inputs
    torch.cuda.empty_cache()
    gc.collect()
    
    return generated_text

def create_coach_prompt(situation, context=""):
    """Koç için özel prompt oluştur"""
    system_prompt = """
    Sen bir AI eğitim koçusun. Öğrencileri motive etmek, odaklanmalarına yardım etmek ve 
    öğrenme süreçlerini iyileştirmek için tasarlandın. 
    
    Özellikler:
    - Pozitif ve destekleyici ol
    - Kısa ve etkili mesajlar ver
    - Öğrencinin durumuna göre öneriler sun
    - Türkçe yanıt ver
    """
    
    messages = [
        {"role": "system", "content": [{"type": "text", "text": system_prompt}]},
        {"role": "user", "content": [{"type": "text", "text": f"Durum: {situation}\nBağlam: {context}"}]}
    ]
    
    return messages

print("✅ AI koç fonksiyonları hazır!")

import cv2
import numpy as np
from IPython.display import display, Image, clear_output
import ipywidgets as widgets
from threading import Thread
import base64
from io import BytesIO
from PIL import Image as PILImage

class StudentMonitor:
    def __init__(self):
        self.cap = None
        self.monitoring = False
        self.last_analysis_time = 0
        self.analysis_interval = 10  # 10 saniyede bir analiz
        
    def start_camera(self):
        """Kamerayı başlat"""
        try:
            self.cap = cv2.VideoCapture(0)
            if not self.cap.isOpened():
                print("❌ Kamera açılamadı!")
                return False
            print("✅ Kamera başlatıldı!")
            return True
        except Exception as e:
            print(f"❌ Kamera hatası: {e}")
            return False
    
    def capture_frame(self):
        """Tek frame yakala"""
        if self.cap is None:
            return None
            
        ret, frame = self.cap.read()
        if ret:
            return frame
        return None
    
    def analyze_student_state(self, frame):
        """Öğrenci durumunu analiz et"""
        # Basit yüz tespiti
        face_cascade = cv2.CascadeClassifier(cv2.data.haarcascades + 'haarcascade_frontalface_default.xml')
        gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
        faces = face_cascade.detectMultiScale(gray, 1.1, 4)
        
        if len(faces) > 0:
            return "Öğrenci kamera karşısında ve odaklanmış görünüyor."
        else:
            return "Öğrenci kamera karşısında görünmüyor. Pozisyonunu kontrol etmeli."
    
    def stop_camera(self):
        """Kamerayı durdur"""
        if self.cap:
            self.cap.release()
            self.cap = None
        print("📹 Kamera durduruldu")

# Monitor instance oluştur
monitor = StudentMonitor()
print("✅ Öğrenci izleme sistemi hazır!")

# Widget'ları oluştur
start_button = widgets.Button(description="🚀 Koçluğu Başlat", button_style='success')
stop_button = widgets.Button(description="⏹️ Durdur", button_style='danger')
status_label = widgets.Label(value="📊 Durum: Hazır")
feedback_area = widgets.Textarea(
    value="AI koçunuz burada görünecek...",
    description="🤖 AI Koç:",
    layout=widgets.Layout(width='100%', height='200px')
)

# Çalışma istatistikleri
study_time_label = widgets.Label(value="⏱️ Çalışma Süresi: 0 dakika")
attention_score_label = widgets.Label(value="🎯 Dikkat Skoru: --")
motivation_level_label = widgets.Label(value="💪 Motivasyon: --")

# Global değişkenler
coaching_active = False
start_time = None
feedback_history = []

def start_coaching(b):
    global coaching_active, start_time
    
    if monitor.start_camera():
        coaching_active = True
        start_time = time.time()
        status_label.value = "📊 Durum: Aktif - Sizi izliyorum! 👀"
        feedback_area.value = "🎉 Harika! Çalışma seansınız başladı. Size yardımcı olmak için buradayım!"
        
        # Monitoring thread başlat
        Thread(target=monitoring_loop, daemon=True).start()
    else:
        status_label.value = "❌ Durum: Kamera hatası!"

def stop_coaching(b):
    global coaching_active
    coaching_active = False
    monitor.stop_camera()
    status_label.value = "📊 Durum: Durduruldu"
    
    if start_time:
        total_time = int((time.time() - start_time) / 60)
        feedback_area.value = f"✅ Çalışma seansı tamamlandı! Toplam süre: {total_time} dakika. Harika iş çıkardınız! 🎉"

def monitoring_loop():
    """Ana izleme döngüsü"""
    global coaching_active, start_time
    
    while coaching_active:
        try:
            # Frame yakala
            frame = monitor.capture_frame()
            if frame is None:
                time.sleep(1)
                continue
            
            # Çalışma süresini güncelle
            if start_time:
                elapsed_minutes = int((time.time() - start_time) / 60)
                study_time_label.value = f"⏱️ Çalışma Süresi: {elapsed_minutes} dakika"
            
            # Periyodik analiz
            current_time = time.time()
            if current_time - monitor.last_analysis_time > monitor.analysis_interval:
                
                # Öğrenci durumunu analiz et
                situation = monitor.analyze_student_state(frame)
                
                # AI koç analizi
                messages = create_coach_prompt(
                    situation, 
                    f"Çalışma süresi: {elapsed_minutes} dakika"
                )
                
                ai_feedback = ai_coach_analyze(messages, max_new_tokens=64)
                
                # Feedback'i güncelle
                timestamp = datetime.now().strftime("%H:%M")
                feedback_area.value = f"[{timestamp}] 🤖 {ai_feedback}"
                
                # Skorları güncelle (simüle)
                attention_score = np.random.randint(70, 95)
                motivation_level = np.random.choice(["Yüksek", "Orta", "İyi"])
                
                attention_score_label.value = f"🎯 Dikkat Skoru: {attention_score}%"
                motivation_level_label.value = f"💪 Motivasyon: {motivation_level}"
                
                monitor.last_analysis_time = current_time
            
            time.sleep(2)  # CPU kullanımını azalt
            
        except Exception as e:
            print(f"Monitoring hatası: {e}")
            time.sleep(5)

# Event handler'ları bağla
start_button.on_click(start_coaching)
stop_button.on_click(stop_coaching)

print("✅ Kontrol paneli hazır!")

# Ana kontrol paneli
control_panel = widgets.VBox([
    widgets.HTML("<h2>🎓 AI Eğitim Koçu Kontrol Paneli</h2>"),
    widgets.HBox([start_button, stop_button]),
    status_label,
    widgets.HTML("<hr>"),
    widgets.HTML("<h3>📊 Çalışma İstatistikleri</h3>"),
    study_time_label,
    attention_score_label,
    motivation_level_label,
    widgets.HTML("<hr>"),
    feedback_area
])

display(control_panel)

print("""🎉 AI Eğitim Koçunuz hazır!

Nasıl çalışır:
1. 'Koçluğu Başlat' butonuna tıklayın
2. Kamera izni verin
3. Normal şekilde çalışmaya devam edin
4. AI koçunuz sizi izleyecek ve motivasyon mesajları verecek
5. İstatistiklerinizi takip edin

✨ İyi çalışmalar! Başarılarınızı birlikte takip edeceğiz.
""")

