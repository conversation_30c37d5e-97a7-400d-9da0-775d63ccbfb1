{"cells": [{"cell_type": "markdown", "id": "18550552", "metadata": {"id": "wh4ZpnndMvaO", "papermill": {"duration": 0.035145, "end_time": "2025-07-31T07:37:14.103329", "exception": false, "start_time": "2025-07-31T07:37:14.068184", "status": "completed"}, "tags": []}, "source": ["# Introduction\n", "\n", "This Notebook introduces few test with Gemma 3n 4B multimodal with Unsloth.\n", "It is based on [Gemma 3N 4B Multimodal finetuning + inference](https://www.kaggle.com/code/danielhanchen/gemma-3n-4b-multimodal-finetuning-inference) notebook from Unsloth.\n", "\n", "What you can find here:\n", "* Tests with text-only questions\n", "* Experiments with image input\n", "* Tests with sound input\n", "* Trurly multi-modal questions: sound, image, and text input and questions about the combined multi-modal input"]}, {"cell_type": "markdown", "id": "65bd8d99", "metadata": {"id": "N3z2yeVsMvaV", "papermill": {"duration": 0.036995, "end_time": "2025-07-31T07:37:14.172922", "exception": false, "start_time": "2025-07-31T07:37:14.135927", "status": "completed"}, "tags": []}, "source": ["# Install packages"]}, {"cell_type": "code", "execution_count": 1, "id": "dbffd06b", "metadata": {"execution": {"iopub.execute_input": "2025-07-31T07:37:14.246682Z", "iopub.status.busy": "2025-07-31T07:37:14.246403Z", "iopub.status.idle": "2025-07-31T07:41:27.460873Z", "shell.execute_reply": "2025-07-31T07:41:27.459803Z"}, "id": "pS-P_shPMvaX", "papermill": {"duration": 253.253051, "end_time": "2025-07-31T07:41:27.462733", "exception": false, "start_time": "2025-07-31T07:37:14.209682", "status": "completed"}, "tags": []}, "outputs": [], "source": ["%%capture\n", "# Install Unsloth\n", "!pip install unsloth"]}, {"cell_type": "code", "execution_count": 2, "id": "066340c1", "metadata": {"execution": {"iopub.execute_input": "2025-07-31T07:41:27.538055Z", "iopub.status.busy": "2025-07-31T07:41:27.537744Z", "iopub.status.idle": "2025-07-31T07:41:32.910162Z", "shell.execute_reply": "2025-07-31T07:41:32.909050Z"}, "id": "lBN09c1tUlSV", "papermill": {"duration": 5.411608, "end_time": "2025-07-31T07:41:32.911822", "exception": false, "start_time": "2025-07-31T07:41:27.500214", "status": "completed"}, "tags": []}, "outputs": [], "source": ["%%capture\n", "# Install latest transformers for Gemma 3N\n", "!pip install --no-deps --upgrade transformers # Only for Gemma 3N\n", "!pip install --no-deps --upgrade timm # Only for Gemma 3N"]}, {"cell_type": "markdown", "id": "b81b8137", "metadata": {"id": "TGMWlrRdzwgf", "papermill": {"duration": 0.033479, "end_time": "2025-07-31T07:41:32.979851", "exception": false, "start_time": "2025-07-31T07:41:32.946372", "status": "completed"}, "tags": []}, "source": ["# Prepare model\n", "\n", "We install needed packages and initialize model and tokenizer.\n", "From the available `Gemma 3N` models we will use `unsloth/gemma-3n-E4B-it` (Gemma 3N 4B instruction tuned).\n", "\n", "`FastModel` supports loading nearly any model now! This includes Vision, Text and Audio models!"]}, {"cell_type": "code", "execution_count": 3, "id": "f9c6bd6e", "metadata": {"execution": {"iopub.execute_input": "2025-07-31T07:41:33.044303Z", "iopub.status.busy": "2025-07-31T07:41:33.043954Z", "iopub.status.idle": "2025-07-31T07:43:20.342459Z", "shell.execute_reply": "2025-07-31T07:43:20.341673Z"}, "id": "-Xbb0cuLzwgf", "outputId": "2796d292-fe2d-483d-dce9-caff7af34e83", "papermill": {"duration": 107.332764, "end_time": "2025-07-31T07:43:20.343997", "exception": false, "start_time": "2025-07-31T07:41:33.011233", "status": "completed"}, "tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🦥 Unsloth: Will patch your computer to enable 2x faster free finetuning.\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-08-02 16:43:16.333481: E external/local_xla/xla/stream_executor/cuda/cuda_fft.cc:477] Unable to register cuFFT factory: Attempting to register factory for plugin cuFFT when one has already been registered\n", "WARNING: All log messages before absl::InitializeLog() is called are written to STDERR\n", "E0000 00:00:1754152996.538177      78 cuda_dnn.cc:8310] Unable to register cuDNN factory: Attempting to register factory for plugin cuDNN when one has already been registered\n", "E0000 00:00:1754152996.595871      78 cuda_blas.cc:1418] Unable to register cuBLAS factory: Attempting to register factory for plugin cuBLAS when one has already been registered\n"]}, {"name": "stdout", "output_type": "stream", "text": ["🦥 Unsloth Zoo will now patch everything to make training faster!\n", "==((====))==  Unsloth 2025.8.1: Fast Gemma3N patching. Transformers: 4.54.1.\n", "   \\\\   /|    Tesla T4. Num GPUs = 2. Max memory: 14.741 GB. Platform: Linux.\n", "O^O/ \\_/ \\    Torch: 2.7.1+cu126. CUDA: 7.5. CUDA Toolkit: 12.6. Triton: 3.3.1\n", "\\        /    Bfloat16 = FALSE. FA [Xformers = 0.0.31.post1. FA2 = False]\n", " \"-____-\"     Free license: http://github.com/unslothai/unsloth\n", "Unsloth: Fast downloading is enabled - ignore downloading bars which are red colored!\n", "Unsloth: Gemma3N does not support SDPA - switching to eager!\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "830167e271d142199841a16397e91209", "version_major": 2, "version_minor": 0}, "text/plain": ["model.safetensors.index.json: 0.00B [00:00, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "a676ec91235f4509a3f58638d3748c86", "version_major": 2, "version_minor": 0}, "text/plain": ["model-00001-of-00003.safetensors:   0%|          | 0.00/3.72G [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "61cd50efd51d439fbcfc2665663f7847", "version_major": 2, "version_minor": 0}, "text/plain": ["model-00002-of-00003.safetensors:   0%|          | 0.00/4.99G [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "e8f7b51f9fbe4b3ea78334fc840a5bcc", "version_major": 2, "version_minor": 0}, "text/plain": ["model-00003-of-00003.safetensors:   0%|          | 0.00/1.15G [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "87ef6db2b47847f7a2e31e924c1110d8", "version_major": 2, "version_minor": 0}, "text/plain": ["Loading checkpoint shards:   0%|          | 0/3 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "093e73bcfc364c79bfbeded0173af746", "version_major": 2, "version_minor": 0}, "text/plain": ["generation_config.json:   0%|          | 0.00/210 [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "4a8db095d0e64bbf83c0b65923e49889", "version_major": 2, "version_minor": 0}, "text/plain": ["processor_config.json:   0%|          | 0.00/98.0 [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "e4d72691286642deb14f595c7fd29fe9", "version_major": 2, "version_minor": 0}, "text/plain": ["chat_template.jinja: 0.00B [00:00, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "d09c53a9945b4bf0a7ff33b8c4128d88", "version_major": 2, "version_minor": 0}, "text/plain": ["preprocessor_config.json: 0.00B [00:00, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "596086db4e36423da67c152e6c691f1d", "version_major": 2, "version_minor": 0}, "text/plain": ["tokenizer_config.json: 0.00B [00:00, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "043ac6790a5d4b179e0e87b9f03d5780", "version_major": 2, "version_minor": 0}, "text/plain": ["tokenizer.model:   0%|          | 0.00/4.70M [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "3bedb1f320574ac3b941f3ee672a7ab6", "version_major": 2, "version_minor": 0}, "text/plain": ["tokenizer.json:   0%|          | 0.00/33.4M [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "e8c9299e9b79497daa1ad83cf1d5b2a1", "version_major": 2, "version_minor": 0}, "text/plain": ["special_tokens_map.json:   0%|          | 0.00/777 [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import os\n", "from unsloth import FastModel\n", "import torch\n", "\n", "fourbit_models = [\n", "    # 4bit dynamic quants for superior accuracy and low memory use\n", "    \"unsloth/gemma-3n-E4B-it-unsloth-bnb-4bit\",\n", "    \"unsloth/gemma-3n-E2B-it-unsloth-bnb-4bit\",\n", "    # Pretrained models\n", "    \"unsloth/gemma-3n-E4B-unsloth-bnb-4bit\",\n", "    \"unsloth/gemma-3n-E2B-unsloth-bnb-4bit\",\n", "\n", "    # Other Gemma 3 quants\n", "    \"unsloth/gemma-3-1b-it-unsloth-bnb-4bit\",\n", "    \"unsloth/gemma-3-4b-it-unsloth-bnb-4bit\",\n", "    \"unsloth/gemma-3-12b-it-unsloth-bnb-4bit\",\n", "    \"unsloth/gemma-3-27b-it-unsloth-bnb-4bit\",\n", "] # More models at https://huggingface.co/unsloth\n", "\n", "model, tokenizer = FastModel.from_pretrained(\n", "    model_name = \"unsloth/gemma-3n-E4B-it\", # Or \"unsloth/gemma-3n-E2B-it\"\n", "    dtype = None, # None for auto detection\n", "    max_seq_length = 1024, # Choose any for long context!\n", "    load_in_4bit = True,  # 4 bit quantization to reduce memory\n", "    full_finetuning = False, # [NEW!] We have full finetuning now!\n", "    # token = \"hf_...\", # use one if using gated models\n", ")"]}, {"cell_type": "markdown", "id": "6e15df84", "metadata": {"id": "ixr4dyTHVIcI", "papermill": {"duration": 0.036459, "end_time": "2025-07-31T07:43:20.420505", "exception": false, "start_time": "2025-07-31T07:43:20.384046", "status": "completed"}, "tags": []}, "source": ["# Test Gemma 3N with multi-modal content\n", "\n", "Gemma 3N can process Text, Vision and Audio!\n", "\n", "Let's first experience how Gemma 3N can handle multimodal inputs. We use Gemma 3N's recommended settings of:\n", "* `temperature = 1.0`\n", "* `top_p = 0.95`  \n", "* `top_k = 64`\n"]}, {"cell_type": "markdown", "id": "a926ba40", "metadata": {"papermill": {"duration": 0.037235, "end_time": "2025-07-31T07:43:20.494638", "exception": false, "start_time": "2025-07-31T07:43:20.457403", "status": "completed"}, "tags": []}, "source": ["We define a function for inference. It will first tokenize the messages and then use `generate` function to generate the output.\n"]}, {"cell_type": "code", "execution_count": 1, "id": "47947657", "metadata": {"execution": {"iopub.execute_input": "2025-07-31T07:43:20.576847Z", "iopub.status.busy": "2025-07-31T07:43:20.576022Z", "iopub.status.idle": "2025-07-31T07:43:20.583118Z", "shell.execute_reply": "2025-07-31T07:43:20.582304Z"}, "id": "UsfUPU-oVQYu", "papermill": {"duration": 0.047952, "end_time": "2025-07-31T07:43:20.584433", "exception": false, "start_time": "2025-07-31T07:43:20.536481", "status": "completed"}, "tags": []}, "outputs": [], "source": ["import torch\n", "import gc\n", "\n", "import torch._dynamo\n", "torch._dynamo.config.suppress_errors = True\n", "torch._dynamo.config.cache_size_limit = 512\n", "\n", "def do_gemma_3n_inference(model, tokenizer, messages, max_new_tokens=128):\n", "    # Tokenize input with chat template\n", "    inputs = tokenizer.apply_chat_template(\n", "        messages,\n", "        add_generation_prompt=True,\n", "        tokenize=True,\n", "        return_dict=True,\n", "        return_tensors=\"pt\"\n", "    ).to(\"cuda\")\n", "\n", "    # Generate output (no streamer as in the initial Notebook)\n", "    output_ids = model.generate(\n", "        **inputs,\n", "        max_new_tokens=max_new_tokens,\n", "        temperature=1.0,\n", "        top_p=0.95,\n", "        top_k=64,\n", "        do_sample=True\n", "    )\n", "\n", "    # Decode just the new generated tokens (excluding prompt)\n", "    generated_text = tokenizer.decode(\n", "        output_ids[0][inputs['input_ids'].shape[-1]:], \n", "        skip_special_tokens=True\n", "    )\n", "\n", "    # Cleanup to reduce VRAM usage\n", "    del inputs\n", "    torch.cuda.empty_cache()\n", "    gc.collect()\n", "\n", "    return generated_text\n"]}, {"cell_type": "markdown", "id": "7a7ea9bc", "metadata": {"papermill": {"duration": 0.03866, "end_time": "2025-07-31T07:43:20.666064", "exception": false, "start_time": "2025-07-31T07:43:20.627404", "status": "completed"}, "tags": []}, "source": ["## Text-only input\n", "\n", "Let's start with text-only input."]}, {"cell_type": "code", "execution_count": 5, "id": "37005f5b", "metadata": {"execution": {"iopub.execute_input": "2025-07-31T07:43:20.750049Z", "iopub.status.busy": "2025-07-31T07:43:20.749163Z", "iopub.status.idle": "2025-07-31T07:44:03.256476Z", "shell.execute_reply": "2025-07-31T07:44:03.255638Z"}, "papermill": {"duration": 42.596216, "end_time": "2025-07-31T07:44:03.300424", "exception": false, "start_time": "2025-07-31T07:43:20.704208", "status": "completed"}, "tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["First President of the United States; key figure in American Revolution.\n", "\n"]}], "source": ["messages = [\n", "        {\n", "    \"role\": \"system\",\n", "    \"content\": [{ \"type\" : \"text\",\n", "                  \"text\" : \"Answer briefly, be laconic if possible.\" }]\n", "    },\n", "    {\n", "    \"role\": \"user\",\n", "    \"content\": [{ \"type\" : \"text\",\n", "                  \"text\" : \"Who was <PERSON>?\" }]\n", "}]\n", "output = do_gemma_3n_inference(model, tokenizer, messages, max_new_tokens = 16)\n", "print(output)"]}, {"cell_type": "markdown", "id": "d69a0e0f", "metadata": {"papermill": {"duration": 0.038841, "end_time": "2025-07-31T07:44:03.378884", "exception": false, "start_time": "2025-07-31T07:44:03.340043", "status": "completed"}, "tags": []}, "source": ["Let's create a function that enclose all this.\n", "\n", "We start with a filtering and coloring function."]}, {"cell_type": "code", "execution_count": 6, "id": "367580e6", "metadata": {"execution": {"iopub.execute_input": "2025-07-31T07:44:03.457272Z", "iopub.status.busy": "2025-07-31T07:44:03.456941Z", "iopub.status.idle": "2025-07-31T07:44:03.461105Z", "shell.execute_reply": "2025-07-31T07:44:03.460580Z"}, "papermill": {"duration": 0.044744, "end_time": "2025-07-31T07:44:03.462178", "exception": false, "start_time": "2025-07-31T07:44:03.417434", "status": "completed"}, "tags": []}, "outputs": [], "source": ["from IPython.display import display, Markdown\n", "\n", "def colorize_text(text):\n", "    for word, color in zip([\"Question\", \"Answer\",\"Execution time\"], [\"blue\", \"red\", \"green\"]):\n", "        text = text.replace(f\"{word}:\", f\"\\n\\n**<font color='{color}'>{word}:</font>**\")\n", "    return text"]}, {"cell_type": "code", "execution_count": 7, "id": "630c5cca", "metadata": {"execution": {"iopub.execute_input": "2025-07-31T07:44:03.540537Z", "iopub.status.busy": "2025-07-31T07:44:03.540229Z", "iopub.status.idle": "2025-07-31T07:44:03.545433Z", "shell.execute_reply": "2025-07-31T07:44:03.544823Z"}, "papermill": {"duration": 0.045825, "end_time": "2025-07-31T07:44:03.546564", "exception": false, "start_time": "2025-07-31T07:44:03.500739", "status": "completed"}, "tags": []}, "outputs": [], "source": ["import numpy as np\n", "from time import time\n", "def run_query(user_input, max_new_tokens=128, model=model, tokenizer=tokenizer):\n", "    _start = time()\n", "    messages = [\n", "        {\n", "    \"role\": \"system\",\n", "    \"content\": [{ \"type\" : \"text\",\n", "                  \"text\" : \"Answer briefly, be laconic if possible.\" }]\n", "    },\n", "    {\n", "    \"role\": \"user\",\n", "    \"content\": [{ \"type\" : \"text\",\n", "                  \"text\" : user_input }]\n", "    }]\n", "    output = do_gemma_3n_inference(model, tokenizer, messages, max_new_tokens = max_new_tokens)\n", "    _end = time()\n", "    formated_output = f\"Question: {user_input}\\nAnswer: {output}\\nExecution time: {np.round(_end-_start, 2)} sec.\"\n", "    display(Markdown(colorize_text(formated_output)))\n", "    \n", "    "]}, {"cell_type": "code", "execution_count": 8, "id": "434a172e", "metadata": {"execution": {"iopub.execute_input": "2025-07-31T07:44:03.625055Z", "iopub.status.busy": "2025-07-31T07:44:03.624824Z", "iopub.status.idle": "2025-07-31T07:44:09.025399Z", "shell.execute_reply": "2025-07-31T07:44:09.024575Z"}, "papermill": {"duration": 5.441394, "end_time": "2025-07-31T07:44:09.026768", "exception": false, "start_time": "2025-07-31T07:44:03.585374", "status": "completed"}, "tags": []}, "outputs": [{"data": {"text/markdown": ["\n", "\n", "**<font color='blue'>Question:</font>** Who was <PERSON>?\n", "\n", "\n", "**<font color='red'>Answer:</font>** First President of the United States; key figure in the American Revolutionary War.\n", "\n", "\n", "\n", "**<font color='green'>Execution time:</font>** 5.4 sec."], "text/plain": ["<IPython.core.display.Markdown object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["run_query(\"Who was <PERSON>?\", max_new_tokens=16)"]}, {"cell_type": "code", "execution_count": 9, "id": "1ebbc602", "metadata": {"execution": {"iopub.execute_input": "2025-07-31T07:44:09.111829Z", "iopub.status.busy": "2025-07-31T07:44:09.111493Z", "iopub.status.idle": "2025-07-31T07:44:12.822772Z", "shell.execute_reply": "2025-07-31T07:44:12.822139Z"}, "papermill": {"duration": 3.753674, "end_time": "2025-07-31T07:44:12.823985", "exception": false, "start_time": "2025-07-31T07:44:09.070311", "status": "completed"}, "tags": []}, "outputs": [{"data": {"text/markdown": ["\n", "\n", "**<font color='blue'>Question:</font>** When was the WWI?\n", "\n", "\n", "**<font color='red'>Answer:</font>** 1914-1918.\n", "\n", "\n", "\n", "**<font color='green'>Execution time:</font>** 3.71 sec."], "text/plain": ["<IPython.core.display.Markdown object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["run_query(\"When was the WWI?\", max_new_tokens=16)"]}, {"cell_type": "code", "execution_count": 10, "id": "de710882", "metadata": {"execution": {"iopub.execute_input": "2025-07-31T07:44:12.904210Z", "iopub.status.busy": "2025-07-31T07:44:12.903921Z", "iopub.status.idle": "2025-07-31T07:44:16.560960Z", "shell.execute_reply": "2025-07-31T07:44:16.560284Z"}, "papermill": {"duration": 3.698044, "end_time": "2025-07-31T07:44:16.562033", "exception": false, "start_time": "2025-07-31T07:44:12.863989", "status": "completed"}, "tags": []}, "outputs": [{"data": {"text/markdown": ["\n", "\n", "**<font color='blue'>Question:</font>** When was The War of 30 years?\n", "\n", "\n", "**<font color='red'>Answer:</font>** 1618-1648.\n", "\n", "\n", "\n", "**<font color='green'>Execution time:</font>** 3.65 sec."], "text/plain": ["<IPython.core.display.Markdown object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["run_query(\"When was The War of 30 years?\", 16)"]}, {"cell_type": "code", "execution_count": 11, "id": "2f2e651b", "metadata": {"execution": {"iopub.execute_input": "2025-07-31T07:44:16.641230Z", "iopub.status.busy": "2025-07-31T07:44:16.640960Z", "iopub.status.idle": "2025-07-31T07:44:22.179857Z", "shell.execute_reply": "2025-07-31T07:44:22.179069Z"}, "papermill": {"duration": 5.579951, "end_time": "2025-07-31T07:44:22.181159", "exception": false, "start_time": "2025-07-31T07:44:16.601208", "status": "completed"}, "tags": []}, "outputs": [{"data": {"text/markdown": ["\n", "\n", "**<font color='blue'>Question:</font>** Who was <PERSON><PERSON><PERSON>?\n", "\n", "\n", "**<font color='red'>Answer:</font>** 35th US President (1961-1963), assassinated.\n", "\n", "\n", "\n", "**<font color='green'>Execution time:</font>** 5.53 sec."], "text/plain": ["<IPython.core.display.Markdown object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["run_query(\"Who was <PERSON><PERSON><PERSON>?\", 32)"]}, {"cell_type": "code", "execution_count": 12, "id": "78b0e606", "metadata": {"execution": {"iopub.execute_input": "2025-07-31T07:44:22.263806Z", "iopub.status.busy": "2025-07-31T07:44:22.263509Z", "iopub.status.idle": "2025-07-31T07:44:27.389049Z", "shell.execute_reply": "2025-07-31T07:44:27.388283Z"}, "papermill": {"duration": 5.168053, "end_time": "2025-07-31T07:44:27.390261", "exception": false, "start_time": "2025-07-31T07:44:22.222208", "status": "completed"}, "tags": []}, "outputs": [{"data": {"text/markdown": ["\n", "\n", "**<font color='blue'>Question:</font>** Who was <PERSON><PERSON><PERSON>?\n", "\n", "\n", "**<font color='red'>Answer:</font>** A Greek historian, soldier, and philosopher of the 4th century BC.\n", "\n", "\n", "\n", "**<font color='green'>Execution time:</font>** 5.12 sec."], "text/plain": ["<IPython.core.display.Markdown object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["run_query(\"Who was <PERSON><PERSON><PERSON>?\", 32)"]}, {"cell_type": "code", "execution_count": 13, "id": "db95cd59", "metadata": {"execution": {"iopub.execute_input": "2025-07-31T07:44:27.470553Z", "iopub.status.busy": "2025-07-31T07:44:27.469614Z", "iopub.status.idle": "2025-07-31T07:44:32.831272Z", "shell.execute_reply": "2025-07-31T07:44:32.830600Z"}, "papermill": {"duration": 5.401128, "end_time": "2025-07-31T07:44:32.832461", "exception": false, "start_time": "2025-07-31T07:44:27.431333", "status": "completed"}, "tags": []}, "outputs": [{"data": {"text/markdown": ["\n", "\n", "**<font color='blue'>Question:</font>** Who was <PERSON>?\n", "\n", "\n", "**<font color='red'>Answer:</font>** Emperor <PERSON> of Japan (1852-1912).\n", "\n", "\n", "\n", "**<font color='green'>Execution time:</font>** 5.36 sec."], "text/plain": ["<IPython.core.display.Markdown object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["run_query(\"Who was <PERSON>?\", 32)"]}, {"cell_type": "markdown", "id": "e3c3bfef", "metadata": {"papermill": {"duration": 0.038278, "end_time": "2025-07-31T07:44:32.909628", "exception": false, "start_time": "2025-07-31T07:44:32.871350", "status": "completed"}, "tags": []}, "source": ["## Image input\n", "\n", "Let's check now with images."]}, {"cell_type": "markdown", "id": "461577f7", "metadata": {"papermill": {"duration": 0.037831, "end_time": "2025-07-31T07:44:32.986356", "exception": false, "start_time": "2025-07-31T07:44:32.948525", "status": "completed"}, "tags": []}, "source": ["We start with the image of a historical figure.\n", "\n", "![](https://media.cnn.com/api/v1/images/stellar/prod/231120172101-lead-image-john-f-kennedy-life-career.jpg)"]}, {"cell_type": "markdown", "id": "3f3361af", "metadata": {"papermill": {"duration": 0.037922, "end_time": "2025-07-31T07:44:33.061780", "exception": false, "start_time": "2025-07-31T07:44:33.023858", "status": "completed"}, "tags": []}, "source": ["For flexibility, we turn back to the initial function, so that we can compose the messages as we wish.\n"]}, {"cell_type": "code", "execution_count": 14, "id": "d00da570", "metadata": {"execution": {"iopub.execute_input": "2025-07-31T07:44:33.146703Z", "iopub.status.busy": "2025-07-31T07:44:33.145983Z", "iopub.status.idle": "2025-07-31T07:45:24.646644Z", "shell.execute_reply": "2025-07-31T07:45:24.645751Z"}, "papermill": {"duration": 51.585987, "end_time": "2025-07-31T07:45:24.687917", "exception": false, "start_time": "2025-07-31T07:44:33.101930", "status": "completed"}, "tags": []}, "outputs": [{"data": {"text/markdown": ["\n", "\n", "**<font color='blue'>Question:</font>** Describe this picture.\n", "\n", "\n", "**<font color='red'>Answer:</font>** A formal portrait of <PERSON> in a suit and tie, seated in a chair.\n", "\n", "\n", "**<font color='green'>Execution time:</font>** 51.49 sec."], "text/plain": ["<IPython.core.display.Markdown object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["jfk_link = \"https://media.cnn.com/api/v1/images/stellar/prod/231120172101-lead-image-john-f-kennedy-life-career.jpg\"\n", "user_input = \"Describe this picture.\"\n", "\n", "_start = time()\n", " \n", "messages = [\n", "    {\"role\": \"system\",\n", "    \"content\": [{ \"type\" : \"text\",\n", "                  \"text\" : \"Answer briefly, be laconic if possible.\" }]\n", "    },\n", "    {\"role\" : \"user\",\n", "    \"content\": [\n", "        { \"type\": \"image\", \"image\" : jfk_link },\n", "        { \"type\": \"text\",  \"text\" : user_input}\n", "    ]\n", "}]\n", "       \n", "output = do_gemma_3n_inference(model, tokenizer, messages, max_new_tokens = 32)\n", "_end = time()\n", "formated_output = f\"Question: {user_input}\\nAnswer: {output}\\nExecution time: {np.round(_end-_start, 2)} sec.\"\n", "display(Markdown(colorize_text(formated_output)))\n", "    "]}, {"cell_type": "markdown", "id": "c0caa8f5", "metadata": {"id": "wZrmFRZpZtGf", "papermill": {"duration": 0.038924, "end_time": "2025-07-31T07:45:24.764939", "exception": false, "start_time": "2025-07-31T07:45:24.726015", "status": "completed"}, "tags": []}, "source": ["# ## Sound input\n", "\n", "Gemma 3N can also hear! Let's test with an audio sample.\n", "\n", "This is a famous speech of JFK about the vision of putting a man on the Moon."]}, {"cell_type": "code", "execution_count": 15, "id": "ce9b9383", "metadata": {"execution": {"iopub.execute_input": "2025-07-31T07:45:24.842563Z", "iopub.status.busy": "2025-07-31T07:45:24.841894Z", "iopub.status.idle": "2025-07-31T07:45:25.181939Z", "shell.execute_reply": "2025-07-31T07:45:25.181157Z"}, "id": "68crYajNZtw1", "outputId": "997423cb-e6a4-4443-ce87-89e7953c40be", "papermill": {"duration": 0.382402, "end_time": "2025-07-31T07:45:25.185292", "exception": false, "start_time": "2025-07-31T07:45:24.802890", "status": "completed"}, "tags": []}, "outputs": [{"data": {"text/html": ["\n", "                <audio  controls=\"controls\" >\n", "                    <source src=\"data:audio/mpeg;base64,SUQzAwAAAAABNlRBTEIAAAAPAAAATkFTQSBSaW5ndG9uZXNUSVQyAAAAEAAAAEpGSyBNb29uIFNwZWFjaENPTU0AAAB5AAAAAAAAACAwMDAwMDAwMCAwMDAwMDIxMCAwMDAwMDdCNiAwMDAwMDAwMDAwMTNDRUJBIDAwMDAwMDAwIDAwMEFCOTRGIDAwMDAwMDAwIDAwMDAwMDAwIDAwMDAwMDAwIDAwMDAwMDAwIDAwMDAwMDAwIDAwMDAwMDAw//uQBAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAASW5mbwAAAA8AAAGhAAKrMgADBggLDQ8SFBcZHB4iJCcpLC4xMzY4Oj1BQ0VISk1PUlRXWVxeYmRnaWxucHN1eHp9gIOFiIqNj5KUl5mbnqKkpqmrrrCztbi6vcDDxcjKzc/R1NbZ297h5Obp6+7w8/X4+vwAAAA5TEFNRTMuOTcgAaoAAAAAAAAAABSAJAZARgAAgAACqnLP7cP2AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA//uQBAAAAhECRMgiGAJOwEhpDGMATL2HGtSRgAF1paOakDAAAAlBiRcAEDcAM8Pe06B8d8R3DJ/4y07+MvH8Mn0PcZOH+1nnh8ARgYH+JzeIP+T/1n+bw/3/g7//3u/AAMH+P8PHgAEgogMCKLiMfLA4AefwAcBlz84EBk/rzOtERAe/9gH/Jg7qRAy+OHsj4BmuCI//Hf8N4AACBHyfmP6P/zB/R2Y+AZA78BB0////////EGIAQGhckBBLvtecdIKUYbqB6uDYIqqwxk5WcIyKQrCcddokcIJdZQ5AQzJeFjkjCebQjnKZ9EIbUqiZr5FCIv3JOFm3O++tcu9pnY78IGlfUh9CluhG8M1d98+5Hli7UwYhC0pMJIQAqtC4cAsIXRz00Mui4QzTmHCEHYKL9JncnPeJbCPqCy+cyIUNFdMmRXehsmB0+7yjcZCuxG6CZm/Tk5kZpeZH6P/CKacm/cGLoCMg0ggcwuVYTOCyoDLk7nLnNqql+J0ZtmXS/dJSN15z923pYAtZLnCTBTnRc0LhmD6HALyHTdBiOZF4//uSBBQAA0o+TAZhoABpJ6mAzDQADAT5TTySgDl0GairmFAElfmibpqJ5oXiSV1bMt3Y6TUS8ZG/9m+UDVBNEyNf+y6dmTnTqRk5qiZ+vTh0Ahk8AWf+npEYBDIqAUhX////wCRpdx+jNsy6X/OUjcccO423pYBl84PcFOWi5QJQzB9E4F73TUxCJIvF35ommnJ54vE5X7MtN0CsmmpsZH/+u6pKGqBuxkar/2W70GXOrSMnBUz/cbw6ATJ4AnP+NT0sAJEVAKQr////4BIg7Ag1M1lRQICTpGCWcZQ8I11gd3Iq+MO0IP0K7bvSxjbdHEiTqpXWvUyOR0QpTiyndHNFkU7JnEyHY6CGRFMshMhhVcX1Yjk3ctTWdhb+zsG/Vv+u3fFn7w8/7ZmfpvwQMQADbTUuHkGgFB0mCJ29fpLFYMLwO7kExvhQuWMfoU7buRLCxl9UcSJPOV1Va13JRClOZdHNeqsmcTCIDEEYLNVc7UgPKYmSmFLi5MwpLU10kiQ1CwTXsGtWYUIaAAArqVrXMgqOsRm9jzq0JCz4ARsRM//7kgQLAAL1NtNNJKAOYEfKqqMUAcuU71m4Y4ARax3rNwxQArrMYwq49FRTW3Ohb3UrlOUgodWKZSHsyudti7eZG7HUrcxymQPVHwDc3vrgnM1rDvtwS82/9YWEbt23/+DpT0/yuPLfT9Uen9gCg9G20m46IlWY+oe2eTZ8ARsRM75jRVx6KITW3OhY+6q6qUg45xxTKQ9mKLh9iWKsl8yNex1Kz7uVEEqvREqyK9aDX1d4vzXSbU73iGVhma0/mb0I9fZf+d3bn7pLf9YAAAARDarTbkEkkm22yOAchZn39JRVLWHh5zkJ5jDx5qDo4JBlwRbk82zrrboyK3J2Qz+joPKxNTj2ah6W9VP/Yztx7qQiLSqnVFmGwsfNhpZykTozEgWngxZacuAAAACIbVRbbkbck222RwDkLM+/pKKpaw8PM4pORhI5UERACEeCtz5XZ11s+jVbi9kIjUrvSz57dPzqf9iJ+JN0iBrRMy9yywPCIDg4FjYKzmC6xPZEJZ1hq+cqACAgFAYULrmm0vu+3vwxdKN45NfLxw7DNhywJML/+5IEDoADIxXabmZghF3jyvrMQACMRN1gePOAGWsWbE8ecAJCyoss/8TAVKMIaTkQsiXtSZmmG2DSPVWlwn5uxpCGQSqcwssWAQZKuSCB0plnqFGiAg4KGUlnal3DlwOIRr3t/Lo16L01fQABBhBtJJLJLbcNtSRmJU9LNQ+qeWymHEVwtBPmsmMsdkYE0hSJOIMRZEvanTTC5gaR6q0uET3Teqt1NUaG5cPewXMAxMYYnHw7YzQpj4MR9mm3W3/nzO1C6dwc2IucEttl2///ftD2VPoS9bJ1OGaEmRYeTLH8zAjQBsugvbNaOZwPUyCvAiNuujF8p/MO2Y1Fpraj0dDpBtbt7n/y5pzUnif/cDnPdRJF9KTmcx7hqsgjntTQacxNPHIdZj/0zlKLbbLrv/+/dPZWNUvYE7eMET5dihQslzMFqANuhNs1o5nDVMgrwhG3XQwuYpT7GkDlRmNTT2mPR0OkNprA/SC0UMq/Yi8aSNhMlEhFBFA5s9igs0CDmhzcXfRVQLcbkkAAAVy2ERgoUtJaa0MxjAAyYqElR3To//uSBA2AAxgy1J5mYARe55rTzEAAy0zbb1mEEBFzm23rMIICKBBAcboQBfAu4A4xkwFXDDAIAHqG5THPMyHDrIoUBcApcbR6DdIWaZw7JUS2/503Q26r9JkE6d2N27qR9BdBTOxzAk12pCIkgVtkttAAAfmOrzabEYdhZdQQDLdXWfuIp5GQIUITiGoWLk+BeJMMuC4EzMvrMimXDAghEy20TcTKcbJolRZ3b8zN0KmWtFTOpGZIsnTuxu3dSNtDaz1e6tNlfWubkYgAAABIDku3/34AADc3NwpO2EjU9qKmlUOrWCzngfScXkABCEA6A2plaQ8AT2wyIB+eZWVNY0XMnkgVrm/zTxZkeqSereG8HxHKjfFb8YkX/SSeNj17x4JOwMXAAAAJAcl2/+/AAAbm5uFJ2wkantRU0qh1awWc8D6Ti8gAIQgHQG1MrSHgCe2GRAPzzKyprGi5k8kCtc3+aeLMj1ST1bod4PiOUw2aFb8pIuuFSTxvSrVycZdg0XWqAAsrft+3+AAAYGV99LSjIOsrBhm+iJmNPHKJ4G5BRP/7kgQPgAMnN1o+PeAGYebrM8e8AIwo72J5hoAZcxlszx7QAuCePmRZQ/W4LWcyfbkL31bX9GOMf9OKdkr4ESuMsS/rCFMzVPTWo/38SRvvWn6xfcJjrfHy+zu1/4D+JR57fH/zEHe/rh0T1v1+wAACkZWLzJY6x60qlCjLufjpXnMW0MwGpBO3A+H0SZX6vBlYn8Jn31bF/Qhxj/sDHE16RK/LEmZsM0aTeteP9/EJ9960/WNzwmOt6Yy+zvF/6XiUed98W+vEP6zoeSNtkeuAAAtS+CZDADqw+xBL1XSu0M2sIzQQMmfR+wkZNACSPMBAFOkOMYdFayUJQSc3JBULsMUjSGMtq1JGmixqjOKaXC1N0Flw0dJbOtSbO3OHXs1zJ19Z+kZ17V2/zhdpa21/YAACzmuXSkULOdBBSXEvEnOQWZcAuk4qwvZNAcpLgiBxpEmPdslDQT83Lioww7SyUyG1ajY0eixquYKaXCOmkYLNEHUvXdnZDOHT9muTn9b0jMQrGtcjUF4AKIfiafZ1FFmOwJBYoFb4iQuhmT0Nzh//+5IEDAzC4zBWn2IABGCF+rPsSACMQP1QbWCrmV6Xq02HnXKRtxdwggaEGrAG4aBQKpUSHQeJEoDrKJAxcsfQ/niBlEz+gqg72QZ6S03db/u79f/6kTIqf/pAg66cUYgQWSu5oIrsuUAEyPhNNKZIhyUJeR6xElPMLUTAUOaIvt33vXixAZgFKC0QBVhYCgVSIGQxh4eSMGfIaK3FAsNYa5VHLKJf9SCSKDp2NGTmS03db/39f/9FI0//Coq8kNBkMkARnGKShdoACIwEkjhMMT8HlENDQskKgMGXqDIDO0u408CSo8RSt9goE2JIWgNIYNWF1p973RjTAloJfQ2vJMuZdB2px/pXPftR7hYUUOAMUVGGY57v//T//QRCl////k4smZ5xnHQPHAAtj8XKi4HoSgrMXSlEztOGiRs43zYZpSH+3D5BUidgfSToS5bc2F8nkQcz9FHfVsYpn15/1eVRTDlYocx593//p//VBSIAl/+QFzwbCr3b86AEwRqZdqoAAKfi7EFuvA4NKz5P8xIgBiM9GKaG7C8qsNrFh6H2//uSBA+M4qov1psJFKRXxfrTYSeWiuTHWmwkUtFamOsNhJZaDoDpBEEyiEU0sUCxKCwgIiWm/t7Y5zC5AcAurp/////UAZYo//pKjJVwMk7hgpQ9qSywM+YjQAA5+LsQW68Dg0rPk/zEiAGIz0YpobsLyqw2sWHofYOgOkEQTKIRTSxQLEoLCAiJab+3vyc5N7BPjS6un///9T/KgsLLED/+tePUaJFGBAUFUa8DBaYFLQQA9+MXoiuiIb1DTW6nBcBRvGVS69OQBG6KC4ffaILZldUUHRJAjcECE8TJtSSlHLzrPFEkKFqKpSKDhF////+oCEfMAsGFP+p7ChNNFTxZZ1JBM7eIiT3wxbA/OiJb1C2WenZ7xP/SyqNUUgeCG5C8b9uE+idsN0ooOiRQVuBghFhUm1KpX7zonhSSElaiqUiiMIK/////qAoQH5g6eYY0tHo6gTNweVQoDen+SgIAAB38dep3lHYiocEKgtTaBbXM6a1etwJDMxDUutqxWrVjB1kTTDJmAaU+FGcBKjsahndDP////9Qw7uXIfqKBAf/7kgQhCIKaL9a7BhSUT8X6x2HiTInI01pnsKPRR5qrTPYUeu0lSkHENDJKcNCggSkXOJAqQIAAB34dbE4yqMRTKB0nrTCdWNq75ymZzwOVgOZmcwlTlGuyLV3sGBArFoDSnwozgIkzlNQzuhn///6/1DDuVicQfnjQCPrfE5cCI6/TWygAvbfDSqB5sYHHU2jhV2fs2tiOUzCANa0RVeCtA6okNYhohS/owshCt7jRX+jGV7ok4SYiDh7WV64hU2tbClSiR5oRiamBRoqChMoMbovAL23w0qgebGBx1No4Vdn7NrYjlMwgDWtEVXgrQOqJDWIaIUv6MLIQre4iK2+iPNPdDhKiDh7LV58k7lalyCmRFTMo0IiEDw2JCglBkLhhfc/11QS7d+OT8a1h40JXNG2V1EU6HRdNELpRQ5FZaeQOIA3aYSzP5iiXbuqxZEL/2K/s5WnO9GaRnCjvf3FoqmT7tgjaOzIlGIYoI1YIjzkHFtmqT6j35WCXbvxyfjWsPGhK5o2yuoinQ6LpohdKKHIrLTyBxAG7TCWZ/MUS7d3/+5IEPg3So0RWmekRdlRnmtM9Ii7KEQ9cZ5hJmTwba4zzCTNPFkQv27Ff2dmnd32sRwo737xbLdN1mZYQ1n40McCYObDMWApnZ4iH0BH5GEgVLko1Y2KuyX7RIVjabKZDNCwuSDd5cvQMsgerGFgk+CMY4UdFuQUFUILb/6ozd5uvdQ6MauqRBxY6dVXSa5X3fU6jmdD2NmscnK801f6HfwFO5KNWNi3ZL9okKxtNlMhmhYXJBu8uXoGWQPVjCwSeoIxjhR0XQxVI//6ozd5utLqHRjVS9hBxY5r513s88vv+rpnW2N+Q6GV5IO+5B38VBT1/4yeolHprXapAc0LY/5oERWWpqmI8YW2QFlUSMh5v/6W65m1aauG//juUSWTjGZ8J/8sKEz/PUMeD6qaoiHi9Av48ed+uW66zR01j7fq8y3my6BT134yeolHprXapAc0LY/5oERWWpqmI8YW2QFlUSEIdX+xXaquUys6G/S6kIphBLAp0J9TAAVf0Q4dpYgLvG7RGZ4olFJZbmPQgF3xhW9wIBdu/GWwaqJXwBaFZ//uSBFiEUqY31xnmQfZPJurjPMI+iiTjXuewY5E4nGvc8wmiOV0mEnq1h4ZWU9wpR7GZDBNQRKxAv//iEfoJHSmDn///UDpThNTS6mlU5S//wio1gGLBKO9LniiyygEi1rU7b4qhregEAu5bBqol/IZplqJzp5VG5kAgBUNzEpW7VUaVTlZJmBf6EZ8gkciuDT/1IHJdGdXIsrkUpiK38IUhlQDdCin48Gh4GUAkOJtfbb2IF/UqASG/4+PMvxJAPsI4qmjUjmrdVmMXpmozQSad8Kv+eWhtpQZVs7Rh+i00KbMZ8ys///51IzVnYWZ35iqU7h2IA0bQ0TmUjtAT0xQpTCWZxesBIX/j48y/EkA+wjiqaNSOat1WYxemajNBJp3wq/55aG2lBlWztGH6LTQptDPmVn///f6zzm+axVcOSoIxtETtSOcLoG9rCmxyZZ9sesYCMl4Ha6vqdKuuM2wy3CYmYEmEJkWXHX40vJfKyO6HzJ0JvNrw5TDgtgUwSqw649P9SxhBnJ//+Q7iYHFw+5XCKRMXD/U/8vLHhQoaZP/7kgR1iIKANVgbBhPETUbrA2DCeInkv1zspK2RRZrrnZSVqoUBAJk3A7XV9TpV1xm2GW4TEzAkwhMiy46/Gl5Xn8d0PmToTebXhymHBbApgqucefJYwgzk///IdxMDi4fcrhFImLnezrqID2uijJcrxQgwoQhqAJrvA1VX5TIDPEqus9UIqyWCF/Xl8TvFtZQef8IWdSD4mtmpMjw4JwZVNNbTO/LmNEkFys37/9jD1cUHkQWGDxECnHCY7p/14xTiqRPep76RRI8AmucDVVflMgM8Sq6z1QirJYIX9er8uzRVmim/okiUYGQik2GNCwUCMCJIjHXBt9S40qD1qv/+P//2HvqCUMkUs0JtR6v11KlUidz5och6qwufj4MX7WBpZxt9wRwqPPFXBcnyaWo2nZ61KFUwZbVjb6T79SSc5iDnecREBzH///VOriKGHKxgseE/9sQPkmIr1/QWAIin498Fe1gKucZPdejhUTPC3Bcnyamo2nZ/qVeiQybVjZ9JPv1RJw4YgzvcoRp///6e4VDDKxgbh/63KDKUAlFVtP7/+5IElYrCmDlXGwkrZFAGCuNhBn6IgMNe7CSp0S6YK42EiTr1WXAc7U/dVQQgAAzgVWFI5ysQclY4W2zzRDspAbZFH7Dm5AbfP0tD0Ck/aSbswQy5EZysE3YqFUpxIdH///+hoiwxznYKcRHsv/7KHzIOSudR57vEiWHsA5AEIAAL+F1oya8rEVKccDm3T4NystdyUvYQdmHb9ii+w0LOEO/KL8KAfnD9yWlynDDX///9ElYE5zsCcG///U9G1R1Uc9TsGaYzN+3+gACqfj4miVsYbNLkrPhJ68Oey68moSjEsUZ6/8lFwM1l65NPQGpFE9QHtr9arwxU/d36ZGS2o11zC4MomVB7p/+pAgkdN4+YQy4jIIL2pHOi4AFU/HxNErYw2aXJWfCT1349l15NQlGJYoz1/5KLgZrL1yaegNSKJ6gPbX61Xhip+7vumjZONdcwuDKJlQe6f/qQBEjtLGsduT7Sr3vcSFkqACBUABFjjJ6WWUINJ7rTkktV52QpcEjtRZC++gsbesDqZLFH6+WbGKOEjaMuWulPl70u3c8I//uSBLsIgpBB1jsMKnZMiDrXYMJsihztXGwYrZFAHWuNgxWyy1r/+8889/pMiaVdTZQkgs/ET3//+skn+wAIFQAEUOMnpZZQg0nutOSS1XnZClwSO1FkL76Cxt6wOpksUfr5ZsYo4SNoy5a6U+XvS7dzwjLWv/7zzz3+kyJpV1NlCSEP7//9SaGptNcIpqjx1Fq1F2vI2Ncc3CKASByEKihoY0aFx6OvzHKOlo+VuzUYs0LjTTWJWrNSJKLifFlzjpWQysFDrWWDsheavGJcAAAQAEj8jU9F/+QIZ/0zPG+Rs8XNosMuOq4AAARh2HCaFsR4KuI5Mgj7n15yQUtxBZBkJJzam1UwJ6z6tuR5qVRSJyGbEQgBtr6iUZLGEwmVOnWfq1l5GAEBwASTH6p/+ZM+pGPx82MkzmIlio63///4c1aa1QAAEONtiVXUaANGmv+j/I4XjF9zsvmHVi7iKnn6EnXueztCsNMgHBMmSPFRUFk2SBOR0gYRAmZYnwFODwSnGL//zE/qzmjpCXYxy7ChH/88n//7xEwgxSFkx6nKAP/7kgTaAAJ6MdfTDDr0TMY6+mGHXovkyU4NKTpBbxkqjZedegAQ422JVdRoA0aa/6P8XheMX3Oy+YdWLuIqefoSde57O0Kw0yAcEyZI8VFQWTZIE5HSBhECZlifAU4PBKcYv/+pif1ZzR0hLsY5dhQj/+eT//8+IhSeOuKiwXFKgBhk4YZVmDfUxxE8NPQJLpTKLFAtqa7WEeMoU0DwXt3Z3iLJRaUxc5ysONPtC5UlUO7tYso7K9tVq2+UfGh0AYcNE238t/p/TR/zXFkFBQ46lrKFq+tn//DNnqIrvWSMOvuHPZhXFU4osPJRSmlMosZjmpttYhR1DRCcON3Z3iLijkxGnObSPT7QuWyqHd24WVdlfKwrb5R+TYfllTLv+Xf7P6d/zNSg9A2EhOJyyJ2CUO4z8Wa7/+ig1d3qAASY41HUkKgAc7yUz/dxmtpMblnuuWWChGUNorXOBH1PdEbHstYDmDMwfZwx0uV2IF6cf2+OQn4fw5i98UeywRMJRn//4cwqfYBeTa81GFEyiCd//+r047//YmtGuhIACTHGo6n/+5IE7Y7i4DjWmwk8RF1HGtNhJ4iLxMlUbL0L0XgY6s2XrXohUADneSmf7uM1tJjcs91yywUIyhtFa5wI+p7ojY9lrAMUErgebQiYzEG1APYkH5+FEfT0w8m9Re2oHjBiv/6YiEicwB7HluQYQNEAUef/ibEjv/8K3DUXLSjWBE29QUW1gX0UNFWQdY3hBeTwXbsOVFlVq8dsq1Sxnw0N9HmuuA7MDOYyoWfGVb5erJcbrDE4+9iJzspcqMpeSyPuPqt0H34b2cqnfGJjICQjexNFp/75RhKEkiOHowvmVFl8IBIAAAQ4rx5I2MDylaUdqKxv6HtmZVBAO614OjbiJIebY5ZUjC5LCeFrem/HMzC5eRGPTfFgq56WyI1s+PoJ7m2Si7hOMhN8d9f/7JNLJ5sPTyoNifCH//oMJbM8ULJdDAACChxMT6a0rFNuutil5TZV7Va32GzU4VcOs1SC21lred6mXT83tIZGOzeHHTZqXd1bAKqLmNp/DhuWonlxd3PO8jEpt9pHMYpAkcOpaKiQwd4d//9Ln7ZWqHAgAAQE//uSBPGMAvc5VxsPSfRhByrjYeg+jIDRUiwxGgF8Gitdh6F6APeEEnbC0FS8gBRm9Lsq7kpnOqdhLyoepTJ4vSMGOmQeT9zUJGUqrw/piEOQ5NyHjRITNca5otgOJUtaqwmeeOxDltYfp8/YGYQ5WdX//jxtVMCrRb50Tilfg0O1ZSAoDNpbzGLd+z9benKRivDdSZ1GxGtoRF2nX6nZMi06Mx4+POEpqQK/UA/opZGpdbUQhGCUeYNrameGJZhLA9sck//fY2WLJJPiNosdRvV//0Ppg6MaIrKmAAkocWrKQFAZtLeYxbv2frd5PSMV4bqTOo2I1tCIu06/U7JkWnRmPHx5wlNSBX6gH9FLI1LrdW5yXM8SXXtq11RtUBsuuqH//+/S3o801Nmym1WI9X//i6MkhQUmmsYqAAAIoh2OGBErYNXKDoCynu7P9gKYo4IpYLsUT23IlQPzyYk8GGOUsuiw5GkTDStjnqHNzzTsir1t5UpJRh2gHiLBG0bX3RCIkEyaOND+DiSRd3gUkQhHJCAo9G/5f9x7U7StE91Z3//7kgTvBULeNVc7Dyt0X6aqpmnmXgt401osPQvRfBprTYeZuv//uw+jMbAgAAgrYUswie9BmwDRjxSfs/81Jtsq16mdYhSMuoE7UAvRMjrcy4vjkWy7KfZmOK+sUpAxuQZh3AY1ewhENBMmjjQ/g4kl94SRg+TGvv/l/3HtTtK0T3Vnf2NRmnbUfMb9rGsCIC/JRUI4IFohjllTUquN+pS0r64QDPUkpykvdTlqiqtAIgjYWtRRANHC3TKHNL7Ncjj2UTTZvHO7J4iifSCACzaDAs4vRZrctCexA0tNJLlkdE8iF/rxBRJkFjdIQZ6ADREABJY47dZlKRWqQMqk8Rs9qY40uEdtUr8P4o+LTQIEp04DaAJjhECx5AoiPbzOxsiHQFTaA6ibxp751lFzj1r5BKZfi/Gf9eQpXQW3BQgz0f/98k7H+g1VQDEAAEqfi1TLC1SrTA+VHlrlifUl295h99Yzrc00Iq3z5KsCjVraeDEu4b55ifPu9Vj83WlJqMYAQXgmRTKzXizTFknc5UJ9/8cztvHQUnFtyWBG2vrdlFz/+5IE8oYDbjTTm1hC9GOGmrdl6F6MCM1QzSRaQWaZq6mGCfrqSQ8SlFEUiuN0Mt6AQAAAG0T0qAAWASH2n3Knupq3LELsRy3H4xch7GiuTdm/RSGJEIN/naVa4C612vCmY4TC4DfZ2JiR1cZW7LOH/RBauoupuJBSFaEDlGqPMTjBsqQNkyaqzoG8XZQm8UQEDg4sAL/1zp7cvy36v3AgAAv4YVy4VCIIR5OlrcuS3ry1MRFzS1LVtXcrHyhItgT8BMoyQoVWwnnh3Nm1IEVuOSoihwn4BMYCoULMriF6veEynybgfPvr7h+H9MDAuLFyn80LCMgPUkKscHAiz9u5ICrAGfjC6kNQikyIdLKsLlnryNAw4HrTGrZtXcrHztNuAxwF9UYO1ngreJ9Z9IFYScyOxUqQI4DowsyvivvvTZ8bff96/IuL1GlTI2MujP6FJkE/07HiJwqtBFoAgAAAAEMLFQzRcYRRExbcO3lZ7uLp3RecIznfd55Zj1R6tCsc3qELggpsm6Po0GFVtapj31Iu2QfQcJOgvz7ABCGIYNUc//uSBOuEIyw011MvMvRuJeqHaSLSjDi7WOy9C9FuGmuph6F6RI0Y5Is2EKP0Lzkj468VEQjneYFHh4X///l6EGwyqjsUAQAAAACGFioZouMIoiYtuHbys93F07ovOEZzvu88sx6o9WhWM71CFwQU2TdH0aDCq2tUx76kXbIPoOEnQX59gCLlxTCZivCTUK1moLN+l+1lf18/QmFPfndINLjW///28o8j0VYKArkFTZ0CP7QwvvdfSbi3bZp2HNG68Xch+oszAgyiJccByCzNCVJa2MKPu+3t63l9MsbwHsvoUJfjGEcDbExv2nw6nLTQlaJJhxDG/f/tCzf9rLPYwGv//8pAr0nXtHbnBjirHgq6argeEZoZH3uvEzXcGad7mjdeu5E6pz0GMzGUqE4TJ04mTAbVvL77et6dUZfg9n6MoSsA4ICR0XtcTWNH1VqVByV/P8IPG/3TlkKLHv//5CbEx4kkaNFWwvUCIswkE4rpJAj0tf2nU5paW9nM43IveeCVak1NIM7UEzGKSCYCCoOMaAqgnxUJR406TN5ulns9V//7kgTiB2M3MdVTD0L0ZAZKqmHpXoxIx1QMvM3ReRkrDYeherjhw8XNBw6D4OdLoCTJ8/SpXUlcy4o9YtJAZLlUA+2GRXP3/D/ylB9pe2jpA7JQEAAAKGE9BIji7rGDk+W5vnjS3rwc4c9LiF5rRL2a5KlgLgS0NBKGYcGBT3ymR8tY18Xwn3w3Q5ydgtR+ADC4cgaIUTzotcJRZKHuaL3z6fsyD5GanCUOHFjH//9Etr1XXpBBGF7MMBVrvgeKG67n7qb5U7dtbwouWrGdqJ3aeGUlmgQ+qjL3gbjIqrU+0l/tTL9/BrfypSxwH2GrBIcJbB2qlcI5lh6bNsxN37/AzVPJgYGP///6F2ULc2kevAWwvZoDVx3wWdDdd791N8lHa8uzwouX7HbU3drylVJvIfWzPwA6M9Vc/tJf7U7//Brz0y1HYhoegJEiWxmqleRy0TWY0xv16CRlJzDQDC4l/zpoNXrVSRYkq3ru0gYAAAEYbjRUqhheQ+C+r702VJlyJ51Z3KOY9l9S/dq5tvFniGkVkMMb54i/T+yyRSlu3fn/+5IE2wajNjHTiy9OhmHGOrdh6F6LgMdU7KR6EXWY6t2Ul0JfPx1Qy5VQRhJ0PqLNEk0p2d7H1/jeeoEEe4qXxDT92P/WQiV/fLCIfX///QiA6yZe7pBAAAARRi/xJ87CaQ3K4LhxqtLLXJX2rFcoAx7D9S3Xpa6936bkLUpkKAL+ZkEHNdhyFRFfXfjeH46ksWRACphdBwQ7IUiqLUatj6/xuvckIXzIV/SUr/nr//FMGor31+XBp7v///cdP95QAIAYWpxCtAk3Ery9MrczOUYdpbere9W+2c6Wpyu7EJTcXC3ZWR+6rWZS+MCXMree+/rnwiKtUgN6BBMFpYiEGh5/39RS28VojVX7sEBQmw8RCP///lx6kHGsbv0ABygACiRx2wydT0RKxyWugScU9a/j+4/7s1zrnCILBiXgsP9JLJFKClJ1/KBRGYnlQBSjlMygYGpKe+aqY1cvpIJFwuF40/uxMNENj5iTf///LlRNIHyDZvfVBAAACthqSljcbSUJ+yXUp7U5em/E+YWXCBZ84MRxtR3AK6qZAn3MnCpb//uSBNkGAzQy1LsvRoRqRkp3ZenQi5THVUwgulFnGOuphjT6YlcwLZ19Uobx8wAF41R3gjAtFg2Z2/3SU51pnotbrQMzhf7sbHX/6RYqmLiupTK2lRmwbRckAJQAACp+FHQjJfEgCqlmYuYMmg8qcyZiUtCmhKuRdCls0Qh8dDHGDTXpv6/1hTL9BXkqd4TE0lvvX//7nI5VjR/NaW6mD5Qb92JCEkv//6Kk1HeqsrrpoUydfikl+a/amAAIhIIBASf41GJqRR/AtkZR92TXbM4teRxziDDjNkAyW9FrTWiVNALE+XCvmrn2//q+ixi7oabgCYsrPt/VRKUs4w5B75WzTinlMDuOf6hdGTqKjbRCqQX2tOH0C4AIhIIBASf41GJqRR/AtkZR9zzpSbn3gU7jLbyRgKU49myGPJWYA0uIAd6zZ3f7TY6eAuHkMAGybO/r//mUtrd6j2L37a9Lec/2uL3HP8USgAHE6BvFKC+a9CEAdAAACXcL1AARLiNhEmDhZnxH3SBmkHeocG9a221Jg2Q9txzIQ4n60XZ3Cvx////7kgTUAAL+MlW7D5L0YWja6knnfMugx12nqLoRapjrtPYtMsxD+bjAKsnQFhqWU03/53yT/6lBoS80dDyFV1Tv1//93aosAEgAABL3F6gERlFWG6duJt/e6QM0r9Xretbbal4+Rnwj+VDipWg4HBC0+pIIIZBMB8BYQw9ZV//T5z/6oQE3mkIuHoaqd9Q+KMQjTpGAEzMSQ4mGy1LQUAAJXYZ0ADLYeFUEzFaXtezWuoG66t4udsmYLqORtirOsPkkww4UCbdc//WYJ8RmBqoLWan/qb5xpM/9EoNEbnkAPFzJHDCpAv93uP5CpgdtLAjstjNx//vPw//o6CgABKbDOgPQ7DcUgJTAspyr2a11A3XVtuMu2SHBdRxO2Ks6w+STDDhQJt1z/9ZgnxGYGqgKzUb/qK+YofP+iUABCc4oExZvMmb+//95bSQ6JeRyJVbbKspOX8PXtbtCAnlQEBzfip7EOUQyJWN99glK/dMPn16jUZvcHxIqZE712Tpd3vr+xGLwiCo4T8v///8T7VNtf/+4cC4sF0+0DwGhrTbr3y//+5IE1oACjzNV0edOpFamSso8qrCLxNdU7Dzr2XwZal2HlXt6ssynNPP8VW7X/A+d0pZVqJjt/2ydczQEyAAgJ3YQRlA1jKA6NafV2d0xbZ3KR/LeVyraNENYUMmo7o056fQvv3+vTM3H47wKh9QA9VPr/0XdlnP/PGg3GwvM5g0EQsdj5v+H4v+ygfVgpmv+w/dJ238HFd/22Z9lACpQykwaXZx+NkxOcNoXvnBJUpO7vok8SBngIkxSdugr//lkHBUeDYOxxvX////6FjG///pJAYPNr/SWTkfUwNNKZ5mucSoopQ8dG1Cn23MX8VeKs9wOVmEoO0EjVQAAWQJdht6w5Xw+nRKL1PjzGQj3q4rUWru70ZLjIfXBIipI3s2EN1O/8LoDQRPgkKxoSo1//5h6HcxvYxQmDo1Zas05qvopKh1V1cqxpiO3YkQBkHuMPlTNYxpFyXGKACYAABKbCOJya7GKeHSr4cfW3fYfJFv8azX1tmSAytMy7Z25GQWubH3vf+PBO50XQdLIaAacTV2/1kQYGAOBzEb/UP51BRBi//uQBOKDIvdF1tHpQfZhZpqqPYdey9kvWQeZB9l2IOqphKl65G0FDAGwwx8Bf+83fPnRvZ0ch71p+LXxDVf5NhVQAEyAACU2FxOTXYxTw6VfDj6277D5It7yah19bZkgMrTMu2duRkFrmx973/jwTudF0HSyGgGnD6u3+siDAwBwDMRvwnvCEClFh2cCgRAA4LDPRf/nX74+WQt6P34s641imKRL75wVwApQE1JwfZWXQ1yw87DIrIIaTyDCRSCAkij/1Y0dCaIhOCIGmh4e1DmPvtEEcgCcOsFDJkWrX/19d8RB66/+HadQkTXlZGHgdxJEAPoLJPRUiS0BI+PW47IKmSkzNPGpF3JAPUQEBKbipOEVMmuO5hA5hCKxBDW5EiSkGEEUf+rNJj6ypwsJpFE6H/32qOsoNieKCzItWv//bFxDEi66//dq1CRNemZEgdxqPhbR4tUpfm0gJoFq8VQ8uZIHYOPAtCYApSAgGl+KkGK3w0VUMlS3FltaeaG7XafwGPEPAUQHzqFPPVbXendaMYq2PPvT7z22dXq3kLwB//uSBOKBAy8zVVHlTpZmp8qqPKjSzDjJVQwlZ9F+GWsphKz6VVf8pfoYv3I+yA7Axw4gjv+hKRhNbDcBrPsnzgdBRaknnFGCgYgQETsIgxoKhuEVYkBohLB27Wnmhu1zcvuRnjP9JaPY2RJl+4yXDTYPmSwGT0ZEmzhs8imsJQTBsmA54LCqqr/zTfocb9zH2QrYqXHyAY/+9AqpR96D6xgwvLRU7e2KPWCAIEJ7iamwxzOwql0Sq0fNHqWI/hxZIqYTHYGuT12bw0SwsvWuw8b9S+zNZtpinMi82wcBsqWNR////vqXGoocgOweCWUEksX/6cQtWzcMoJLSKhpguF6iawQBAhPcTU2GOZ2FUuiVWj5o9SxH8OLJFTCY7A1yeuzeGiWFl612HjfqX2ZrNtMU5kXm2DgNjpY1H///++pcaihyA7B4JZQSSxf/qfUNtjiCGVmGg0NMSYlDxV4aBApQ4uNhUQiKQkZClkoo9fuzsH4quWmAtNOffmevdkpYXj5Yl5zYvmZmdxTpINlxk+DAxLN/ZnL9bsvIyKUDCERYCP/7kgTagALbMVdTDBL0YQYap2UniIs0y1rsMOvRcBmrXYYdenCiwJP//////9PGpWgbvQCBShxcbCohEUhIyFLJRR6/dnYPxVctMBaac+/M9e7JSwvHyxLzmxfMzM7inSQbLjJ8GBiWb+tyqvW9eRkUoGEIiwEOFFgTP//+pSRiVmkn7xqQQYDcM2kjSQukxLUtARX3jN+rLst1q8VxcqNo5SR17u6XD0WrB4Ol595yq6nzNsmqwdXRBJS9UHDgKgtNua/+nv73TrT++4KOlYZjhhQsGv+UgY6WNxnJlAiPAwAF1OpYk+KAgwHYZuihaISgA61S0MNym/Vl2W61eW4v9G1LpI693dLh6LVg8HT595yqmnzNpmrhZdEElL1QpINQhNv//l/v3T9P77gppqGYfBRSu/4hSBSoGHyIpCrxjUlBGSWdSOcB1QAGnaLAjKjSnQrAx2LotNtpfWFBndNLadDO50X4tJlM95WP1K3l9L41iCat/+2LiNITFgtPBGF////i6ktVX4mpNpvgkxjmEYvBuHaArKsQyPWbcsOFRiX/+5IE4IzCk0xWmwwS9FImStNhgl6MXMdQbDEREYYZKk2GIiLFXlHlp24YWxgYAQI5sNBax0ksLAgY5Disk/GUdcfTFDbdHY1hZDw5MqmFQomEpqj//vcfJUip4ixX///4y6kslVXiak2m4gkxjmEYXwXt/NNP6x6x/e0u611jFHTuCbTXjc+zLAN2VQAJKQRIwAxkdmhlzCTSYDdIxSP7EYWmqU3ITSSolSmjsVY1ztRJ8EvG6XoepEhfq2jlM+1//WPlmKODAEzIMEhzm3/6JU03+g2Gz8/jUcUkLiLp9BDfWXQgebYwRJcaKvblD7XVgBggxvUTSQqi8IBKkgmuRxihNKuTBpxZGSiU17XW9KlKRPEEtCUlBNaQFxYbN5rqSA80wAsBwSHObf/elzTbIraDYbPz+NRxSQuItz+Y3/HTHojHqeXRUVBsxJQqCJ01W8BzSLUzag4AARzYQ4DFkQwaF4ix1y1XDdg6DPlH+8LRAOViZXQEsnZf//9wBhoSgkSSDP/+xq9tMjaBRshBJygIKr4Z7/+qR0RQAft3JZEb//uSBOsGQwcx0xsPQfRfaDqXPSg+zJzJRUy869GZIumphhz6d3bZ1gRbbd2Xu4yBUqAg2X8J0JYkYoSMrxqZ4rYVCL1P/5Tg5bovCJhzL+sEMKh4Uhn/3/XtpkbQKNoQSF2AQVXOTf6ojNkZCM5mFqkmtKc9cj6szoS6uPGrSuoAIAAAEnICNk8IUX8A9RwubIxddOHjUl1FZOh65gRsXqnzvLqkDMU8VWoNXxZo2P//9KFmXjmscbMAiRzv/+UouxHaZEV8xhNo5KIHo/y1SMMSfzhf8O/oYxlEoZJhn3nHci//L/oEAACV2CU6lCKSt4EezYvG/kNdoL3Zdg1zMqoyW89WxapDqW8kYfpgDIN9xNEZ51uLg5Sf/59DpUR8EGfDrQoOmHu2v///0VkWZtXyruOFaj2KKOoa1BNSscSY5YQkpOccp5JihGNqc0bq0KoGAACLkDMWvJ0JjjKFrPC0CvytzNoEF+9fNqsTblNrOmB6NFuU5PGxFmOvImPvX//zhX2XIjwsK6cQIjt/pYxVKi701fUszysNOJgOZLkFGv/7kgTkAAKnMtQ5iSr2USn6yiTFPozBCUVHqHoZrhun3YeVuFTf/54+wd6oTTjqPQ1b+Kv9FEbU2DAABFyBmLXk6ExxlC1nhaBX5W5m0CC/evm1WJtyiazpgejRblOTxgRZjryJj5///zhX2XIjwsK6cQIj/9EYxVKi70TfUszysNOJgO7XIKMjub8p95E9kdk2sV6OhPIOPkwWVmEP4APJAAUd2EEYohYmG3R05C9lGeMpKNPjtf8qEPh+bLAp/9Jg8VQKxP/RCsdRrO/3n+o8UtN1JAWD8Unl1mE3mkqH957aT+kaU5Tcv4qmiFexmwunvNlEgAKNBgEuy7hjyEOOA1ToFD7U3mNFLnW40MsPOVgT/1SYPFGgViN/0QRYTUaz21OpcqYccUnKIgELii1OVUJrIyHz85X+dT0nfhZwJTcFRMOHJvVn4Z0AIAAAGBeE/k3HJbkkKycZOmm8FqUdu9lVd8G8hyAHzf5+LdyvXrPGz8WouFt4uyhgDtszobjjvG/v4b3b1NGCZxsDKK6hH/+6Vag9BsfGv+ppIuROfkv/+5IE5wADJTVROw8q9mYJeidh5V7K0S9TRJUW0VYl6zRijnsYNgAEZMDDaECzFau3ky8IIZGM7El4JkAg4IEwIpIASAAAsmUHsdj1TGiqx3m+2WYN52xZanB42O3zJvGc4b12LxNLcdOJxjJFgWN/VzSwVCwgFI2VDG9UVRMPQSZyNe+QagXLOflYwbAwNtCR7IbRq/h8vOIIPsvRYJ5EgcGDCYEpBCBIEojLhm/41YxWNgojI908Oa9ZtFz8RvUjlvzhROlMcR+HcrlsSU4H4BGH8/O0c+QUEhwkEwI8zf/+hGP6t8RLURHCQMv/w0z96pCvFAg997R4YSJuFLDZhWiUDCFrJmj3nDTXrXim/P6rP5YcmUM5VH7asxbAOhAeF5mZo58xjmOKhI85v//Qxj+5re46bUdLDwaX/1hpmO71Z4LkEigQPvJDg0sAAAyiVxQyU4MBGI/G0NrML0u+/kbykneQvbhTkf17EItDBfuhhnrpxSNrtf5WcHFnGVxWaPGuvph/6xxkjyvEuSklQaYUR//////ONSVa/9BodEsW//uSBOqEw6cxz9MPTZBkJjpKPOm0inTJTOwwqdFNmSpNhh06UpSnAAosn//7dZVejN9k31LUZNd4JKEbPL8ABIMorywxkBAcYTYaoqvxHF+4Yl+VB3kL27Vib17XJFGEk7sR7AcUlbOY0o+ECneYJWbHGuwxz/1jjJHlgJlFimBEkNCB9v/+NhJIK9b/+xYMbR5UnTZCVnP+tcmoxmmSpKZCsSqx5dp4AYEAACSOL9Ah/UIZFXSpnT1uVV4fzH3Fo1z6j0hkXu1YPxGhLk22QmQKl4/6KeDgaAvLsEiKv//5jqY/6GiOTGhMfIS4sF5C7f//////Rtp2lXKshO5QBgQBAJI4v0CH9QhkVdKmdUs1Fs6/7t9s4UN/lvCux/7VZnHmi6G54rPC6b8X/+ZzbkxYS4+CRFX//8x1Mf9DRHJjQmPkJcWC8hdv//////RDbbmdZViQrioACAAAACONbS0xIVCz7Shsvzo/OTe9pgreLW80EK1Rac5CWNGJotU+z+0mZlrjJA1FEHgU9///Sgfe/5lDoOYWIZUGMYa7P//9aP/7kgTpgAN6S9KbSBamaYZqY2jm0orZP1tMvOfZYqgraZYeW2XEMiBAAAAjjVdBBiQuEtzyWsvzRytR3alqt0Wt5UAStUDTnIR8hCE0OVPnP1pPHiwPg4qMFhsLT3f//0VBu93+cVGobOGxA5UHGOHy6O///z5FKFHI41aSBAAMKfIEh6ixG9ULOQrmFpstRx2wYhuWzPcIZK3jyHiIzRDTbA/JnNazLbv9f3u9UZDmViFQO8BqSeubn/////3MOW+99fpk+B9PkgYG5Qb0XiF7f//7Rm6wEAAwp8gSHqLEb1Qs5CuYWmy1HHbBiG5bM9whkrePIeIjNENNsD8mc1rMtu/1/e71RkOZWIVA7wGpJ65uf/////cw5b731+mT4H0+SBgblBvReIXt///3eyjj6gElMrNSLhVqTuXmEihyzFLM9cgCxXfqq+8sji5Y6o+za8+0QqSt24MbEoAGGeh7X/m5zCU1+/+7MzjOq4BomXostYZLKpm9G/3/P/+GAhUof5/yoJRDQGzIPoAwYFBzmzYQ/QAkplZqRcKtSdy8wkX/+5IE5AIiazRXUwwq9FWmmtdhh06LOMtW7L1r0WoZat2XrXoOWYpZnrkAWK79VX3lkcXLHVH2bXn2iFSVu3BjYlAAwz0Pa/83OYSmv3/3ZmcZ1XANEy9FlrDJY1M3o3+/5//wwEKlD/P+QglENAbMg+gDBgUHObNhD9AASgAgEIcZfBI0sIQpBzi4ulSdMV6+qnWqVN2oxLWEpFkDc9toC2Io4iQkVMO2g+ZmZZlgdlMCALR+NyIo+fmVYzoUr1tTztE/5uqZgdoZgKsKWYxX//+tDXH3n5ljIqCOMswA/MYDIfgWsFS0I17qsasOsVZft05b1YfyzJJD8SWzUhZcS6qySNSLvG//7we8MpUtR9E1OJBoBUbzv9WToyvm5cfSJ/vY6jmAbQkwDpnUeO///5UPT4jeDBS0HBYAAUAAMAkYBEkhgHOTk4GrT1U5rmuYUSLlSuTOPPGINmq00FFQFQzseb6rfP/8Ctqt5nmcuWAupfS4mcwJVyXlAPgYGC1CdEuCJt/FDd3McO19/7uZcchAmUSn5bb1urcc/o9IAEAA//uSBPcA4xs0VIsCTwRjhoqRYEngi/DRXUexDdGCmesM9BuKAMAkYBEkhgHOTk4GrT1xzXNcwokXKlcmceeMQbNVpoKKgKhnY831W+f/4FbVbzPM5csBdS+lxM5gSrkvKAfAwMFqE6JcETb+KG7uY4dr7/3cy45CBMolPy23rdW45/R6QAAREABAADANByWUJUW242MVfTWDmRATAKjLlZ3qouarVyNzZvPzM5VJ4qTktaX0JU2tLdl60zj5QsgXOryvVhJjqR19WkWJYyesUAHBR0kHqo9PDrnV6dkPMQbJusL6AABEQAEAAMA0HJZQlRbPWYw7X2WXuYSojKp+O2XqouarVyNzZvPzM5VJ4qTktaX0JU2tLdl60zj5QsgXOryvVhJjqR19WkWJYyesUAHBR0kHqo9PDrnV6dkPMQbJuipfRQAXlCAgF3X2xrDM7Ycj9nFrKl9bZhikNh71y3OoBJL0+njzM2gcKJxUUSBGEtmYiByIC4zahLMJy2Xo3z06RlJIP1n2zppBDozMk4l3RnqkcxaYFkPU58o95dS0zf/7kgTygAMfNNUh72NyY6aaqD3sbsv4012mDYsBgZprtMMwuL2+cyACqoQEAvE+2NYZnbDn7OLWVL62zDFIbD3rludQCSXz6ePMzaBwonFRRIEYS2ZiIHIgLjNqEswnLZejfPTpGUkg/WfbOmkEOjMyTiXdGeqRzFpgWQ9Tnyj3l1LTNpb5zKAYcbAg13UqVc9ZW10HeUKx0uDLt75YIuEi2xo1JJvv//VKvjncTwiOZy4iHuWjMiVyX1SwD1UzuskO7MyKuMn8OU9WVnvIpu7fTOmtCCaH9LFsmruGI1NZ+My2SpAMONgQa7qVKuesra6DvKFY6XBlz98sEXCRbY0akk33r/6pV8c7ieERzOXEQ9y0ZkSuS+qWAeqmd1kh3ZmRVxk/hynqys95FN3b6Z01oQTQ/oMWyau4YjU1n4zLZKkCPkEAYZgn4K0gu2X8OduS0tnNrcM7s+ucKrNVpZLv//0zMalWqvxiJwkpdTsiqeWjOh6jytWmVTBDqXRnany4TjQlGtgUBJy3CODMiHUdR+Gkoj9JAVllG/2ZTNnugh7/+5IE7YMzAzlW2YZi4l/HKtswzE5LpOVah43ngXYcq1DxvPBIAQqRAQCiMd5B3mI66/7VXo7neO+q7R91NaArSKnh6z//6ZmNSrVX4xE4SUup2RVPLRnQ9R5WrTKpgh1LoztT5cJxoSjWwKAk5bhHBmRDqOo/DSUR+pwy1S7j6ximM/WpJdJABhaCRgQLhjTqBNE6G+OS/l6rpeUKp/h/7OJ4tlyyP/81DQaUGQRM8uCyp2EJCaBkIa6PB6bvKBMT+RmQohL5aYr1jF5aanIZ6kH8jCRe5YKgiE1PZ3nEr1UVXhqwAMLQSMCBcMadQJonQ3xhL+Xqul5Qqn+H/s4niLLlkf/5qGg0oMgiZ5cFlTsISE0DIQ10eD03eUCYn8jMhRCXy0xXrGLy01OQz1IP5GEi9ywVBEJqezvOJXqoqvDVlQALS0CYAACRRdwNNgs805YIRHk1F/GPM1b3tqSOa6Vv/kk0mA8FxNVIhx2sERjQ1fgRGZGQkFbJKHFY2cKmZd6y9InRFoRS+FTJ7GSTguiSeCUSVtkrtnI7S0yhXATa//uSBO8BEwU51kFvZjBipzrLMM9cC6zZXYS9mgl2myuwl7NBgALC0iYAACRRdwNNgs81lghEeTUXbEgvM1b3tqSOa6Vv/kk0mA8FxNVIhx2sERjQ1fgRGZGQkFbJKHFY2cKmZd6y9InRFoRS+FTJ7GSTguiSeCUSVtkrtnI7S0yhXATagDkAAAAKonGCFCOYHGQ9drCuUddwo1N8IyuSDE4ftWnFtw+DIIm9qt//9pg6HB8AoobOuSc4aJx24ojc4MlobUN6fbFhOpX0l/1U+iJgkEXhjqOSfd+P/XrTTLv/Qw2PQ9AB6oBAAJ4bN0gSuI2g4bg+ZwpsyHorVghVPa2M8NmRSDZtJJ9//3ZU88hQNp5TsONu9tPjjEfVbDfP19Z2V+W/7KPpUoMkDbiC3LNJ7vy7qXlqpl3/mgoJliQ2aQpZmXUAWQAQACaIDWIP5SFdR88Iee2xemHuNxsYt2azqT3g9iswzqkgT4gP3u//6Gg1OLAYHMhAIxyAtDkbWVVNads7xPKJzRX+5VLszOhzSIySQFAier/5w6PexpoWk//7kgTugAL6OVbhD2aAYAcq3CHs0Aw0809HpM3Rfp5qqPGlKt7A8AgAE0QGpoP5SFdR88Iee2xemK43Gxi3ZrOoXvB7FZhnVJAnxAfvd//0NBqcWAwOZCARjkBaHI2sqqa07Z3ieUTmiv9yqXZmLQ5pEZJICgRPV/8MHY8QoixgzLPAGpDIZcIjvCSzdBnjs2p3CWZw9GFGrMMHxmyTqahDdw4EUbw6O1bdX/74OmQDkRCwvectJX4dMMKCEr5H1KrF1HZD3BKZf5xRcZpwGRDv/+upaWlHAITuJGTIA1IZDLhEd4SWboNEdm1O4SzOHowpKyBg+M2SdTUIbuHAijeHR2rbq//fB0yAciIWF7zlpK/DphhQQlfI+pVYuo7Ie4JTL/OKLjNJAZEO//66jiVRobW5A0yqACaoQAkjCMMSI1kbwao4qe8mrZ3+na912s1c2xsrPh1idKb96LKLwVWAYBFeezk7MzeHyL4qDgBk32Q0LTLJ1klokWPM4rZvVVogwsfTYnHC87BkCu//qc7JHyJMOpD4SLAvCAAAofxB4bb/+5IE7QMy7DRTUw9B9FzGimdh6D6LTN9NDCxt0WYb6aGFjbrAkSYdspaIQ7ZolT385XT2YtfWmbLTmzITNFX494CsoowxVAqE7Hi/H/+YbXYhtxZhPCQSz+7BCkrNCg0UYUOiQxxHC2b1VSCCA45NiccLzsGQK7/+oa4VYLCY4C4dvOpAAAAogwYIgrEhmc4iYEa6y/4cZvG6fjiWKQFA/Z5T/FvJurkOZ6QJ2xCFfFPwzCXpuz+9933/SbVUeQA3IqgZFYyVSm60/pH/P7I3NaliRmyvJqcvKsC1wOSWlnxXZ//6iDIYvFBwcqQAgAAEjCgIAw7Jc51Blu4s88sgent8TKOMDpdfpXBMf1xk/lPYPF9TggDujte8zf5mndU3FBUdFDBQUoTXp8V+/ry1tRwpaj6UWVHpnDDyJVFrGn4Vz3//KIPChMAMDoQCwcH1BwCAUhxfXyiQ0kxmbYEQlDVHP1ALhNdO8N8S+U1R2H69jH5giYokLcIFnQ4yD5Lf/vl6OtkyKZbT/r/s8Pr7OZufxjShmuxmeCydPoIcrTLZ//uSBPSAAw0v1FMsQvRppfpHaeNujRzPSm08zdGNGmodliF6Qmj//VJwIsUACHC7HlwAASMJGOBjUCVJrvJdGRg7CLyhe5NHIdsyvUj1HbbY4s/6v42cWP5QkBIsIAG0oFnQ4yD5Ld5lfhT8tNwFsxLaHjO5+/zPD6+zmbn8Y0oZrsZngsnT6CHKND8a93/+XOj1bATaVGF1ABAAAClBxDgDNiAaMYG8aiqNtsRmHPyeD8Wa3VySfPm95oFl2bbAnbz0bP56wmYrMKe8Sd/lVHhnmLoLjTx6fqx7AzDwMBP4JzP4GNf/8WIe3sKmQQACUOK9QiDOAKNoDGXBAytbYjKJftl2GuIy+swwlHlxNm7psfJMVacLbHc4i4x3dYQIxhFhUWe8df0VSHR93UZR0x68psw0Dlj0jlv//CRpdUW4qVAaACAAAEljjFjTx9EpR8q7jaDcDdvy1dv2vR2pzocDYb7ivObFbKooXs3w1DGSLCliNT/cZz1kwhtLjRBA3Gl4+b/NeNblu2TEt+lWe8c8gq3whnIYUsNiT///h9NylP/7kgTngEL2M9a7DDJ0Z2Zqk2WGmIqE42FHiHhRWZhrXYeVOic50DQQAAEEYUqUy274O2eMl3dsRmtO+vBSW13GPR+vI+AOhcDnRp7LZ/RrnhAFLHOAKD/DOhF1iLyvjvrsc6vQiFdrebEGG40vH+/zTGbDAZZkyhjfpVnvHPIZb5GeMbVJ///hlSUQXU/QBFymIvz4lmeNDCZRBOQ1X/sfsN45mY+Xks3E/fhJR5sS3AVbaf7eW0xygJsxC5s5+uZvO/thYnQ9K3u+3McOLLH+MfTJ40fG2B1yU4miqDCCMlMUQInEG9tymIvz4lmeNDCZRBOQ1X/sfsN45mY+Xks3E/fhJR5sS3AVbaf7eW0xygJsxC5s5+uZvO/thYnQ9K3u+3NZ6edf7Vvfctqa+r0W95OtHiZLMQ+TqUASrQ1Lb//7a6EXemq4vlVCgMCMVgMHcLeDAN/ZRAF0gltFjTw2oidQs81UJ8KYSwQ0Y5utydJAZIWo9QvU2e5PmQv0iKrp/OLxmIEnWKGwwwijiWtc3//jXOylLMyePVlUgXZxYUb/+5IE7gRTDjZW0w8ydGcmqqdl5l6LINFWLDxrwX2aqsGHmXrJuGvDlziACSRxqAXouI9QIF4mWL5pcJj0iqT9cUOWr1bw5DQYCKO5zAyVD0OkMQY0M6odYm2b+PliEy1eqQIwYUcdX/yj01YpxyscFBFdgYEFVSWpQZ13//9j5PtKpbQCEOM6RGmSiNLmrxKQUkHQVKokjTR5OvR7Qm9JpNZUrAPiRfcGxEx025MKxFb6rz3OsFuOAlT1udzzlECCY9HaSu1a0LOibHE5B0h5rOcUEQ0eEQX///n94DJt0Q7YAAQhxnSI0yURpc1eJSCkg6arRJGmjydej2hN6TSaypWAfEi+4NiJjptyYViK31XnudctxwEqetzueeTIPW25479p5/LykXtm/yz/CvF+vm2gaHXAlP///qliKo8Xsco0agAACMJt9hK73mJpMpmA9WALc1TOEFko3eA7qwzQnSiWoAllkq7uIq7lSeC7IipzUSbJFmQ0UsVgZg1pCSZqGwvEX/x9jazG33D7D/cuntGYLFAMGYLIU///9fKtR1Vg//uSBOwGwwE0VINPMvJaBnrjZYNei7jNWuw8q9GDmetNh5l6RH2lB82pmzJF5YAbS3SfiUaWG1QUYtsDlVSzNywdSWgA1kaNOOqQS9xOSaJMVEUlTw92SLMaILcHiBIBZNQpGYIbBeBLtmPSG1GNu3E6Zf1V0WeiRIAZTLqN7kUu6UPAUDqllGjBSc+iqSWMas3K878VjKGq9eSPB2yxLxwMrQTUyyMl2P0TqOfJ4uaNojcUqp2BJg5lGfxSqY+3jl/Wt62+MyyrDU+cWxYZDseQowZgYlBcs/yhka3vNSweAoHVLKNGCk59FUksY1ZuV534rGUNV68keDtliXjgZWgmplkZLsfonUc+Txc0bRG4pVTsCTBzKM/ilUx9vHL+tb1t8ZllWGp84tZYZDseQowZgYlE5Z/qAYRPtfqhlK4AAg0B/CwCMYTUEOD9Vay9EmYkNBuzHYRZdepL69l+qkZjkcgxhDhwALGi9iYHiT0d7KklekXr74jPFTkTkT0eZeTxME/D9S79n8R+x1//0CEHEEMY31LU5hdMqC4cFNn/YP/7kgTujuLzLdUbDzL0XuZqgWHmXsu0vVRtPG3Rf5eqjaeNurIkTtqwACDQH8LAIxhNQQ4P1VrL0SZiQ0G7MdhFl16kvr2X6qRmORyDGEOHAAsaL2JgeJPR3sqSV6RevviM8VPE5E9HmXk8TBPw/Uu/Z/Efsdf/+EIOIIYxvqWpzC6NKguHBTV/Qa+a2jQ2AAJL4Wn/IxRo4OavEaW5zs9vO9upm/taktZa3O/Kn1XmqggnzhBRCOWU6bXnY+x4dDUUE4+HADp6F5CEAdKHd8mZlv3UoUHR/56qerMc5yiIr//+p3dJ/Hqx6oLuMUg4bVv7ydRPMTF4nKPC5GN0Yzc52e3Xot1M39rUlqtczhPvstEVBICEGcTIUKUHGcKTRTWtOLnqAtCwHiLISY4hyA3TJE7I0LYPyhz3k+P2/dShQdH/nqp6sxznKIiv//51PfoyCx2Ej4IkPvbVAGVAAAFDiGPc8EQGm+ZXKfw5Vndr8a/7ZjDqpDkknKQVcOR2tFbAgdHfkNkWGBfLXLEx8aTXIRSnt7OvCCBuv6zz8MTlaD3/+5IE8ADjNDLTG08tNGVGamNp5aaMkR9W7DC00Z+g6c2XlpqbiRis/ckRIuoFCQPn///5hxV7hxRiTJxImAGEAAAFDCGGuXgugEurlatO+10q2qY4vPMSOlopyOJJLgLcCx2YiW4OCJmOYmLvHRAG8UKcrE42HpDTmJNbj3751Fix/OnIpv9ObtpxMJbZMyWgw2PiYdpBALDdw////jUWbOlIA9IQKxKRAwYwdtFz2aisdDX1yc8xYknbieViuB8SbtRXe3eZXEsezY+A4TEy0leRKn6Q8eYZVlfm8+nw4sdaXrypVxDJeHBIJq0yNBoOyIViIZFoQDw8BKG//+7dntVYAbIAjjA1CEpBxSirPL9eRgSqIOnQNZYjSnduJ5WK4BxBbXRPvbvJTIcxWbGQDBETHIleRFp+kPFzByflfl+WY9vFjrR+vOK0hO8WHB7VcoLjahMaLlRg5az//+maKyaZcVtSAATzYDCRrJIFLRLBNC1EirS4mYyvHFlxkjqA5EgtHDv/Oe2dPtg0PhKIYliMDdyh+Sh/Rz9EqyrkG3q2//uSBOKDIwYzVtHsSmRk5oq6PGxUjADNXwWZhdGNGeuowbFappdxDn7c7RVDxFW4Xzhto8VnTU3gdIAAnmwGEjWSQKWiWCaFqJFWlxMxleOLLjJHUByJBaOHf+c9s6fbBofCUQxLEYG7lD8lD+jn6JVlXINvVtTS7iWftztFUPEVbhfOG2jxWdNTeB0gABzRApkXnrJZ6f0vSb4eXy0rvMkswiCsCUFyEZC48QhxWtyOW9xOsuxvqSSEmn65ICj3M42bbJZ/sIN4SCIEQvTDQeWYcRORQiODRENSRixMKokKphzqZ3gAHNECmReeslnp/S9Jvh5fLSu8ySzCIKwJQXIRkLjxCHFa3I5b3E6y7G+pJISafrkgKPczjZtsln+wg3hIIgRC9MNB5ZhxE5FCI4NEQ1JGLEwqiQqmHOpnfQQFqUBR4FyNlWJqTsSHbzLr7697qxRD/VZY3yFC2JI51ejFC4Y8B/RMOm1SMqSJkc49CIMWZVRT/jo1DcrYPontJikTnpMOi80cJSCZsookSEoGIAw0MiINEcJJttvUCAtSgP/7kgTdAzKTM9igw2HSUkZ7FBhsOksgz16EvThBZBnr0JenCKPAuRsqxNSdiQ7eZdffXvdWKIf6rLG+QoWxJHOr0YoXDHgP6Jh02qRlSRMjnHoRBizKqKf8dGoblbB9E9pMUid0mHReaOEpBM2UUSJCUDEAYaGREGiOEk223qABVaQCq47yLU5JsJ3Sjc5O6yjaUJbdTpBgeo5qP03zsgsKsPnefj3hFso6KBTqMfpez6LEF8yl8YNnfEYZrl5eR16HAe13i7C3RXo9RpTpOiVAkKR5bPyYOWMJaWldYAExEgElgjg9Y1hpg2YIPdqyiDxiNd0WkFIyo5qP0n5gQWFWHzvPx7txBIjogCLQsYpezaH8CaZSUKSOXeIwuNy8fkc7KaEe1h4gwnbhvoqfSlZOiNgFByHLZ+OgdYwXaW8+KgCnUQBh0e2i2iNvnnJq9j0Fsn/2corGpoBawUg1AaEQfCOXFi3qbjvkoHgZo3lBIPTsFnahfGmTwlTO5zuz8QznZ0VgkwIER2QoJgcWtpkZaXmEoo2wCQXK/rMSViAU6iD/+5AE8IMy9zRXIM9MwF6miuQZ6ZgL4NNchL2TAY2aK1CHsmEMOj20W0Rt885NXsegtk/+zlFY1NALWCkGoDQiD4Ry4sW9Tcd8lA8DNG8oJB6dgs7UL40yeEGZ3c7s/EM52dFYJMCBEdkKCYHFraZGWl5hKKNsAkFyv6zElYgAKokAEcCOtxNWHPDA4L3Riu9n7kP5TwPGIFoyughTEVmo/n53MiP2uJZcP15dRwl0vrX2z6aGRK6q6OInZLQT4dGCmBzDT/CCTHkSQWE2iRDv//8LLYtx95VS60AAKokAEcCOtxNWHPDA4L3Riu9n7kP5TwPGIFoyughTEVmo/n53MiP2uJZcP15dRwl0vrX2z6aGRK6q6OInZLQT4dGCmBzDT/CCTHkSQWE2iRDv//6HrYs62VSZWtEAdQAAACOG50MMDOU7M4xporOofqLWLF897Tx8+MnEg1qIwz/lJ6pEajNn4f/7NCRTHwkQpkSYWmFBaBECj2NHH9NRktU5W5JZpcaaEgKmNN7U6nQSCR9///6CQSKnxgcYkNkVgkANIAD/+5IE7wIi6zTWoY9OgF1GmtQx6dALoM9fozDTEXUZ6/RmGmIABHEKYfYR8x2Z1Gmi3i+rjWLXz3tPHz7KRcSqo90/KX1SJVts/n/+2hIpj4SQprJj0woegRAo9opX7VEtU5W5JZpcaWEgKmLntTqeEpI///6ADJtDJxJUWZKgByAAAU9xdrrtB2lC0Umg08i+rb5HpD0cVnNosbYPPDhfEDQSUInhMHxkzHeb3mZmzJytgsdmJynJjSQyZpMzjdVizCg5+zMDVowUwQpgAoVGsjAQEf5pD0RyC61oi0LkzIpPjCguGAOQAAESIAICT/F261kOMx9TZ7bc99LL/oJ2zP0SxJODE1EGiCeAgKUicLCZEQvb6Of/+2rGtXQqrkixRFGv/5bFVbKmqdr/mM4NjJCuhjADIyjKkwoM//8aUfiOKaT4rTcPuXydR/sf0z5FABgAAAlbCCY8OcSScQ8VhuLh5Z2z6+uGPQIPsV1c6lHbUxgFIC2m8L4iCKM2tP7DwkIHHuHcFB7sIprVx18fbQrWW03xVHv3/MjVGQwf7P5l//uSBPOAAyszVlHpNZReBnraPSayjTTXWUwwbdGiH2u1hIn7RRuI0BWTZUNuQPGlbiLEgwAAJWwgmPDnEknEPFYbi4eWdte9cPegQfYrq51KO2l4wCkBbTeF8RBFGbWn9h4SEDj3DuCg92EU1q46/+2hWsuJviqPf/5kaoxGD9X/YNmxQnat45FtaWwlUoAARoAAkJP8ayWM9loMtL84FyQEOKrKCeai3R12W0m8aVRIIsSTKZCXhP/cKdVGpkCUp//8I1qPeKnyoaV0hMzMBMJFDiHfxpYNy4HkTVqqiAJTNT7EOADUDCmLbKsixzhP3XZrapJTzeVSL5ggC4DKoaPFBaAUJAQANCCQGIyK8X/35uvMU6cARqm///8I1qPZFT5UNK6QmZmAmEihxDvyiEmVFyNJWTFCCu1Af2PVADUAAAlXCOVApO5Kh5Q7DrvynGpWzCiwpIzNM1vYtywPJ4L1QrUZSMJ3CHK5nYVGtNUaN5yVEIKZw3MGkQMU2v8Ufbt/sq2GzV/aGZI0bfA667mg5/raha6EvZpChgrQSE8UOf/7kgTngDMBM9VTDEH0WyZqp2GIPorQ0V2sJGnRYhoqoZMN8mBEAegAASlsJioILv6zMoTGYfKtaNXadXITx90LNbh4s/Vp6qskOR6AMdEzQscBJV/5c4Z0i+gaYUUaNn/9C/X3f/W07q3vpiDGgZe7AxLP35v9aiQ8VegNxQUJvPUXoNqIhDuACkAAEl7DuApAvyYGlIg2MO3rqGXJwkr7dBI038SgmTHw6GyowBouGxIlfxGMQlLUcS5oqHg+K/jryYomaPeK/y56lOZtKdxo4WiL7ua//+bKUk4mSfGGgdtbYgfFBG5YnmQAgAAAJVot0gW8RogAO8dBVNz9xuTD0NLJe1EwhFpX8SEwhjsRw3EcujQDp+MilTvns3MgFi0VG6jJBbZ/2aM0jEiS8Hnxn/YWXqBpnt0yGXbqSZiR/rEw2t5lKEhEauhVK7D+8koHAAACtD7uicv69BCUOixZlikqV/JiWtXmH25bt1kykpnzxmfqVOo1uKc50IamZari2+xcZRONgVHYiKeEGtv/tAZFq12yvGb8/aJwvcNYIiL/+5IE8QADLjRUUy9C5GKGiqphhj6MUPdVTKUH0ZaaKemWGToKGP2dYSGuQwgQUl0iEM0+1yyAwAKQAAAVsJXETPheRGZO+07jNrK+DIa1Yy37rUoabnmHrpTk1gF5meQPI65//UIxaGgacUFUSHP+AxMh2EehHp8QSQMyCwRIgIQf8CmiRYYLOcwSDhgoxBusGUFyxhUaAPKAAJT3Fi2GgfNpBN+sqhT8c8y7FpdvMfNPafaJBjYFiQpACj5/1SU5yALhEuw1DO+1ktBsQIDADHKUpLtQjVsEMFL//9Ub9dNXWSdao7LZZT+wgwoEnqAAVSRABSc/ABgRGk/AS+CgF6UHxjuIZ0Rama2XruamTBZJyDE//qRmrugcjyI7odw139axNUNpCBoozqpqbVwlROhDEm/+0bcuwwPDQHL716Iwk1MAAA2h9nxOOYaYDToOxOepN1aSa3QcxgDPXMvzrVJTT2IOl/xSAorSTrIMwnAOHYgqn8i8iEJQWmyWLjVS8Yso63+h55+w0dIcuqxKzas5bjb+gp///q63CLdCMf+y//uSBOeAAyQ0UzsvMnRfZpqaZYNOixVHV0wYSdlRmmu0F6AS0uB84JzdRB93mO0DYAkpbiVUJ/FFBGUTFJ2/iuE1s54lPWS81lEKOB5vsFmYOBTnSddO//7R2sdDLNri41UvGLKOt/oeefsNHSHJ5oqyrLKc4hnhl/4Ov/////1ie0tB0wnvz8dVGKS8KhFUpykGQnNap/EJAVolixDkJ0txAVS+j4YBjeG/1y2KZXK+o+ZCeZHjowNgwIygDnEEA6QiTq/+2VcIUOyLEx9JZG319/g2pA4UP0I3+UyijPVhrghz/iU0PIlraDrqjYceFoAbkEVAE0gAAS9hYcg6EWZigrWrqlorhP0yJwzKcrspuquZaTOHRIXUI0mDA8dFlfZY6a6FCRdDj3n9G1MGhol0Mb/NHipBnqxUuGDL///Ui5lWVvr2A4M+JTMI9zCf/jmNqgADE0QAUkvxdtlJqZgFLSYJcLqSESY9zO7hqbN9wo4joUakuygHn///ZsxBJImjdnxUr13TKV0MscZUYEPb1H/Kc4gUjUJWLwQf6RetQP/7kgTuAgNJPtKbSBXGaOn6l2EoXsusx0ztJK3Rdx6qaZSc+zDRIgHpIqDOOFh7HjEBguEwAhAAAAFokrrk7p/gIIdl3IpKtYnxk+nC9qfe4EVjpVjnkXmYjz8cDRH2xpghbqu9f/sylBCQ6527ip390yldDLSiowIe3qP+U9ECkahKxYx3++wJUICSTkYTGZEjIoooaAAISSAAADuG8ZYpFVCtPPb4pT6b/lC9bUbr34NPSMSIlRwgJCTsQz/sr7LM+eZJyzZjV8sm+fIiGhr+X2y0JiCBC2Ajx4k/1aYsKoCy4vngWBYWDyjJp4AAgJABGDeEvBaQTJSxD11GNrD/zofbi2p8QHNnCRYRFQgIBQJKYhn/Sdmqyq87MUcUqhCpVi5PebjCCFQhP5OoY2SRhAUEE0g8jE4kb///zxMyWC6nH0U1AAVTRIAAAHGCaNAq10rKMa1kaFZ7Pc0or0C6JI60sOFROWDw4RIRAAQcTMQ84yKDIrIQBj8ycnTGzT9QbDAumTjhtRmBcnH5vc2rVEa9l3KFx5M8sJCfXk///8H/+5IE5gAi9DJW6wsx9GEmOmpp5k6K8MtZp6TJ0WEZarTzJLrLnEFmWmEsDwABiQAAAAA4cVRIQ9LYymkpS714y3Pm1H020ELx0RCqW0xSLB0ejgCBo2eUm9Wkg8mZyBM3uTyehldVH55HThLTlsiK2FtE5bN18cT6XMK6/S+4sL4/NomgDBLcuPv//////7+1UsV06EvhqZAAEtGgiBRYnp0X0tOdsjIadzZrNy8f9Tw1yc4915rIXFWScGRHBf7JIwfBVAWPWdLCIPgIinJYk0MNTKgvKWmhG2PlDjfmqQ4iISi7yEiJ5MPPjjHeAAS0aCIFFienRfS052yMhp3Nms3Lx/1PDXJzj3XmshcVZJwZEcF/skjB8FUBY9Z0sIg+AiKcliTQw1MqC8paaEbY+UON+apDiIhKLvISInkw8+OMd6oAFFlCIQAAAKUBSJuElsaa07zPDV8fc1NEUTVylJ+5ud13FRJ/l5ZVU9zXeIUWAh1V8vbOUiLLvDY7vW+M/BQMETcQgHjiZnGTC5XEc1RRMhcdIVmyOLQJIyZd7ugA//uSBO0DMyEzV2jGSFRsidrdJYecyqjNYcM9MkFVGaw4Z6ZIGkGIiAAABSgKRNwktpNad5nZq+PuWTRFBaHH6SNbc7ot6iS/k1Vqqe1rbEKLAQ6KmSTs5DDfJHDT92Fdvn4KAQRE8QYA44mZcyOLlcRo0IYbIXHRSsTiOJoEkZM82i0gqxokADi5SUMatIZkxcmHz4RXgeUMPsSny4DtUUN2YKGnRZ7Z87e+cJxg0iQFh8RhgfEZYjESSNFJZ5ewZPACQE0lCbB0vtomGk6lBdhxpg0sibPIomv//80HhWi/a80OILtaJAA4uUlDGJLIYijFyYfPhFeB5Qw+xKfLgO1RQ3ZgoadFntnzt75wnGDSJAWHxGGB8RliMRJI0Ulnl7Bk8AJATSUJsHS+2iYaTqUF2HGmDSyJs8iDbv//8BuajuFUpM0AOYoAFkwxHRoBOVCr92xN+zz72HsOd+gB5TPU8fqvRku2R//jp9Mw8YrPJRKWl8mDkvvATlwUl4xPjce0A5UwHDI1H5SPTl6FfcelPoBdodpi2MCOxYxOgnQAc//7kgTuAiLlMlhwz00yYGZa/hnppgwgz2OjPTKRhpmsdGemUhQALJhiOjQCcqFX7tib9nn3sPYc79ADymep4/VejJdsj//HT6Zh4xWeSiUtL5MHJfeAnLgpLxifG49oBypgOGRqPykenL0K+49KfQC7Q7TFsYEdixidBOgAVWgAAMFEj6FVyI2S6On7C5/QVncdUcfn3Wl43kEjRbjhfoSrS0h57FsyZvLC55149O4RrOSzVJeOUNRCZIJSb8zZjSl5VBtLM9c5o9Rml/fUsxerge8W///+7gFS61gAqtAAAYKJH0KrkRsl0y+wuf0FduOqOPz7rS8byCRotxwv0JVpaQ89i2ZM3lhc868encI1nJZqkvHKGohMkEpN+ZsxpS8qg2lmeuc0eozS/vqWYvVwPeLf//9FHGqTi6oAJQAAAAMEgQQBgOAwEbHjOp9QPuj3Met6PFtbfMUaG+XI4CfujTMwzEOZFDlyQ9OPiSUNBrIiohJxUgOhlxElHxoVkpCuZVcVAlWjKqBCTWMirUdjzxFkhyZxIkP///lQkXDkHzr/+5IE7QIizzLXIM9kolnmWuQZ7JRL/M9bpL2SUX4Z63SXskqCrA0MrABMaIAAAKHDYnBwMi4cY8Z1PEbMTvHuY9b0eR49oVs2bUIU8yvXl59JDHQVf/jnJeSRqZKCxuHNn7E0TbU+GhL9F4Qk1jIq1HY88RZIEgnFQPgfF1//////5rIp7mKzurpY2prMUQACYkAAACiOLqYCfNUWQJ1dnHZzL+S+DDVbAsvocKBDeKxXsenjo00saDe+YHuFEBSdrvWh4gcQF1AgePW3sJKq02XY63gseYCHITUFs18mmYh///8v13kFIigAagADCnaUYWq0iIhF2PuvlPuGy+DDVbAsvm9iYG9WGgaafmUCaDVk2EISaHH4rbSrhk2/iez7pB9BwWHAfkgKA2LZevjoqVnj6n4iZST0IhNknh8XPIM///+7Sq/ZADkAAFFDihgMWFGwruldB7yihcISBQmGpJIuTaH0JOJnkPQkYiGEZIpKizJuuvn36k4PhBlT1qP/9JeqccVunErzbCyCFdUTXAS5QtMmq///0LEeLPe8AJAA//uSBPIAI1Yz1NHpNbRmqnrdPMe4i1DTX6eU1tF4Girph6FyACEOI63MOShsE1xlmDUy4sbNPKh8hfAdaZeJ56Zi545k9JY9DwfkxY0pZg26hfXp+pOFifPqo//0l6qabeP15thZCrqlrgDcoPnJNz3//7B41TSg0ilKFDgAmQBLSn4hasEOhQVp9qM6aS52kjBWYpy0W3vwbBRBSG+EVI4i8//tJ1s5GFFC+i1F13b/3v/7y9dp7ogPWcjaBua66DEHg//tOoZvsd6vqBQAES9x74hyGcmTEuWlB5ZDJrraiULZjrOkq23cADOEQoFiSfhFSOIvP/7SdbORhRQvotRdd2/97/+8vXae6ID1nI2gbmusGHFwbf/Wi5fUwNGo4sy+QYwYAAAEgRcBiQBgTBDTeMy4KQq2lTU3ZnrK6XCqYIjpHvRTSwnayyKt+GreAPZIRaTFFoFyJQaDaoUslz8j/JlTIGRCrZGGzkiUopX9MdReL0UpdHA0uSJQz22MML4zq21KX/FSN/+hYROEaS4aBgXSx+QsB+uLMAAAEgT8C//7kgTsAAK2NVdTCUH0XAa6ymWIPooAy2FC4MCRZJkrHYSY+jgLpAA55UomqAsKXdW7M9ezLhaeRHS3BGiZJ2vcM78Td4D7LqOk8R4EKL4dEqpWk2nJ/kypkMiFWyMnOSQlFK/pjqLx2UpdHA1MkShntsYYnkdl6lL/kSN/+hIq4O6zpRaU9ip+vUADqABKT/EgnAs7CpGZkvfeYqB93lM+PY2eUtJDjILE7yM3CazlJ5X/jdcovxeJQUE8mbVZVDFCBqYr1Q6DYPqCbijk4IynP////kNmDAAW+rlT1kongaRfEEXlDfpXblZBtEFDlAq9gK+GDOpTyu3uzh3943Jb3HcOWqezUjVZycX5UOceFtbb9ARiIIjEzyVe4p1hQj7czMGkEPkpWVUUqNYGzitmetZUNQo7HR2Sn//ohjCDbu2MdIbsV7cjPO/yNtoAAlWiRkChNUDBm6eMUlf/XUmM1rjUSB9qVKO2+IXRGmAKtlRRkHSstznJEgbi/+3eAaTTjYknjy13++Kik1VEmR4nTHMmFvq9Rs80YBAZEiGnfvD/+5IE/QDjrjNRG09KdGymajNl6U6MHRtZR6Rr2YsfqY2EitN08Bhz1uccLIEZUowKkFj+msPAAJK2E+k4NKnkD7dJg/+K1kxmtfqlPttaJ5dIhlSBXxVMoFaCiQ9CJb1UglKJBEhQ+w0qHPrZjHaxSNYsikKj7uWdRQYNKVW//x40l2FRT8g76WHm0bntXM+71ugN1gACStwmSgMq00CsJ5qM7tJqZZex41J1kZS0x4wHhDCRBcOhEktVr/9KUuYSoGnzRsS5CLJ99o8y0vFbyXpRgoa+iZBQKRv/+TmvIk9u+1GcI4Zkox0ZQoUgUdAdgAsiAAACuBnh8CZQmh5FhPNWntJrU1b7+65bJq1fM7UdynRERXocsSruDBm/+qhAIG4JCgN6SyuETos80e068LffQhAgY9CyAYKUUJ//0LVENp6XezKmhxjSIqpKNNZylRHdAAUKIAIJKGDoqBFlTrvuWbdg2qA3OtekWrDSziYKRGMErQOZv/+1yjLmLlGyx0P2Y40iazf72a3ac15KJWRpYTlVL4ieK///4uLER6hr//uSBOkAAyoz0xsPMnRdZ4qXYeU+y+E7V0egUdmIJ6oo8ZcbjxgS1igAChZABBJQwdFQIsjXR1Cj1cvHLAP23r0i1YaWcTBSIxglaBzN//2uUTXMXKNuOh+zHGkTWb/ezW7TmvJRKyNLCcqpfEanf//6xdiZBpYqNRCSAC1AAABVoLoTAfSRBzQ12zttYrLuu8w6x//4ys8CZ/MdhzmyuFWn2MComY5n/UwmwmJiCONRM5pt3/7w1a8uO1yB7NpNA/dLSSkzf0YWdf3f//4SyWZAw7yn1uvm/RLemCDWLTnQQC5AAABVoLoTAfSRBzQ12zttasu23eYdY//8ZWeBM/mOw5zNXCrT7GBUTMcz/qQJsJiYgjjUTOabd/+8NWvLjtcgezcmgfulpJSZv6MLObd8ODXuMiaei4uEgGgSC0kGjhZA5qiqACVSRAIKT2BESBNCHCYVmC58hN3W6+mJwpilm/Tk0mBypTSQSzo/93BRgFcUI1KZjtQklAijAxTwEdroSCyfWUkChQyF/0PEw2Kn3njAhCQVjirTAbErSYyeAv/7kgTmAAKXMlXpgjKkUmZavTGGAIzAz0tHnNZZlZnpaPOaygAJhJAAAJVoN46CnVpEnUr3Bvdq1nzje58RL0xExCj5kcG9qcCkgRrryCWdH/3+rTFOGIz0kPON9XTmMzksai0rYrXe8yE8vyz5hE05C/5h4mGtY2eMAydCo55VrDouTE7hOeKADSAAACrQX9kNN8SZZjxots2gS01Td+SRxYgkjXfYsocBBYlbVR63///d6RkpMxZxLUBNsGNnmrX/k/mbUvOfjWL7J/htVPFUjZ532d///xPNHMLYGwb/q4gwRSxdmkDa73YA0gAL+yGm+JMsx40W2bQJaapu/JI4sQSRrvsWUOBhYlbVR63///d6RkpMxZxLoCbYMbPNWv/J/M2pVOfjDF9k/w2qnkUmzzvs7///HNdSsHRKoP0anksAIUDkeMOrrdUAAACYA0J4MgEjUigxsDNLFThjoWDGsqPUyHjRGAPPTrVZ4ihAiAZvSwOX7X2LbpZA0QOkQfEjRIkEIvAABgaMUTS1N4baTHiBrBmQAo7GroWdPgYuQZT/+5IE7IAS2TLVaSIdlGRGWm08RrCMUMtLVPSAGX0ZaWKekAM9R9hLMloBAkGDL4IXP0EJFKi6eThr7j9SBmnJGoLJzOQaIZznkiCSIcAgQzrTqtD9OVTtctShlb7Sxu7+3qQLDNWQlx93Jbf3vff8u3Y7ru+f6aEJ/7Gv/na+WNNqxllyoIRW2ABJEbAF+r+nkcWAAAAmANCeDIBI1IoMbAzSRU4Y6GghwVtS5DxojAHvla1WeIoRRAM3pVHL9r7Ft02QqIJSIPiRokSCEYAAAwNGKJqJRYbaTHgg1gzIAWzGq4WbPgYuQZQ9R2hLMloBgkFDL4IXP0EJFLC6eTS19xPB5m2SNQWTmdg0QzlPKiCaocAgQt1p1Wh+nKp2WWqRlcNSxu7+3qQLDNQQlyV3JDl+O+/st/Y7+98/0qIO19TX/zO3axpsL2WXMOc1vus+/+HN2MAeSHj3+GKaby6r3Cv////1mDKHbmmskG6aGDAAUU0mBEmGj0tAgwXAHQhlBTNnFHgkiGgEUxVh4ED1ymNjpmDULEhcEwUcVCYkUGLB//uSBOqABjJEy5ZvIADS6YlyzeQAGSjtNBmtgAMnHacPNbAAJhJuDi137ZgJE0Ni8FOW/afecADpWbA3GhZhqfG/ZbAKgAMnjdZaWFU0GAkz+QNSAwK2GOjxmImYgCGJAYBCg5RbsIiUHDYQkJuGXE5RAmIDilJk4KIgdhc7BEy+hiAQYiIr/zYxFhCCgobSTUZ0m0uFB0KBiTasveaz//616/nd///UlGCYmr/r6f/94AIAAAEaAAFFHBamcYGmQFygqCfWWLMeuluKGKAJLGIAPM/KTJEDA4RnWthQFZSYeImPJg8GJCgUOaCYQMGFAJgJGGCsP3waKPO38kf+H175xAhGTRE4yyWM5p4ukIjcFnQ1NsnCqQIcTKWgFOIFbDHR4zERMQBDEgMAhQsYvUMCocDiRQsgxwXKFUwYOZCBjNBlsc7HLsQMEADBwVv82wTyIYYDqxqf0p01FYogAlvtF7z8///d6/vP//9UIwWqFjyBAIBL1iapNqoAIRAAAgAkcAThMgBaBc4ifcD8wpzM+SEYNoUqnGrlGOlrI//+J//7kgQTAAKgMtZvGWAEVyZaquS8AIrdS1jmIK3ZVimrHMQVu1kTVwPD+FAyB0Qq7jWfPHX1Tn3tp3/+6F6cw+4mqkra9b///wXDxJxpCy7BsYwANAAAACMA9YpiCDWXHGZ7B8MShPO5qRtwzUbtVfPbNVIV66x////JfU0F7YqjzIwUxLkC5RmHP1Gi31FtHt61t///8Y3rGN2e5heseb///3EmuPld+cYKgEAlbjCuAObrjyayvX/NlhhLSlSLm6wTY4VFxWv41//iixeUNdnBRQdgHFggFL4uZ2iSPKrf7FVCnAdBHQRN//+aiEtRmr0falDWMWysjLPVEIw9k3MVAIBK3GFcAc3XHk1lev+bLDCWlKkXN1gmxwqLitfxr//FFi8oa7OCig7AOLBAKXxcztEkeVW/WxaFOA6COgib//907NRmr0dbXoJOpm1VGWqFmaPXGgQCVuI+neHidpKl2CyELg+T8zuU9SllEmdxsuUQPpM/ptoCfGEfjco7X//5AQvIF1hCQNkApgsXxmX/M/ldKt/zPMoDIYx4//GFknb/+5IEJgDC1TLWGwkUVFuHmudhJT7LwNtbR6Hv2Z+bak2DPjOmrRIj1RoFFUAoSYdXYKAgJASf4m2FiyoaVthhAJPi5/JpNwQqHgwB8ij0o/ptwJ8YR9WzFf+IB0eHDiQRDguHABYw1mNxJ/K4xVb/iR4soCyCxhNR///4obL1T8p6+fKlY5ZLb2H2v/00ABACWA7vxBE7YxhCTIVBer8ZgZoVoT6NKHQWdFma1N1PuTUkVEJp4ufY6llVaxVZRaiv//hmZv/H+//i1ssQ+rvma17Nqt1mffkgFfmutwf0z06wjWqNLPp2nnRf+YQCl2EdIUuGIRA0DBojDL4yKUS6VU0qf6RTbtOdbqEZfPKPlPXRhyIqn7a/5wVJpoKaSWAZeDVNP///7VVV8/xv//4tbLEPq75mnvZtVtM3x5IB3hl1pdtqXjpZPWhODAlw1e+X7/f9BACAlLQ2AU6rcNLBT2hNfnpT89jXGcs4dixGnLPHVpaXocaRsxPbPudPzM7s2K8rlXMtHABQs5SH+mociuQpf/4MDN0cqVAGV4Zd7S2Q//uSBCcNAv8/UrsMEvZeiXpnYQWKy6UJSk0lB9lvICppkxT7zjCF0jR1/GV9+9WP99+eth1ECAEBOXB6BSq5kIwg7fOXPSn7WOP1dZWJW8lHNhWhZsosaPIcPQ9tzK7/n7B/FjqJZyDj3pJ/6aiZFkKX2/kCIBh5NPYOMuY/1LojsRbM/KrLY+/YVQgfJWbbtl0ipSASwSZoDQIwglMvxBh+GdVZqqHBsdXhaz8OL5AwZFkYPEM/i///BJxQWEVVmh5ZlMsX+U9ftjVmv965/5EEOXb5l5SxOZ9Jqvz8zVJxVbSd648EbN7pkb9jaap7s8AKQAAFu7B0m9DyFPCMqBGYSwJs1FmYYLRfPW4U/bJVYo1/2/5XQWHKysPExRjFR+JHbmjSq3u1fUIgKc2qnUhg0UqRlKXXjVYQIZRxrFNFFyTd+8Zteg2mNX9PKgSAANy0J3kBAMhpir7OJvKePuSb6yyEG8LLSHUYc5FHFB8C2yjVVv/+GWAsp90Mq4zUr/7nGscOhn//yUv4xQu79Aap5RpLvI7fI5xsGFS4n7b2VP/7kgQqABLzN9K7KUG2Xgh6V2UoNovhE1FMpKfZXCDpWaYI+2/Och5g1y9p3l9gEAAbloTvICAZDTFX2cTeU8fdW9ZZCDeFlpDqMOcijig+BbZRqq3//DLAWU+6GVcZqV/xc8sdUNf8/kp/0KFj79Aap2UpLvNfxVda9rsi98+dK4wVceuAigNWxM+C4AsIBkuXYOivMPqX8YhSnK2H9IK1pAHp2FGnM2+fUvUoZBCxuygdU9MqIo4BiILmOGKdf/GOfIhGuhM4cdBZRIe1X8ouxg8tMyktZaleyncQNJEt0WMt55MWTCJ3jdUBQIJeKIYLdJBGSOFYFDxlpIz3mClfQkVRRbG/U4hvAs6hio/7UOqdkyolgyI5lGU6/+CnySNdCXOAOhoZ/XsLYwrTMpJKK3civBW1+nCZWZ6FOmOf1/yVAGBUkFtyCYaKbgTBfaBI9bT1Rb9tQhta5VubvSbxkLJnthn6Eq52ZpGUGQKIYiv1a6mdpHGFBThAZdaf/JYl2UKVPrBFQZw9B3UDgtrRZhdqWRlL3sl+gDqqSC25BMP/+5IEL4ACuT3WUwkR/lPm6tphIj/KlNdc7CSnWVga652ElOthA1JhI6QR62nqAizScoCs1r2vm76byJFbthn6NudmyMoMhRDEX7XUztIcYUFOQGXX/3JqzbKFN/82rmbW96AjBH+rIJzKc+r3nurqBAAC9/xMQEQDbsyRFlU6sajSimLvs2bJ2EwDj6B27B8QYPn/7zCofEgAY/QjWT//9BQINe/kd2VTFKchRcXiM+TEsBTu+++LOOUXdib/d+bm39/tICAAF7/iYgIgG3ZkiLKp1Y1GlFMXfZs2TsJgHH0Dt2D4gwfP/3mFQ+JAAx+hGsn//6CgQZK65FOyqhRx0Y4ugje9ymkEETf/xXmFN20xbPz75vurhajqAAaf4rXUxqq82rLxCKXYYkGJpkzAs2sllI+TCA1mi6gspDLKSlqUYzoNRKCxpYWTXMMqln//6GIb/Qw9zxwsOGGCIDtxuTD/8fctoiASjN9wtNLLt0AARP8VrqY1VebVl4hFLsMSDE0yZgWbWSykfJhAazRdQWUhllJS1OKT+BUPgmUcImt0//uSBEOMwq0z15sPOfRVBorzYeg+ipDPXGw869FOmauNh516nMdX//p31+iIb//wj25A4hEFAX2Ln2/9KUTqnhUhSkl2INdQAKJHGeKENKQBj6hg32TTU9m0dX4Xo0jjz+cV0OuE3banTYM+h3ICzRi+sX/9HcIS2zu1l3he5f30PMQ4y1Ke9FUbnmuPFnCOgyg0R3///3jFWAHIgAokcZ4oQ0pAGPqGDfZNNT2bR1fhejSOPP5xXQ64TdtqdNgz6HcgLNGL6xf/0dwhLbO7WXeF7l/fQ8xDjLUp70VRuea48WcI6DKDQh///869i6BaCAACihxyuqOum2s8qEUrfQJ53HCs7diiX6Tiwx5RzHMN4M2LAhk+yu9/PS6YIr1Duaau971eb6/6tOqX2/f/95rUs3UjsOpJGU///8hjl/rCAACixxq4z4Y2SbgkBhwoE87jh527FFvrusMpry+T3PHaQQHjuK3vWxcfAaehGOSs888rV7e0fa9f+8dZWnMO1B1YsLRZ///o3rCiapQUxUSAJmyqEH1Qe+Nmag4R8y4iVf/7kgRZBFKCNFe7D1nUSkaLBzzpuImUz1QsvKvRHJnrCZWWyFkEuuzNIRpk2vOeB1NwUjLGOyA8dw5M6z7ykOjN2vGnCg+hjM97/iNe6f2ISjIFnGsosgmCgPQVQwTBZhMOn8LjUclvVsdRflBYxpLrw9glyOWPoLem2rKOWcoggxdOJqd8g0eS88uFB9DGZ73/Ea90/sQlGQLONZRZBMFqABbf4uUyq9sRsbZ9H0rX8a3M7HL/K1ndTOG2o2H2igjWQAURhYlSNPjf/9HSV5l04LDjCBUb/6G//KKEK7GDziIdbUBQye/tKSFj6Oi6YzXfeEAAFufjVMtO2KmeZ9IYOo6tTdzv5p3WLmYbUj1qjbSwnJzo9GHXM/8DTR5o54KHMQsV///8N///+qIt0zWSbXMgKmWFSP5kpocbdq6PeoEAAJYCVMJBQSHojvTiIRHFjFqX0Px2/N29yG/VoL1Iq2citUsUClaVgACeryVVfn5zjsl3SwM80NHjTiLNz///84iCjv/QjN//5RIceigEKbCoLf4rPSmEwkZu0JVKHXn/+5AEgIRijTNXGwkstE6mivdhiD6KTPdU7LCy0TyXK12HnPpo8va9y3y47rJqhSxNYy8woXr40H86SRv4+I6lShrHFRkqeOs3P///4+Ohkt9QYd/QeIwkRODCqmtMwG5anHwxrgAG58Kd2SgsOGhC/lDEm6ad+/SV9tf63EYaOZUZK8GlQUFMCaw8vc7qqqL8DcZxsuqNVv+a/V2/1FJ0eeYZx8eA4sPt/7giRAzLHHfbXQoABufivDKMMOGQjQlgFR2p379JX21/24jDSXq9V4mVEApgasPTud16qL8J4zjZejV/81+r/9RSdOeYZzx4Diwe/oZnBdLHocMGEtqw3BBSBrXAggiX8biDuzItOSIuQxXx5Yn8pGntfqPWKzzPwvYan5CKgcHh4vbayGuTPtpFUVvaKjc5rf///1gvHkP95BhgQg8IDo8cdDHxONQEWSO1yPDmlJdYIIIl+G4g7syLTkiLkMV8eWJ/KRp7X6j1is8z8L2Gp+QioHB4eL22shrkz7aRVFb2io3Ob////rBePIf7zGFoVB4QHR45hjz/+5IEngRCeDTWmwk69FFmeuNhJ16KiNVe7DDr0Tiaq92GHXq7djFEdX5CBCjf4kJkWoQsG4GwAWoSnqI6OPMqy9DhCFVD62RxwYUgoBQkPXKVZa6YpIiZKjYNM9v/V289m+bQfYoQ6ElOUamJ35cThh412gXctoNNSsBhLlkCoGCCkn4kLsbYkYYAugDaoU9pHRx5lWXocUSotbQ9CRUKBYSELiymki10xFgpKiwNf/03b3ZuRBGYBxwUCXoHhMPDQBHFxXdxcLTTZnEiDow9nR57aVx4YQIc/FZ0HOHUv6MoV0+8zd2u93261dvFQT9izfcKvrPG9D4mrYX8pPVSKKYgpJU///9fRQsJQjEh1kXoYVOWUljXMNHoQDK2lBakMNmVXlyM49yACaQCBDn4rPA2cdLDoyRdUTjszcVWGGiJlFG13QD9uq9YK6g0O4iJUNvqH9RVFZAsWp///6+ihQeAYeIsi+g0yyksa4Be9CCqxNVsbM9zrNUAJAAAAncPKhk30hBHMHhlcPP+y4so/bLfLsK5W8rlqlFRWLQyH4qj//uSBLuEAqwzVpnoOnRVJrrjPQVOinzPXOww6dFBmevphJU6sLsiCjJU3eJDyB4W/////QIC4oFr52uKjyrd9I1KVIlYtliRtDzLSQqFiTmGwCZABJCm49Pxn7DBkocWnphO3cEEG5VLfG1nyb9Qg0iaHjRCJhbIggiVN3hhyAQqn////whyA1841xI5VKRv/ux481LWbqn3z2rvQGw2RENbAAMBAAKse/DWqyAryqZHOIkIc53ZyyDWjL60PRMPAUjj2GCssrozvtUVNGOX6L///Ehaa2ICyOUxhwDlLXECGM66mMxBk6l1yH2cPmsZpyyap/Tvq6AAYCAAVa9wF9RjJXkcyOcSkkL1u/q8+HO4ae0lkrFgwTGrwpHzyujO9WY4qaMdfcSVqlV3zW/iQNNI0QFjD2MYoDiJfECGEnmq5ljBrzWuDaNLpwAIiQACrXfwwtx/beWWkPJIiD3q9eYie3Ek1N3TOFuJG6kpaHXHjOl+FdBQbUlCtS3886xyg80fk3X//8SaWIB4OHfsVu7/Hz/1bQL0Tzb9zy8zPM5v9f/7kgTTAAKqNFTTDCn0UMnaymEiPsqs+12njKeZTp+rdPMVO5HWxvTKZB+gDkABUquwKpeCZ48aFwHw8LjevX/dpSswelXomJ1WdF+5VNfPpFwSuvQFeS+xKcIG49Tn//d381rwkiuDf/1NUcJgIF3OHnPJn7a+r5VhV7v7NF/3OxZu6Xuk9mwCAC67sEgXGq5U7Ikw9DY/HKg1KeW16iHyxLku0qDhJAA5IETB1V8KWUk70KoUK2/mZSC4gRzqrN/2O+zKHTipzMfEXd4jPw1JJKmjE78HaG22izmVrO8/i1mgCFAALruwSBcarlTsiTD0Nj8cqDUp5bXqIyWJcl2lQcJIAHJAiYOqvhSyknegqoUK/80ogcQIc6qy/9jvsyh04qczLUaO/3cZFqsecRi+cgJJtLHNoG595/kpSggAApqUECZ4A7xniAhCT0h3i61SbsPvEzBwytUkW3o4kxXYupD29uPKpn/9b2ekvFhemDASbuMcHOt/CTh0HWrjoQxkeRLvzQQxkLuLmZajRBtC7mWnzyAyEgFAWOIJIeEqjjv/+5IE6oQC9EjWaeZCZlxH2qowx7bLWNtTR6Sn2WybKmj0lPsicQO1IqySEp1nhATEYrUkVNLCwJhAAATUoGUzwBdxLhKQNpmQ7xdVpN1Lq7hmDIytUCLbFFkXVdhsjPXa6DghUv7HysK0cDNBARXdOhzrZ4RyGgXNDjINBQKyJd+Tg6GQu4h5LzrOwrBIyxoeXoA4GAHgsbgF4YkqHx8VhYMeUiiFuj3siYsET4WaKMeAnHRAbAAkbRIKSJEGqWyI09RuNm40gcY/M8hWv/lJc3nfBE/f/99uclTucKFpoR18WZnVUQ8ajSRA8xxJU84r9c6XMWHKuVevRU9Hu9LGWM6muYnKGVJGT04mB/6fwpI46Hv/7dS2iiW+0ACRtEgpIkQapbIjT1G42bjSBxj8zyFa/+Ulzed8ET9//325yVO5woWmhHXxZmdVRDxqNJEDzHElTziv1+i5iw5Vyr16Kno93pYyxnU1zE5QyKSMnpxMD/0/hSRx0Pf/25ZbxT9tAC39aLSCAdswsF/c3zajLr3OtzUkp1P+9t0i6DEaDNf5//uSBPEAA8U0UrnmfaR9JppHPG+0jLjhYaMl+jGUnCw0ZL9GqRzdIyucWV6KNhZAsL58b2is3A4wnQGPEg82urC8tOj9fYuByLygWhI1f1q3mmLDOSgBb+sl5RAO2YWC/ub9ajLr3OtzUqnU/723SLoMRjFq3+razdIyucWV6KNgwQjAeycR7IrL0JYsToDHiQebXVheWnR+vsXA5F5QLQkav61bzTFlIoQASW7oaKgACgKPYAsJJuBOClq9wiJBy0HFj4MSZgZkR/ztlC+l9q0sP+WJOxpe6r+KyytDxxyBw/Kid1ESiYLWhCNFKCbI3IAnpT7v+zZABJbuhoqAAKAo9gCwkm4E4KWr3CIkHLQcWPg1MwMyI/52yhfS+1aWH/LEnY0vdV/FZZWh445A4flRO6iJRMFrQhGilBNkbkAT0p93/ZsELzqEGACAFGGAzFoOpooIUaQhYwe+r2INc5BYzFT+p0YTCCcZ0w3kLIpSRVo/EUoEMVKVZ1YOE5F9TMais2q9OKY6GCMO45C1QxjZwWQC8tHOSCOHptVQ6WJP1//7kgTPgAKVOFdhIWWSU8ca7CRMsgnQz2XhgZgJOJnsvDAzAVvMTyxsh0nhUgIXnUIMAEAKMMBmLQdTRQQo0hCxg99XsQa5yCxmKn9TowmEE4zphvIWRSkirR+IpQIYqUqzqwcJyL6mY1FZtV6cUx0MEYdxyFqhjGzgsgF5aOckEcPTaqh0sSfrreYnljZDpPCpEUBXIdTJ0AAQAgMQp6LVikgWnJwdNei35Ov8kuXlFLpRGO1///NtnTK7xW+50hEJcqJsAK4dMmywvm880zsqWUtk4XAlk51W1R/hkDwkDwZLPnn8o3UpT//zvxaVUCXYdUN0AAQAgMQp6LRxSwCzk4Omuxb8nX+SXLyjXSiMdr///3WdM7smnVyEXnlRjACuLmTZYnVzzVdljUmuoScfXUKavsq5MQLCwTBCUPevZeGt/+sfVKoQAFgSABMAEBcOToKxBzUL7ZEb/GFZXb/tOc9MCFj0TDKSf/6z7zPzTPw1A03AmJRA/zVVKrcIcTxwISOOKzdWIzoyddsYnj2ny5di4RjE5HEeBUTRxOXYnjX/+5IE7QADKTjXYMV68mUnGuwYr15LiPdl4zE6QWKe7PxmI1Bm/+awUNEABYEwATABAXDk6CsQc1C+2QZv8YVldv+05z0wIWPRMMpJ//rPvM/NM/DUDTcCYlED/NVUqtwhxPHAhI44rN1YjOjp12xiePafLl2HQjGJyOI8ComjicuxPGrN/81goaDAgGMWQekrhOwNMG/A25xLx48kRvjw1HHamBxZd0xTw525OJV+oKv8x4lI+f/u+2BkUBORmE8XK/FJin1ZOyRWYVm+aWzvjy0pTKmYQbV3GSE84MikloFheMl0CgDyCEAARhDOxDUsZ4/yX025qh+PTKQFp2F2UYijseOfpRAbZCSbpoQm/+/QOEwPIwGwpVoPpucs5R5KvmLiflsQ6UTsRbfaitRqak0QhI8WGPAJ3///pooEAATCyWEpUDICqMTQmOwDwGWX61BJYDk9qMOAhbOXVhTqoear8Q3OC9IAkCALtHKN48eRMf+8r+LARVkNVrM5aePVma/9WT+pS/I0MVRDsLCi3ooZf//3FiDEP+4DjEhcyQKE//uSBOwBAvw01vDPZhJf5preGezCS5DJVMeZPAlemOuo8y1y3CQqoEAIBJvFksGrCCSLxMkAqDyO52tQUM3R2MBwLasOVpyeLOrONvw1BwuA4nLR/RY5SZmZ1utCtYxPT57ljqnvmasj/qUVbyDkDwicOOOBxEXFqKAy///uBwQKR3fxRJ9gHaEgAsCiFZ0KEzCowddCDpKJ/gTgpCrHE60Sp60Tsyt+KVn7QU5UIxpaZa3Dt0kgiDF4/dla9qrNUQkOh3YquLsJOmQn/v5S4kEzhITDxx+JjUJ7//44WX/1SSYueYupQAwiSleMH7dxYqqUqEJisDbyuyZ+zdWzx0hjEBKESWLTLW43U5JI+2P1MrVWqs1OQxxqNXmmq5NhUelDDP/fzTceFZMLD4Pjy+PlTjPf/+pEXL/95Ii0agiZQoYmACEk05xbcGaYvGoSQBa6Vjgl/4r2WL1TdwsHA97u5ioqSiDTCS0MUaP+JLIBoLHlIOouSDpHkD3s41emvdzZNf+0+T5u0V+wkFUMpWHZ7rd4Z/+VTyQIBJWEbLmt2P/7kgTxDgM8PlSbLxPkaKe6t2GFboro91ZsMLDRcZ7rnYYdOml2JLNEUZaEpXWSj7LLexHInymP9XghAkhcYrhN801SsqKPxCWQ0+EJLD/97MIJXTKx3MS6zNvUkc/rfdouq//sfnk3XcTf0QRxDMrOnJeU/+46ay5hIohrmgAEl4UKyqMmOQmwwYIhrbFF0gX4yCT7x2oYj4d6NGcKsPZurxIi7gdQBqcpIUD2H5/axhZec15tYQHnSRx/6uf+c45/0qnON3Ndd5MXehxpcnGzFHbtd7W/Cn/8Y6hqkOUoAAAPChUWoyI0IAMMGHFGehFDqEQbac595ZRu7Yplg3lTyJ0Rp3+26SivENKwdn6oiAFoLR/cUMFHnkyZ6SgQMqic/+c/////Ztuf3u9SL2cotM9Dli5P+WZ/9jjC41zMMh2AYIKKHG4jSrPadBkwHmBJV6Bp/bRHEidCFwDAWKCXMzuuSxxs6rM0ko03ef9GZ6U5SRxxQtE///dK1S5TL/f5DiISWOVfoOQnzTRIW///C2ioBAJ9KSoAQQUUONzNKs//+5IE7gjCrzbXuwxCRGIGypNl5kyMPNNUbL0o0YkZqk2mGhJp0GTAeYElXoGn9tEcSJ0IXAMBaBnPndcljjZ1WZpJRpu8/6Mz0pykjjihaJ//+6VqlymX+/yHEQkscq/QchPqbJwOf//sZkEMi+XAAAAglAYOBiJU4QkUkOGwUbG6hqAizVEEOT7uQy9BxtwMCBxVL9Xi94S90uRMo1yAyFRAUBBCTmEpTZZj6QEwXRGRIJj6tAOkbSNFZlW/BiMT/MF+t8IVpNz6JnARpEnDDKMpSFPdZ//6QQSVsMGTDAD/p8QMk6rGTgBwiNTR3Q3jOdDmQoYpzoxVzOVqy7jDTGISTQarRn/xR9SglssDlJlJG2an2aYn+Yf1vjK23tkzgI2U4ZCjKDEKPOs6We9hK6g2Pv+uAAAIoboNEBUpGkmhVhZYOVAZoyTVTylrL8QY9DlL7VQborCvweIi5HmsWeEMBqQtFp5G83XJmZmhNUnS65+cHiGU161zoEta18pfPOX8jHbvbIVWBSwWigrXIpmkZ5ySxz//6GJRnOpIAQAA//uSBO8Agrc0VrsJQnRUZprXYShOjUDRSG0k0xFyGiqNl5kiCMIISAEIlekzWSu4QaCFGErO5l2YpJIIh5u7GHgZK3hUiSfkKRpcSwpYPj6y0r7f/9glQpKyZhAsDTlDCjcn+d+ecv5nf/34qsClkdKS1yNmzPK7wY//+L7xqig10yHTQIBKuEGKnQ7qOpw12HBUwFwnQ+kTdCKum15cjO1qOW+dg1pj1gkPSggg9Yxuv/5XsUckYSxNIMBo4WidOmV4h/l5uM0ujs/Tgc8ieq7pRIjh9QdS7zho+5MEEa3FjIaJ3BQevPnC4IBKuEGKnQ7qOpw12HBUwFwnQ+kTdCKum15cjO1qOW+dg1pj1gkPSggg9Yxuv/5XsUckYSxNIMBo4WidOmV4h/l5uM0ujs/Tgc8ieq7pRIjh9QdS7wi/MpIEu5IZADYswXbfGiMMAlXCJxZBggO4U/BCIZApAOPIblMz8tbg0uIJJXNy1ho+/K7EUNsg5A/zza7/+CjYeKa1WYWE2f5Np7/iJlse2liRU8BsnshSQkCQJ/6nXiuu6v/7kgT1DINFNNMbLDRUY8aqh2GGhIzAy1JsINDRkplqTYQaGpiwcc0eFACJXqODCBiFJPcN7kTATkvzm2EjD4HrGYpmX3SoV2AZZLtFr3876itoyAvpo57//7HItbYSTIyeUachBK/3Ry9/7M8tj20sSKngNk9kKSEgSBP+oWd9qHVHFg45qxzp9QqBIIJBkEFwOVcNMdiNI0RgqCj2ZPRSWGmo3u7GIBOrDxE0RHkMVP8ellLBHE2iRkVrZ53MacjqWl1s9HlugteAkYhX/Dr5swpRFdYSSxDrHUgCBAJVwgF5EeWiNTh9pSDyDoUSuikdmQrDRUt4XfU3d2fl0lAx/scPoZcq9Rln//i5kDWCDDltJfL33ON6HrZncY8EbrnBT4BCEHv0S2lHtg6wcAQCQB4POpjqDAJWwdB9EImHuDIE7kESUoKERFc1xXEeJLtTVuyaIJE2KEOf/GSixlNzItCOe/nQ04cOppSEIJbVhF6AhQnVJXa4VshpnVwxwABiwpX//0R0MerWC5M3v207J3McpMQgxG3X0MAlbB0HQFj/+5IE6IgC8jPUmwg0ZFvmetc9hlSKENNfR5TR8WuaKl2EjhLLvazIEnhGIqBAyiJrUWWtMbsgemCz67TPdFsUIc/rDIwsSpuZCpi1czX8NuWXLamEGG142hTvYYcb8xrY7smvMba5spw4FZPFVuP////7SJqaShB2qeJiJGMrRk3goQiJYKLtzhDBSewbFTwy57tS5xlRpSjU3ddZp6x0CLktikcJxt3CMj/304pIeAcKvE3/f//wW+ESkhGSvDl+t/TMS38tv/LV6z+71jVR7FzIdAMgSAxf/+eo6LsdnMR7kchXSdPogsx6CaVcrX4xmirgtqXuyuNlUPMuHhkgxdbXWTLXRTKh2cqPQO3uNugFMRPfvypofCuKNuJ5w9/OQGdgEEKBVdgw8z4sgrr6oa7andArEqHKoUDICAv//7XZcyPfItLpmewQUMqoDSVxJU2OCZw1ABAgUFJ7iGvwVW/ZznEbCPJV6slC8J1tVPq7rV23n5r/sVgJqEYOZlPsS+gFOiOcxSojibFQ6n/1uShm1KIiuMDBXO//+REKyK9q//uSBPSI4xxB1JsJE8ZsaOqDYSiEzSVTUuwYsdmSqunNhIoao/R/oPuQzC1tx1dI2lMJgBCAkvYMaTgoWzqMlYK8A2QMVxF1Vg9Wg0Ko7FV5cQZ/A2FKDppIaYvi/6Sr+AauVFnlKsRb1w8v//8/8NXyqr4wKmsj/////wxRToCMLcYv95bWyknu6LtpTywAQwCVcFH42vtfbrsPQ7ysCyJQtZdsuqy1hYVCxgQIFzy7IRZq4JSkUdBeRvNz7DP5juChxCzEqX/ON94Hr///DTOv4kHnvNqETiRm/////mErviuK0qqq79ZGotvLrMXK1bV3JEZFQ8IgCK2DR6du7c37ctbdOFTJ3v7DaXrutjFJEwQMLuXZHY1cKlqWQnIfjufYZ1S8w4QKqKUT6wt50DRVv+/Lwpxc6uoGOMRP//dmqiM5CUEFBT/NqZD/jFfXRnS+3moAJYBKtEhV28bT37jzgAxiJVA2sJBRpcfHbREcEJYugbipk/utmUbl3139s9mpQ+VmSYKEzKIip/DBfaV//YqGIRgmKjEFlA7jw65P6v/7kgTkgILSS9XR4yymW6Zalz0oZM0NS0zsJQ1ZdKHqHYSVqxK5ZQGUvBnFiRhYT0oONACNoASwCVaJCrt42nv3JHABjESqBtYSCjS4+O2iI4ISxdA3FSZ/dbMo3Lvrv7Z7NSh8rMkwUJmURFT+GC+0r//KhiEYJioxBZQO48OtR//9Vt2EBiUhtGYcAUOY8SPusLiABK0NEYfRMuVMwJlRlWXxUu011kMhpYclstgeYpsyKh3zPMvesnoo9GXKiP+3/llmOTfSCadtjjxi5jP/17JhcRjx5qsWJ0fyQReYUCZkYKoAgkIuA4RY89i8TBsiIAWiMPomXKmYEyoyrL4qXaa6yGQ0sOS2WwPMU2ZFQ75/kvesnoMWjLlR/+3/llmOTfSCadtjgyD70FLz////BhlNqwwu78qIEkhiAdQKEzJwUNEwsloG1x4MqgAALDAKewRRQ0Znp4rxLRkp5jZm7ea+zZeGz1Ya5FWMxWEhcq7l0v1ZHcg4OmFnmHu9IRHUdxZv/9EqJjiugKKjBZ5if/87yURFtmAw6r8JDO1Mx1r/+5IE5YzS8zNTOwwq5F5IGmdhhVyLyM9KbBjxUWuZqU2DCjJ6GYUpEgp2OgAAWGAU9giihozPTxXiWjJTwOFCtefQizwvAWXRzcnDk5f3LpfqyO5BwdMLPMPd6QiOoZxZv2/oaomOK6AoqMFnmb/+dyxCooVkZYqMHlU8hUOy6mHjTr/ikiSJzZAAQCDASdoHe/GECkGo3AUMCwc7VCskNWbAcmLGPdRoa4+jEzqmO7b/prSlDwk8sTh/934qlcDRBb//7/4e+hRkIcoRyxd9Sd9P//2YYrdpVFhwZ06eiS2Bm76jZUmXqYCEAUpAD7V5SLyyJqRQXxDDge5irz5cGKpbGFA4CQJ3jdpPDqCEO3MbW38U1pRoLCQjFwuAeYe6YVT4GiC3//v/w92OBocgcFlCOWLv1P6fP9/FjJrOQyi7lERmeUENgHy/36NY06oMAA7gcGqdKieazDy+EF4LXpJpbVpIeP4Xx0qMa53joexGqDnPxWAsvKQt71f4xvPz6Od8YgpNnkZFKhhJ16E+mkyTZ1cfEsb3v2TzNCpd0ZhA//uSBOmAgudP09HlHPZd6Zp6PMVMy/kFSUexAZmYIOhc8yITCwNBSxnyjz0UAKlqCdgiKRWpMCBUMhEqIACdwODVOlRPNZh5fCC8Fr0k0tq0kPPss53YfXG8a9ELBZW/6iUHUCi2v6/qtv34G51SoPs8BkUqGE3XoT6aTMfW87hs7/79v86x//T/ErvdNUXk4tHrGfUfYfUVrEg0gwiMWeqI5oMhELkwBALMASewSHRCHxP5wHaR1ZS9ZX48oWIaYuMRd+2xNxxV6GYz6H60j3r7OMFpAweL7tE7qJgI6i4ykiIzczkqAg4aGqFsWWyf/1QtacgNmRqZGFfh3aHhutbGvxq00AxgBSuCkUxnHgp504SNsiihiavFdGhwgLiIoo7N8utggZRF4mxMq+OPV/mOIWxh2YGh5/2Oy/lyLkfHGjJS/taVIODhKGxo2nOGfxDE44mHDxQL80HjY1ATYIh4caHjdQAQbUISW4Bw4hs1GRgT6Cs07phpBZFcQi2y/a1AgEVgdbaYntmQ7Edhjk0zmLEQIow6o0T//UoSFToKt//7kgToAINPMs+bDztwa6ZZ82EviAvFFVFGFHkZfxkpqPShOv/+VjvqXKueT6b5XZ1Vki5bFRbWAEACBZhCS2AOB4hTNMyMCfSGqjPUBVB62iGxazAy6IGRitfWyf9eyPSXQgWnxW7LIghBIx5GJl//qUJCp0FW/4aB88Go2fOOol1C5dqx1hqpQAABJAAGQEGDhcg6ycsQuYRAb06mc2SOkjIMU6EKQ+pqv6RPNiJi6cNxMF4cVUtsZxnm/+83VOVKRNkp8CCv9Lvr+JwGIkizXv4q/+5RUPODGLEfkhoIGSIIAagDFhbKGSQoF4bNOepIATAAB+FAi9sWQ3WLEkUxpA8aegKRxiXtiYIv1nDTnLssVX0WT09mpCohFiJ5dNX2b1+Zne+7El0uVLZNNgYF3cgO48naphZUmHR9NZiURR/7jygUPOLM3//52JYjGd2ENWs5qLSiszLagveG0OqJqgCASQ4oSS5AH0KFwodFBLWNhsLmeWySA3hLDM1PINh8yzxYkzyN/SrmMLKLDF3tdQkBEMPUpdP+pS5ShQMrCnD/+5IE3QACi07V0SYqZlAGWqolBX6NCMdDR6S20b4nZ92GFjC6FHWZMcvo0o4ulcrcyPmasl45sidp/gfilVArAZ1PkAABahJUuAXk0mmhhEtJh8rWWp7mvok0TxMV8Wad14eo+Uz12RYlzyE/pUWMYWUWEFx/qJBxHdS9P+pS1KUMG6mF0M52X+pO8q5NrL5rU68rsTu0Zu4+qNYVyD0wAAaAieqvAMHIjC6NNE6IAXNq3e40s8Hvr+4YQavae56WIZ64trD2RR8VRv1omIKFGEYLKsO8UteeeRklBAnLuNKRqsNS5zqPaTHOtSMm6spERUshO7onC/48l0AKDnprwPhdCYVkqqV1JFysm7uamptkX67Y4TGtT/X+SzU+dtrQC5C+CxX/nJ7Ugy0p7beTnfs33xsQ5Qq3l+hUvsot5z/NaYubf23+zP6UO5ShMB0G5TgRwv8LcpoAAskK31XgCBJuykC5ARzZepBICNZhsVj1VGpGOM5nvJxTdILEouzipODDl+ynVRqCYSBmov5Gcp1F9UKLHYgiMO95HWugzt84//uSBN8AAv1MVeklRc5cCdpqMKO6y3UvU4Mgcbl1I2okkJpHH0AsnFAZMkZ5/ycvx8V98RIAAQAMeVaB8FM55RDxFn4x3izwKSPt1KgGZLreXzY1ar3Zum8Su4ZNGJ+2kzdlLz9tfu5bHih2az//wVMnovKxQJygUEB9PQ75fLz15q8TY3WJ/ef1comCY52KvrQkAAAwSCnaBmoABDTC5Vh0lFYGAoPOogkNP+i+sC1F8URuC5voje+qXZUlKAiG5q5qn4WTb7uXbGpTqtXf8DY1VpS1aZ0/fdFPWuSUw/sVOFOnJi7v9nt6fHnF+f/iM6mcMON1B5s1vZAAAMJFN/gd0XcHiNZa2thpjgqbQJIozDeFDOM2hiXRBr9YwtleSnKUHRP10iMoVEmRnXYgaFVUjP9gc1Vqg6tbpffqKLUrQqYs1MJkdl//yvvEBj+ZsHWp0ttBFDnktYwSoa0EhjWLRrsIJC3G01GEZKbRICHvH5karmPk3WPmKff+5IEGJIMGFoPyKnlHdetHh0tqMX/+ND/WqPqQALB3UKUSiAZphf/7kgTjgALPO9ThIyt+Xem6XDzDf8z42TdMvQkJpainaYQOEbsR6//fYIrNkYgaKBo4Wh9pTnuu38bX/XsBAIkKONtpsIyU2iQEPePzQyl1F8Q3WPmKdKG8kgQYkg1Jwl2+R9xree+tdbOesRzb+Z+NKW+9FQF5SgAsLlCsjRAN2hVmbZ/tcEAnnSposs5FxrrF4ssemwkAABbUIwFHQ5FOtxXM3juXKm1izzUKo4sLY/h33TJOHXvNGFs1MWXIeG2h2QKhHHSf/PzOwEpEsFLzQ4gCgIU/mvAR3qO5Zy+dv/7fe1sVOb+upub04/qEAkJFSKSNqQAXkOfTg8c6m1i7mKo4sLY/j30KCn/EMObOYspIeC2m8gNE46MRnxuYoUHaXRYy84cgCgIU/mRSNez+ZJRQ8A7MKipjB5wxLriuezW0jPx5UKoAAEAGpqIqQCEjWLrAQ9XG+lCVvIrsQPwZUMnCoWf31JpOD5zFoPgVGBUh1c468NHU/IEKWGryOrTWTKFV9pg+yHMsEMhkgfbXNJzdF6tdVNT6Zz70lYtZ8Mv/+5IE3gACuEFU0SYablonmp0kwn+K9NtHJ5hpuWGfavSDDO//Uhum/GZAAgAkKQGRpF4UDACp2HII1QFn3Iqp2R4fbIjUQTUzDtG87u5SeetI17tFI3KkCuZOcdeGhr+QZlhrZDVmi8MoVvtMH2QwWWCGCMSBybucn7oWDKjqDUPDbSOMjDjHZIFZONKgCCRIcoAEQwGDk1pl4VdqJA0AaGEAJVYHJBFpTCs9O1CgcDC6jiKogNQ8KUMzGQ/tsDE1jqd+921qeR5g0KVi5zJz+HFhwwIjJCDhWgUOkWSROH/wZ7pNZAi2T9xUFE6yFELrAgopFygEaQZCFsDRVYOo0XWQiQmkMAGPgyHwNmUZgwAgomewi0IGodljgoxkP/nVp6pr9j7GFpSEdYGhR2LlmTn2HFh5gRGqEDCnyg0Q8823/kd6lvcb653a0DX6IAwvtQAAJSSSUgCTLg71cT1CTKYxLI9HV4+Z9C1KCEGFGZE/tYMDDBDCpx6cJ88SFL0avRawrKChK8LhThR+gzVmnma9RSmHuh6Gn/dAyfvelk1v//uSBOwAgwRQT+npGmpi5+mXYeNMDHTbNOywaQmBm2bphI0hXLb0vJTfnzHBb2OoAEqgeBVTYFg2vJoq5UudMaczcNrJsD648KRNKAVHIMyI/N4IBhghhUiPTm5HiVXsldRasVlBRN4vCnOP0Gas08zU6ilMDuh6Ohf3jJPtio6dIn0zKpezj+2lHVC7dC+QFApIpwAH4mAp44S7l6DoAThVHgYtGAlNMVJ8BQcws8Tawl9hRk4+Wuq6luwMci/UyIET8iPSEOmg8eGeS8s3I1fIe4MhIpwhsRmWW62CbnDpop/U5/5uU0I+Ec0pWHxN6YIAECynEnIAgZLCrMlFo4fgYRXIyBPWj20xWXA5Zjnou7I32S3LW2d/JvnO9FrzG/mokQo+Zl0kcsh+wz0U+zMnV8h7AZCRTobKZrp3XE1HL0B1jJ/6L85trxJGm3y94gAADBRADgGPqXIRp8Q+8S2F2jQqcDrCyTFW0uj9SLKdEyzQi8gRGVjU+ULaJ5n5kTFfcK3djPh/nsh1HR+HJhZ2uQOlAf8PzJj04CMxkLq9qv/7kATnCALgT1BR6RpGX6n5xmEjSUxNVz1HmGkhep7oKPMNvdSpKx9p22A2g1N0CgqgAAEU20rgKTprDQaXD9t2UUVKLFRsPoumTUcnAtE3FA5RKGaEXkGQysinIoCHpYZzInUhQy5uAtAcDDmUP89odjk8jyYWb5lbwH//nxqhwEaUz6rq6vl/X10vDd1y+qCES7QBgDjR/KBrwpgozISkJc6SG8RCkFCEQOljAfavStprRoQUFTq88vUCLVOqmPScW6GXmxGaf6aztVGhVehmGEvFGDhSfvzY8qgKwzjrUH2YTlM4ph2W3ZxDTN7eJEUWU7gXEIDkaUuYBU1TKTGSHssG8RIRhCQKxCA+KV7201caKFDVjE8/PUCLUiqyi6ji3Qy/Ys0/01mDcSjQqp0NkJeKVC79L+Hk7SxmhhV3465f4UwbfLKDQnYzWUE1AgACQnAK0h4cBiaQqVgYDWu1iVtqIWokrKxMJkb0jnDFD64TYIa5BoVIbv0jWnA6JUTYV6R2hjQJYzT//t3vF4EBEVNwbFhQgHXnInHb7YEWOf/7kgTnCILzTk7TBhpaXwgJymUjREvtKTRsJGjJeqUm3YSNEcHo08zCxyrcABAAJJLlAzEIgwOFqAqWID2TuQvD05e4xdgVGy+PIOGw+uE2CGugaE5DE/0jWsbzsTY+yG0MoEsZv+5203fi7BAQgqO4MMhgxqBvPpH28aoR9W19RzxSb5fjhCvt4yABAGCSS7UWeNhwwwvYZApvwsKYlC7khVSUjjWuj4YwwlFp7eVD3Kqzqyqd/ZhXw2rKz7bE1vamQ86KPnIIOlf3FVTJ4IFmDFE63eUS5IHtfrtWHxQpv88CAljFc7Cl7TvP/oIQAAFIDDGNhMFNCbQjKQXvFRZQZv7kJUSHSOEsnR5hRhBKLvT8oq7UZixuU7nswr47Vi322I7e1Mh08UfpIINSvdxVCsTwQLMGKQWJfvjB7L7+bK75Fl/M8poOU+mC9wvdm80AGAWSCXaArKZgabosal25rbqrOe7oQaxZTudnlN8w7MzmQkWrXYeLAS81ORHM8zSkVmzsZQltO6/5/nAjnE4SCwqjQG8jgxMA5E5iTK26OMP/+5IE6AACzzbMu0kaMF7G6appgzhMUQ01TBhriY6nZh2TDWl9sX/9u5TcXRvm0MgEkCCFaAbeaQ6HESZRHaizdNJqbXQgvTTnLOzyTLRwURFhIKBk1YeLJ81eMIzMzOVSCyM7GUJbTuv//+jnE5o60oDebqJm3/khkTH5gptDNzKdB80J6edMTV3NL5UBAAAAUAApgr2Bi6ticqYsFBCQOBoxg5iTgAu03x7etTUdeXGrOQNQMo2q8IWEUIZr3aoaBHPIF0n7CY+bxXMhiZlqWFMXTshsZjeNq93ntv7OHWktUc1Bz22dChkcTPkJ0AEAugGchqYQG0hgK7Y8HGJrq1k4rgA21vD26VBYgNSpZsNSQ1b0nxUPLvKRkCrEQL9+woe2cW5FtrpYUguNElGrPci/PPfs0eOkw8MOn4H8P+EJT2QfQgSQDKjOiA4ZmFJ2pFKX2m7M2acAr9ILc429ZW4bGK+azHXiXMfTFnakx3uIlIDrknaqJ/CMp5TNZc9+26mSaAo9PPkS7fCsIzBdMpaWdpE6dLMy7ICX5Baj5mhH//uSBOeAguc3TdMmGmJdKOmnZMNITAjlLu0YaclhmSZpkw0h+KNp/WAEFAAyAzogOGbFJ2pFKXzzdmhNOAV+kFucbewrcNQhTRvYq8XzH0xZWThy5/1D860HYoA7jmPY2Su8+u4MgaoLrlLEcn2ydwrrul68anePFcRcdLhdWcAABOAsdggAYwSx8MFsVKoUWPsvUZIjpMHBSaD1qqUOFlxpnRoimczSqyhxxj+DgpgNe1qchUs8yVGpFnHy15vuQ/sLCBGJwoQUsGbEs4g2ImIFbMvU+n2aER+nmUI/8WOYk8RcJv//1gAEFwFjsEADFCWPhgtipVCix9l6jJEdhAcFJoF1qqlI8uOZqNEUzmkWjqBuMdODgprXWtTkJyx3oyo1Lrs5FrwruR5sBoEYnBwdYTEpuhmRAqjvKSdPrIfwiL5nyLwzMDAhxgEN2///rgKpEouVEyzw9WIFMXg5C213J7xhEbzaTnDPUruy9/wsuzV7p6ltuVwmVJwVJDN5AuTsTZ2uHPplC4I6VLuMT1gptSZ4FRRkycdD+9J0LMuqP//7kgTtDIMDT8ybJhpwVuN5l2cGBk0VOy5tGGmBo6dlzaMNML5dkb2N5mMwjBE8HHiEBOeP+j/9YABJkJE2czqFRoAJaM4TfTGQRv5OyHm0HmG7IjrPA17klrVHM3IfPPCXoxgnXN3kC5OxMx3MGcdyibCIDKpbjE9YBHlJuBSUk9zh/4MqnTLqjiE+ccGQXSFhcBHGGhAeXLP/R/+sAADhEWEdljl0EjUELk51kwkwTKAC+C/XCCAg9ymoKFm4uvWNy4chn1WRhnje551iNSP6r2OZnLmsMsbUEjpLk5Uq8uUhLn+Xr8P8/7ac+Crw4CHcBM///rCRedujkjSbYPsQPsrXq7KA6zwF0UPm55AMYPcoSgkLNxdNYYcbYcFGH6rIwwcEG93M7FNVPKq/vmcuazLKKCEYQJgMFTAWwKda4wuymtqEh1ssper7vt/11QAIABAAUpBFTTIqDi994YysO28n5l1g/MXmLNPsVcwxPMWj8tC0YLfQvC+EVeb0eqnZqzLmrdX+afUtYqeyER6ingdKkg5d2L9UTdfCN3Q+kZP/+5IE54ADK0zNOwYacGVoOYNkw1gLGTtH55hhIWKZarSTDV4lplVyyubZl4uqcz//9YAGAAgEGRAippkVBxe+8MZWHbeR8w+sHxi/izT7FXMcnmLR8uq0WLzQfC9iKv9p1U7IrMpClbq9NtPqWstPZEI9TePKkzL89TLiUweoJ4OBGGUhhjmlnCY8BEBdSunV/+sAABOEyaGVgpSJMglsDunA2KBuMmH7Nkh6mTK7wQzEbpci5EJWaMdxpqp4OmXu2SrmcMlazN4TwJK0+uIQMCGMxcDQgzfdyes5UyPpoDHhpS4nBMIhcJuFwKBwVWBlj0fp/+sAABOIyaGVgpSLMglsLdOBscNxk19q7h6xyqt4IZiJ9LkuWJWex5DIaqZg6ZddqS3M4ZK187N2CStPriEYIMxi4yUFP4e+w5Zl9yp9Q4T6bo4RYTOjyYLEjpcUE/75ven+1QnJ9ttbbLLQVxq8KnRu0Te12Qq/LfrFIV4P0hqYky4qpPtUI8cmeXeKfYRX3M148xo+vgD2Vlond6q8kWqUr1SWf5nuH26L70Xo//uSBOgIgwFSzNMGGnBg6DmaYMNODGEHMGwYaYGGJKYNgw0wh2+9+m/3dJ8Jt6xrygTk122utkloK41eFTo3ffvdIVrzDdYpCmg/4zToTURBuYnxh0Pd61Qr1RmV53lodP2F3K6lGaILyhY4osc4qHuzItX2ru1aKh0s9e3lu3suf0EvO8R/ACAAPhrxhNBF+kkwmJu40GAascpszObSJNdiNxD4ZkWQxnwuWGyfLPe6Y9sWd/j7l3VmfHK3be9q/T+qbX+FTh0s7Y/PeNeWCcdlf9vMlr/l2nez35yGWGCioLrJ0gmx4DKDjJf//+TAAfDXjCaCMNJJhMTchoMBNY5TZmc2pWm5OODYZkehjTJaLDXuefu6Y98tK/M7mvR8fJKv7N2z9namrv8KnDpZ2y2PeNeWCZPZTZ2yZPXxoOKBwTnhcEwxXYommEQqkahQjs2//8mqAb2lk9u8t3wnJfgB9En6jGrzXjZaXwa0Hjp07UZzYoQ3wlEZwSZiiwS5fkx+/WPLkrHKRQl1pqtQqQWHSXPzOBEkxZ4Ri1IhSugDAP/7kgTkAAK5PlhowzVOVinbDRijyc0BESzMpMdBl54liZSY6A0RYNGuHrQnc6j/6bQAkBAFxBaOYc0BgFjrdQu9ljW81D4S+dhO+XZtOnpgz6hizMjIvhEmcEmYo6HCTjAdvE0wKCZ5JRgqGxrWAFLTtI45HsIBqh4sdtApw0AjgcUgNLfv+5X/vLgADI0BK4FRUthDPFAvddoYbA4DHopN3gUcFQ4gECmLzzKhqwbbBLzdLrKgWhgwLNFOG1yBnY0TpamDFNQZvrFmCYiGNzYuBlABQ1tooXDbCssNFI0Jxhh+6PfbJXa4pZeTQAQAVGjQpcEJWqaA44P7ruCGwNAo9NOuYUeUGNAUz/MqGrBqxAl5vLbKgVYCBEcV2drkDbk50tTBjtQY56glTDMuMYxqxc3CEAt1F0xUKiJxwe4JnGiF6rlvfbJNRMzYpFI4nQAGkLVDGBlTL/vOU/lQFTIgtOHldLlk8FQWPnykw45sNqhdmFuLw1CW4EywQFcwzvPMjvKMdDxlMIq5H9ZRbmhAqgMC0A+IGESlKqTYcxjMsPr/+5IE5QCCxjtS6eYYSFnjuZdpIy4MLN0uTDBkUY6bph2GDFDWlExTW85asmKOd/+hV2kACuEEYxAJdzN3ACABhFOQWnW+1swmwFDp/5TJV8NuZ2qTtenPDvh1Y0TenW7N9+v2eGe2adKfP7/fZTvfyvBZLwFuxhyDPDOig20vVGZYL2MTgCkwt4YFXDSYpGVZv7fcubAAITgIdAcpZhXm2iiyIXTpXiqLpb2s59ECk4DIPm6Y9nnbpbTlb9Pl6DUkDHnKfWO+vDLOUiTPfglIKtVr78qZGpq3nef3M6VnTjU8hkQvoq1S5bogBIJkZDIHZLaL00LU60W1FOo0Dse5tTCCp4MnzNMezztKWq3fbTKU3CrpD+f2fmrQyzakSZk/Cka29/+/mo5Brzc+TF7DKSZhw0DK5x4/oq6HPQAAxAAALiNM8F2CUC8RUAg+JV3MklwMx1nWyst3RpsMJQKCkWoTMZaAjY4XmRQwRlSmaNCPO1ajBt6LImm60BkC+EMiNBpkotd6VcrGM6csh/kybHkuZmAxgwskDgqcB5loDDX///uSBOgMQwg+SxNGGfBih8lyaSYQCo0xMmwYZYlLnyadhgxI6NGsAAIAAACoTMlwOEDliesPOwExKueZJLUM49l+lc3XjW6s6AUFIFoyGzFkCNjgxZkjOCMqXikbIyd1VSYNRdFkrNuoMBRgG4QWMT4kFoJFrvStKyHl/wzy2m1PU6bLDDOpAxE0aDBEagCnP1v6t7tIGCyBFJOxiCoLEUHW/NQEEMVu3RVyrOYWhseRZO6fsg7mR8ZC/rzjDq2aGlA/OdlfOelIoaWEeJ5IKFuxIKkNjrr34QvGrZddgup4PMYBEKJDk74tr3f0dIL9tutkkkckVaPvpYDChzbtik5VnMEq2PL1d0l2QdzI+MhfbZ7HTzzlc80NZX2+JRkgpKEQ4JJogEBvCRimCOvYdsU8YLEJY6FrZgGETp8RJPh6de0fut/7VRFCAN4ogCbBgGHPq1UYgJzpFor2hq8ZRAnysQrTDtRDgQmQ69ydwVrkVjuU/0iXLsXi7vd8QuWEqkiwjEi1hCYtKgNga2G55bE2+lQ+z8+fNbWEb5Pzo373E//7kgTxgAM+S8vTKRjwbumpamkjLgsRCTdMGGVBZJ6rdJMNHtf/uZ5t9+2/5wIBURBQLkFBAipbhnURbLLLBf9PK90auegQJu0Q3MTNPgzYEJiPewncFa5Js9Sa5kyXTDxXg0Hem+IXLCUtFhGT8ITeTALyO+shxzT+kNKtboLwC7F+wTuGbq98ulLsP/SDOy/ahX00gMMCCCQZACMiMBTZo4GEgJBDyR1EzEEfyjBW8+OQkXoQLeqr+TekuYgcnq9XQzN6WRjj55dG+KOMEEy0AefyT6hb/fs0EoIghlprmvvQr4VPq8nf3TQAZ3KnHHElcBgSWACAy0SSBB5NtEzEFfEDTDx8SL0IFu616SBvkuYgcnqxS0MW70vx3enD1X6pMaRCWvIju/yE52Ex556n40Q/sQ4VmalP84bqzU6EDmY8M78FAgAFQR/NeAiRhelE6SnUesBaTBWyGwKpfFU5zgq3LdFBZzrbFBHDsNU2lBCY5BmONQ7Ghm5WjEOAm0FYLkIWj8fYrSoKh38RZmhySn3CYOqFTCoqbk7Jf/W+q/D/+5IE7AADGDVMMwYZcmgmuZpgwz5KwJMzTJhjyWqn6DWDDDXd7Dv/3of191XYf3Xe2j0+JAgABAAuNGAHNGOqRBNKUaj1FL6bCLLkNwql8VTnOC659ooLOlb5wwZjabJQQmCyDNY1D5oZ1ejEKE7Q8EbQgMI8hkKKqVBUG/iK1NDn5+UMHnFEzQZYGjEaWwAgVsIB0uKAmzbvosv1cAACqUp2MwoyULWPSaJK1XcihmYhIg/TLrq3BRFYHZXRzLPNL/KUhmCE3yJDMznZS9Wy/yJMzhZsReHcBEn2z78SiANqNiYkHRHDpx55BFRJRK5LBiX/tS9ui2kAKEZmVW2ekm2BcSTElj/dao7uc5jZWeXXVuMXYHZXj6ZkaX+XkM4rxCJDczhLA5dVjLz2Yl3M10ZsJkdyM4nbCz0pKhAyp8keIxHPMYlJJRJLjNSf/b+lAGVmmlt0c35MSbYKlVR+aB5j0x7OK+AJeqYkGOMhOZl2WsZA0JDzMgI1YnCHautYnJqRHlkcdpqgoLkIIyIyM9Lke/HqPFdK05bPimX1tc2k//uSBO0AA2Y9S8sGGfJp57lpZMNaC0zHQ6eYZOFfGul8wZX8nP0BBwKBy8GKtGhobPPAyrElLAEAEAJ2ACMBnSoLrJ9OyvlXB7OHvVg4cp4Aj6qYkGOMhOZ85dmIOiof+3T3nauthPykXlkcdpiUFBchDOoI/C6IH0eB4hnLU/yz2r9zWgccWycwz2MJfb7W/2er73b//3YAGNyNzrVvCiZMYmk/PPHvnGrHrn47vKVibCXwYncqnyJ0PEc3JcwtVVKfGyPp97cimSGRQzAEOYCTConSTgkhMUWcAczrSUcRHsE4jCIKigHKoEQ/enPIut9VgALosQrPddJP+FExMYmk/PPHv2NRDr353eIyS8KfiHujGj/xD0e7Wm5L6vVXns2R9O3tyKZIZLDMAOMCYGF33ilwjAwhQJJoLCVbXkBz6z7TtJ9JZ7ab+5GKfUoDyUSiiHvhlgFMos4QyjJsDDJIzjYUQ/jHMI/iZkfrCTOmpyKX7PWKA1OUxR/3p6Fkc8uZzQ0YmGcZIMDq0pl05UUzQb4TxTL3rHqXKxz0bR9H2v/7kgTngAMgT1Jp7BgYYWY5l2TDKEtgzU+EhHZxbBhpvJMN3IMNvQDsVGjrHJdYrjiC3HAPpBSJId2GZGFSSzSgyRk2CBmJYzicKIfxBzBP4DDIVmFhJ1zU4ilzZ9igNTmYr7LuehanPLmc0NGJhDjCIJB1cpl02eKehFzsLz3zfX5f/z55XHbthL+7zCi/3vil9o047myHioALstrkjcSTkbMrEpsZG4RUZNsIUnFn4Le1VCqg6KRNnPrQbek1IM6kI5KOh0tV2RZXM7V3nctSLIqRziF0YTtWNREb8agKbkuE6D4YUQNhqBnEA+nZXcRinsZcoB676WWStTfk8WtGKtKnpzDsdqV675lgsPapKqHFpNnPrS582KY51EMuceGXcpYpsbe93LUmypGhL2DyaxsBiM0ulw7cXcLyQlUhK4GGBxPIIXcbhCZ+spUCAIACtgxuLEjBA5SknYHZsSggWKgVsoOuYPzCi4UTNxmi+VuRbUy6tXi/Wj5sYemyuZr0tAXDBT2FoAgzAFy6qEWZFw8yjfcpYT5oX5uKJGB5JOf/+5IE6IADFlBO0wwYoGYp2cphgxZLdNtXpIyrsV+baXTBifwYEGC5gisXE15eha+rUpOMBiSSArqOWjQIGwBqF8nB42AoIHFQLMoa5g/MKLhRM8mi/1ciJqaFVqlF+tvmx27E5nel885OsLwEGYS5dWEXl8Mpof2TPvmWUzzid8pENjCCJvlNHtp+fv+T8v/9y+c+bZ6BAAAC4wQklO+IjMvyNTmV6V1LsrR46YuvQ54WCPsPMXrv2JTQyZjfdfd7VDfvc99mpvDsZ3dKe9wUMhtQq35tJS8G09b5IWfQ1o0gWFYmSioWQFqWfWj9GyoDaJRCuwGqIOvhahYuGhB5ZWjw/enhzwcEGdg9MXl34lNDJjOmeb8ZJOuXmUJyYp2t9qQYgkl8G3t5Xd//uHKuPdq90cHeLcb/7yrEffdlf/706ckt/DDf1D+MAFtlktlkbv5QF0DYXcmy21cVMbE57xvUw/sJDDmY3tSLjl1SLjRo0uSGRwsiRnXpo5lX5DJQVcKz5U1Ui/R4dXLbjy/DL+n559a0wxZVNvNMgWbr9yPf//uSBOkIAyBOzTs4GBBjTRm3ZeMCCxjPNGwMy8Frl2dc9gxBys/P8+yH/5t7l3/MggAtstlcskd3KAuijT80sWPbOY2PNSLzJwzeph/hQ8yLakXHI6pFxo0buSGR6ldGdemIdyKeYwsOEQpTiJg+zK5S2uyKjqXmodlzP/R71qd0LSk1HZlS9uZnK8rdr1v/rd21t52EAZrrttdbG7cU16jAXB8artNdWtOeMidd2DPjTupGIq9225EsRJr787uZuwK1VXoi98Ed2igm1BHEjiDIOAMg1JZgTGCKpoIPQGQzcssevfrV9O4MKScJKALcbbl2rWNR0vVO0fo3S8X2q2pxfQor0LBrxT1LSqVz24icRGxi3NIvdzIuy6Ie8fYKF3ZRYpxoqEH1ggoBROwPAGx6KosjcUUjamlP1b6PZ2UABoCQQC7ArE4LaWGln27IHjnYmjpIogQ1ZqndBBp8LExxzE3W0rCwkRFVLJR/Q1uV7uVWGGo08TcD/iZ+bMCY57JIld2qDN4i+d9Y+5WmuY656uL/66a78cyvfIkkdxW/DP/7kgTqgAMhaNJp6RgoZS0aTTxlfwrww2OjDFbxR5kp8MGVvnqsyi7It/bnfvW75skwFCQQC7ArE4LaWGlo27DDxzsmjRJoilm47oRE+FiY45ibWmVUWCIaK5ZKP6GxMzN3MrDSkTtNwPriZjfMCY57JImadqgd3pfPNrHwvV/3XOvH3HXtb+g/e7UZv4x1XDFpc9m2Yvq3P+rrvvk0Aa5NW220Vdg7xJJC4UDkeny4aWSSCVlAaCgJTmtbglMQYAjg42eMBNsXcBZRaxUXD6IfmzxYPAMDj2LUvw7dHqMuNINYbhk4GwqNCb26LU0NMiZZgrm5fLAPXWyyyxuX8NQQwmUDo9Nz41hsJ/WhICI5qHbYSmILNF2Kl7ZEtvy0s17p3svn5e5+ZtmzjE5vVhlYhA6SEj3qTQg1hkJkQwZSNGoUtJrSxjUpXK+m5SoAQkkgAgEKWJigVaApTeES0RTYUos9XMpOof6jqONhgsmIfzimCnc9d4r9/Eq5F55C3h1pntU91uS0l45PQRCj8vZTg/WLOSU+ll12z05pkVPIxQz/+5IE8AADfU7M0wZBcmxKGZdgyC5LGE9Fp5hjYVob6XTDDFwccqQXPoJusU5db69dBOUAwEAAABONFUwxC4Ew3sioEtsZRZ6uZW1D9Y3Uc+GAZSC/OKcnc9a8V+vMZXIszMnOHWmZtU91uSuS8pPQyihy+Mymw5hJyonnHCcPvOQwVa8DKHDVqreGBONsuWquu3IE5QAaWZhOOxu7CcH4B8l0gYWgobEyyOft4d7333p2sZFmaz7o3aDQjY4c6a8lrw3S3fmuXuU4ZgwTyYNpIvSprHoNkCpJYmsPkRA4CBc8XYGovYhr+IPDTrPUALJOwk3Glbg+C+AHEtyBhaCzYmVGD+TeHde++dO3Miz1h9yYrQZZscP6fxrXhunf5rSycpwzBiOTqTSokQeQEQhkhUWkGsRUfjWNb0NNx0GGIPHHAJgqY6oBLgEUiZbQTZ21sPXep7O956s4UkD2qlv/5lqqxq7HUQCIU9ok6GrZkCJipCSqM412IkdSmYrT2b+ZSXRSczKFauIXHCHhokraK1RkCAYXeBVWIBoQZpIak0ih//uSBOkAAxpLzWsGGXBiBzmaYMMuCxTLR6ewYKFjmOi09gwUEncQLJiFtjim8DJS3SoBWO1ptuJJyCK0ryaFOM/kCAG2ih//JVY1dbeJd87uJgw1amSFCpDFpHGpCiInUpm2k2b7Mol0UnPKFaZQyMAqcWZw5CzQ2WnmuzFl8nMrTGzWIVcnSBBTjoaLEjdEb0to3Oal9m9ncMAA/iOpFlQGFXXho9ZX/p/zy1nl+8fxrjD3mH3CR+oPfNsvY6WX6SRMy0On3OQoRNolMtr2w1P05HZKLC5o44mt+Qe5zXdu7jOttP/5T9QT1uwlttkluVi6gyGeecWRTfWHU2F6G+QPfNsrmdLIvSSJ0tDpy5yEhFNEzLa9sPM9OR2Rxb0UgROk1ve1A5xwJl580LCwDcXvGL1tpdvUv7BJ11lKAFTcACTZKlwUo8zTR/bHsDVPS+aLQfsimCnDznQdAg9sVCIs4RbnJFgtoYd9Dv5F7aciwi5g0Xz4q0tHEiDhoSIUry3MvoY9JZSSUre5RTJiVXLOE+UUvNSLyk7TJiP7PZ+Ts//7kgTsgANIOc3TAzTgaG0anSRiv4nQzzRMGHIBUprstFaNzoXzpHk+YIACDRHIBksbm/IyeavZ9AmkimbLljWL/VS8DxfOg6BA7Yogi84RE5xorC2zr6HfyL20wSKxEUwaKZGUVamjiRAqMhEQmV4thlks9Iv1EOf2oymIlIY0pzplF/Mk/KT/2Lvfme3P2/yqeT+CACibAJSJJbjIqQdMnRiMCz2x7Frhdb+eC0zj6UKxOhbMFYxXal4vm0Qggtyz93j74jl1MDsI0FKd5p7mXd7ufyz3PuqKdx1RXdv9icLhGQDctWmgxuzuAgQls3ctWxGv5dOWV/l9MfSqxK8zMBqgRUW8yKhR0ydsu3mx8LmK5f9Och0Xr/DiYNw2HBypusftvfYgyLs9PkybRu6zd3eDwuIy3dV673Z+7L64sLsWZKBO4gqJI7QfSnvJwlkSrP6bQtsKY6sEFCBC5GNNyhIxhEEm0uO5VYArnS45heoB+kAiiS3AwGCh6miws8pCDn89E0uwmb/JG3/0kNK1//xtyoiD1W+Pnn2ZAigOrTj/+5IE8QADUmjQae0bKGrNGj89Yx8NkNdPpK0O+b8qaWSTDuYor4C3U6ZzJqyocDIVpH3y+kaywk6pMZWKZkh+q5n5IRzh/J+ZG395r/Dvj9skAUwAiBWFgXJkbpZXLr7TKRXmh/Sk0t4qe6ikQkkrYxDlNb+iHZosLqa9Na5xxgTIKnMoaR8Rbz0zzjtRSocDIVyPvl75rLqnV2aXUckuATMYceqpc0WrUBdbT9rCS7iigE523Jc7bNv1EHUKhtvWyNOsUQIEAr3/KB9jPsETA4p74Zi0IhP7nIIpuy16xZEFFap4J6cWBmybYmFWoQ4naKvL5RI4RC4dSgSpGw+UHLInByz8UtOIQFhvpcGG+h5EBOZtyXO2zb9RB1Cobbk2vdYogQIBXv+QB9jP4ImBxT3QZtCIT+5yCKbtr1vUgorVOAnpxYGbJtiYe1CHpfk73/5j4BWd6zNyhqqyN7AZP9bHO9c5lnVzn387nf/9bdhVADbSdcs1ju+rlIdsEUzKFY77dlGTDeBJ/czRqvcai8Oe89yq7J+rDQ+OM9HZUKc4//uSBNcAAu9e1VEFH4xgaLppMOOdi/DNbaSIdrF9me20kQ7X52p9HV0cip97KQEiow8IhQef+JwE1DXrtxoqNMHUBITKhpCROykwSONCQHmqsVHNFQBBBoBKM/QWHiTHnTIFsTMbVV31eXSiAwMy4XI+5nM7uAIuGPzo5Vf/VlEwwz7sqFEzjnZEv7l//ssUiow6FYLv///I/OiL87zfPR9Z0Ksxvltf//pVQ/51A/q2uEDJHi4YCQ1OStqXQRlqkJAkgu3OUzjiFImQy5wYfwkBxvvVns7I9rOKjBalDOKsY5rPt6oquyq31SaHe0pW//2xa1VsTo6b6pVlM6EdFIhspka54436HrJJQmMMWFTd85+/GABqFSO3a2beQPNAUKAmFybuD0WtkYdxLDj/KBOJ+z1bakMyMjEhhXOHRJM7aGWhfLLTSqX/KCxWA/V1AV+/0v75EYp8sCDzSmhRZxEdAIAjzxiigdsSyQQ1ew1sACicst//2//N0PIh1k7RxekFQCtqgwe2McMMvjBSQMROul1XL56rx7HBbDpYREOXS//7kgTXAAMIMlpphyycYaZKd2WFLcvJU19HjKd5cJ7tdIOOVnc9OYAnG7Ed8I4QKHWl7fapsHc4R2tnSfvSI27DBY4wWOAA5SsFGcf+j+sAKJuSTf7bb8ToeRBYcmm2p9uiyOfvRSy+GM1/kvz3WJxQpFlPVc3rbcMj/zXDli3PhIQAnG5aL3mWYJJmmuLDxdwjSweWmIuSTKKa6wMU7Vd/roHa4+sAKpS2Xf/Xf9SYOtkdkjYo7WIObzGYX7oPS6uJ32Rae4xu4ARNcrWf8yjZHmmZV3Q5pXw64JIlOnGPpPMqf3/UsszIrrNj9y3vZsR4ToIg0sRCPCJp0V+3zHYADUZbJt9rdsNkcqTYjskNghhrEHH2cJYXUoBTfyPN+/UZPH/jmdyKrm0GuUXtj62++6m67cxo0V7CebEIyEl34KH8q4zUa7moK3TDarIXWwOHjgmATEMTUGP+tdfdUgBS6kejcgyJxOXkD2ElKd8y7pF2i1YcXDBtA6fopmelTIEMy2GVGP/8/66kRtrNC4nmVTjTaNde+XMXWlZcqn4lm3//+5IE14AC4EvcaMUabFcG+30YZp2LRUtxpIhysXocLXT0oHYmqoPSnQSVQgc3r7vy9NW13wvc1DLHgN7Vvcxb8OgAJF2S6ey7DSB6kIw5N7Lfx3yusg0wNziJ/gJFrgQQqEA5rTglX/P/0xDasOxHcaNWHcuflzenLr/PyZu+TUkFOUUGGSiQjgRv8V8+fsKr1tc2PbQMZKjTQT8OsepR6JBWAABTZSScEpKgHUFOF6gIxbzRe2O57lsysC7jExILGx2qUu/pTi8IxSdx0M9vNzy+UQDR5osQA8e//+IuOedJ65riFixwhtdU4up9+8Da5Y/IWdjz/HT3p83/54qqNe9IKlAMSmYAgFRltKQVU2ooOOTWCgmpPT2x+e6WqzGHHLjE5vrKJKussCYBw8fLHdvd/PvjCBR+iwkCB7//5Dh07oDy6XxYYwczyodRZ/el//wv8uFCNyI3kXIuSdBaU/ZfB00yD39qDGkFJdxi1pbgVc6oO97tJtqypxRqKwwjEh9hi15stAeRGV2199Nf5EDhCVBit/9UQrHms/RxNDsB//uSBOCAAwU211HpG35hSXtNMQOLzKDVV0wxDrmBpCtphA4vJWo261jJKsOZnvIyXuyvSyXqkn50Mhjsjj9fiDjbqSAEgK2HZfxi1pbgVc6oO97tFydWVOKNRWGEYkPsMWvNloDyIGRGTiPfaVv//pAoiZIRGpf//pMwMJV0aKv+B5cPQea1xXVzvjNPodXffFWNX1tpQ6nkivP5yG5PrRvV+qaBAUDJfxef9ehI1KUObdijMrj6abJlUJrSY2RF1rXIicBi4WiMZNwOwfIf6oJjA6YeNBx3/REaibiYud1qc8pTg07HmRw1g+Yq1e9P4fHGO4qZmKeVbxphzzcPAwy+f5ltsv0CBh7z/r0JGpShzbsUZlcfTTZMqhNaTGyIuta5ETgMXC0RjJuB2D5D/VBMYHTDxoOO/9P85zutTncpTg05TxZHB4fdlq96fw+KiTuKrMz72DmEFzbA9N/jyV8OZdJVBwCFLvxCG6bxdzlaIXtrEfHhLqPurHEiizXRSlKUrVFSr/1ZFcOgKC/+N/sxmebszMqrrSwoChkYUBCqFP/7kATbgBLFTdY7CSn2YKeaymEoPsxdE1jsJKfZdB9rGYSU+zECCjrNWpGe5KmTHDBPRJs3e0EgIFcAt3nc9V8rLXAYAhSb8QhXS7EjOVKkvWMm3OFXvvLOkdncSaqqTWpqr///NRLCoOiL///Df7MzN2/DWq/M+SbXsSKqaSLIQSdLUpQ1JuZrmIa0OGDZNmO/mYYiLkfPOc1mdZy7QFbIBFyOQNio/FCo+VVtZpqarteh9nXes9t0UQFQWStjFXribgcPKVwoA6rzdSIxJv6ykyk5xez0h8uR+gUgR9c++1xnVE7TB4K6Oh1TKmUyp4hKVYZY5KAK6RDMkkgXBYnBQ0TNRlJZbUIqlhtrK0zTVjFFDR2nPTLPX+XYudK2EgC+424lpjZmUdWnYykO5C1NdEf92EAFHCRpQd2U0yoQ7E1WVCZO6uOK7/igJyiqXTZpDAAq0MZSWdAqkkL3WbzrpWBxM0Vpvzl8FLkmFmgVCiGZ9jEOT+rh8YCqNYDuMU6Kdt5AEAAUdmExc7uz+lZz/2ssSHkc5xJ3QqSWkO7cTv/7kgTdAALeStY55RvmW6katzzILMsNL1lGFHMxbCXraJQVvuByGMG+2+1fP74Um0owACnJQqiOiZAVXQlzrM525WhShPi7h/LbU0ElE04mPgVOGbxudHT//uHBCcGQGcr+5f6BABD9xb+T5zksDvl/5GrwMOINwMMMhGzMSO2bEb/M//2jPbK/P7DKWRgwUlRcAalAEFN2hlL+NVLJQd8NW5nnc47mh4QmByoO+8pmhynMQtkKiSye4/8QTZW6iYNTsgZn59EAVEFCQF5EYb1MVqLymUijFazHhhmoZP6o4kI0c/cf/Px2v/fUeaGySdvJ9hvv/+rmzvz2Hd0AyoDVoVJKxQnkgBd2NW5d7j7jq9WR2/zT5tMGWpsGsLL45am7f5mZo4vyKP1rCl+6xjslt5Ycnw4GAd2Mx3jSmaRuC6Sq8zacmlFNqc/ssMIDnepft/CUWpEsMSP2vEVVJAH2zZdomXaqACcRSJQKbHBS2RDAkND4vwtD0lPqF3OeopHgxsJB4Vz9v8p8/Lftvv1OiwS0lUh5yyp+vOkqtITBLEP/+5IE5oAi5zfSswkp/mAqOjNhgz7NZPFLR5kt2aQgqSj2CjotrjQG75mRwLxtCQiEM+PDAKzNeVBILo/jgSQ7H9YvfBj//+eExRIYQEgGSGIa08A/LBIKbHAgDTQGoEBwjn+slzS3ylc9/00WwbUgQgDp3vjfvP08mTlXIxPrirAyaeQWd+xLlzbDoOst6jfLBc46fS4+5240HRf1cqE4WNXs6cOhInGbidJ+ebA/c7HP//y1hwcZAKAKMSseIgQnEUgAAARwsH1jnfRhpVRVOr97HEd+ob72fjeADNpM2pHfLGyiMJ2uKJ1VFwa1exkxIWcaPVEFvbG1uUJEZEmjHFiAZJCYkJEZO23BTwmSYiSRpMUKxxRKzf//8u0XGvz7LYxAJUzKRAABQ+kH1ksP8wTEIwFU4J9NSA7yH1nOu8IauB3WN+bcxQfAblyhibBRCZrwVA+O5ueMtsJUzB42uhbNGjguni5YeGY/n6tw42jZYiMkIzgcwRxgkQm43///+t1jF7YxH+r/rglvyrgD2A2CjhifEbvpkyDAAs/sD2Et//uSBNyAA1M0VukjYfRrpprKIM+kjJTPXaS9OFGcmmv0kbGatRQLIu7s9865fEuKQcodhAAEH4/KDaGSSOftD43CTCsPp2tVKhIXlZOdntnYc+8UeUhvWr7SyDXI9QEt+VcAewGwUcMT4jd9MmQYAFn9gewltqKBZF3dnvnXL4lxSDlDsIAAg/H5QbQySRz9ofG4SYVh9O1qpUJC8rJzs9s7Dn3ijykN61faWQa5HqAAClzMiAtryo2nEM3RY6j0uyKv27W57HVXlgtRmbTrk3thlqGfoZ1xXVxl58BR0htNs0mqpCSLi4JR+PKNfCsxZDSKm2OPPS4Jh8uxqmxIFp0QzBOkeSg0ispVWzSyy7Xpl39ep3NTMRjgmeRyZz+VE3TbOkWF240z4THSHA2zSaqoGHkhKXlWNfCxixIoZXgZw4rACDrYFGbqBUtaZAIAAHEJJlGBycZbqxgrEVD9hW/x/1zZ5oxH1xaxePp2vuIcC0qBefaPSw4MnB2541aR1WK2qNrzIvlL2nnnMTExLyqHmdWViWipQZ///9Fykl+oFf/7kgTKgzKLM9igYmQQUWZ7FAxMggnAy2PDMNUJLhlsuJYPSS1pkAgAAcQkmUYHJxluhjBWIqH7Cn245bPNGI+uLWLx9O19xDgWlQLz7R6WHBk4O3PGrSOqxW1RteZF8pe0885j6WldrFTa1+ODnvFSgz///fvqp9RJcsSIAAAI4DwwsWouhORcwyojhmkhWaka/nUkJDc0IiBoRPI6YfH+P9GQRJiaBggIQckW57i1VVmpVUnOipqmxVaTzCKF96aqLRFUZqEKkRA2gmVBox///U8qphEAizx8OBIEK2EkAAAEcHCiw1S7KSEyweURwzWAuizN8stMdutHoXDkjO8WR1maz+FkSTI+YPDghAVItz3DFVVK6OLD94EqrHzLYHzxdSGY4Vp15Wyt0hisqPC88ulBox///VqImQRUbqDiqgX60AAAAwJy5GTI0I+Vms+a1MimZHTcdZr8YxBvllbVAri9nNLAb731mv+athJlUWqwxqZxTiVxdkQ5v3Ci7wpG6ZWx4t9P3CDVcWVzm9gzag2ibtDezxYsDcKK+Jf//7f/+5IE6wACtjFY6Mw1RFYmKx0YTIKMZM1fpLE6EZEZq7SRscojME1DIsRBfrQAAADAnLkZMjQj5Waz5rVEUzI6bjrNfjGIN8sraoFcXs5pYDfe+s1/zVsJMqi1WFOpnFOJXF2RDm/cKLvCkbplbHi30/cINVxZXOb2DNqDaJu0N7PFiwNwor4l///UqhiAATUq0iY9hpktOGlkGcjf5kbGG7JYYMAVweCuBgsBsQaUrEncoMKgNBIvtdbF2TSpiajQQEiQAvYxQzAJ8OjZkL2PTxCcmQg6u4wFxcUGg4KLAgUSoCsRoGMFD0614IIRovHkoEHpgLAZqJGgkQIBDQVE3TyH2A7IVMlDS5xiYOiglsKASJKO1I1kLgTbQ4ABlHMRABgoCpNGgu+EEAkAQAimBQFZZVBFiILNKgiIoclpl7UyGWokz8/phsi/89/6tson+f//7sYUZj2GmS04aWQZyN/mRsYbslhgwBXB4K1DBYDYg0paydygwqA0Ei+2JsXZNKmJqNBASJAC9jFDMAnw6NmQvY9PDpyZCDq7lgXFxQcD//uSBO+AAzoz1dUl4ARoJnq6pLwAmKDxOBnNgAMTnicDObAAgosCBRKgKxGgYwUPTrXgghGi8eSgQemAsBmokaCRAgENBUTdPIjYDshUyUNLnGJg6KCWwoAIko7UjWQqBNtDgAGUcxEAGCgKk0aC74QQCQBACKYFAVllUEWIgs0qDIihyWmXtTIZaiTPz+mcyL/3v/VtlE//7//djCjqMvm40WaRA0TgR0MXFc5CmSsLsoMEARsoQDWmpiM4a23ZyGqPpDbBIyul/V5mAMmABLVMNcN2MXKZPUYJGYcgZog765zBgjJBnUTfMYhctOEvICaJq9YKxmsWKWmBIiHoZVYraSiEeD7qDkJQcLbCgspnK4kXXYG193WsrAqlbCrBTujmkq60idV1nip4Ydh4YGkcbeduVMqaHog67lO9E27znf///9TuX///7+qAAAAQAGnrxu6yIrU95iMmST+YMrEXAMEAHxDAFxVhHIct6n8bJEJW0yMstf1mYEIllGSmCnGxEMpMbaABswYoygqH2VlkjGAn4WWYYy/63UbArDNHbP/7kgSBgAVrO1AGc0ACrQdqEs3oAIpQy19dhoARPJjsK56wAh080SJXIFFiPEY9IwEqhE5DyoDdHQgG9KYTD5XKkE7S3fd1wWYrtelalt9c03X6o35gaAq8ohiAYtJ6eB3itLujUoft3n+lbxznf///9UWX///8Oo8u3aASyAAALHFSOpnRkWQvbVvHe9fc5q9szZH1KMSWEmGSaEoZHGSLUl9Z4yHCdD4Zj6JQamSKDqRZD/1aa1vdSn1tSSUqvrRRPgd///9RyRvalcWQATSAAITHEBci+YQKBJsXrnOPC1ibKdN/4hE2BKECw45SoXdP81A8yFDyBBMOpNQtyNMruOP+PfM33Ci8S1azzkHRNXSLQ2B3///7pHJ16AAqwAACX4yiKIlIBaNolT7J+FBtWSMZm7P5qR/sBuGLINahF+x6NCo6H6GgQl6p+Cfxx3/x87ZVP3fFHTRppbV3OWzCZj/IKJVmRLpKUmFHi1F1NgANQAAAr8ZRFESkAtG0Sp9y8KDct1LvP5qR/sBuGLINahF+x6NCo6H6GgQl6p+Cfxz/+5IEQoACoSxXUwZZNFGEyupgyyqKQJ9fTCXp0UYT7CmHiXp3/x87ZVPm1AEIhkNur4TMf5B45GEzrmFJcSCKZFkdLmAHWgAAS+BXqNlpRl725T9ix1dkjmk5bbz/31QoQEobTZWafufaqFRGo52YOJGLh3ViiSb+v//8+emr4fRGRjvAQFxiAqhnAgK/4SVxWSceDx1wB1oAAEv8V6j40oyuDsrd6x9Xdn95Jrbzr/5+VYwPVXLNaNPvX38UxEakPZhvKhcO6vhAJ7f45GdBQwABnCIC4xAVQzgQFf8WUnFXtjzTVav1VQD6kAAKf4NiZZA2k3kuRmm3SDVLuJr+95gVl6VqH3oVH+00UG8lgQgNh6IEzMTqplzDv/6uuotiKU8dxa4Kgsg6TMkA2c/3oq9lSl+2xCkJAOGAADAoemaFdC0YkfVeYLSUyCKCM2qZbXV58F3Juiozcj364mpql9QUMXQk5PwC0BXDVEBQR7o1coDWYUL51851vWK0j0YX1Sm5UoK/+64hSAAORsgdIwaNyYgl2GEIeEaSWYQ86039//uSBFyBAoAq2FAvWBRR5OqpZG86SiSvYWeZZNFCFew08yya09IEhRxe5BjviumlcECTlQEHQNVDUmsdznPVbfO8W9Y4i2aYharKVa08HQwDUn/uewahPmVk9+ZlrAAHImQACU+BSMGjcmIJdhhCHhGkq151pvm6ekCQo4vcgx3xXTSuCBJyoCDoGqhqTWO5zn62+d4t6xxFs0xC1WUq1p4OhgGpP/MlmAqVT3jZ9QC7UABKnAqbL1Zv4Vnwo5b23nNYmEBvOvT434khZGQxDKHMc66rlrCkSD6B4Ex5tQrL0nXKNfVSxikQxhUaLHGbkLSRppk0vC6g7/RD6WG2sUkAuVAASpwDBYWLJjmAzqIOdTSda0WnnW1Cp5ocFYFwUYlJalrf3TtaJRYWQmHS67hNHnVrm5/mPEFM0QKIKQj0Zpxo1BtzZeIn/xce8ckSt3lQAIoiAAAU/xqlRpsMKKFcjFm6jM5w0kvW1fn1DuFFUpdFbYmv87jvolAZs1opM6nmv/54tyNZbafFKTd1Cr/Y7/jQwkWALGOk3KlefQ/Jqf/7kgR5gAKQK1fTD1l0UYVa+kWGpIn8sV+sNWlRS5ZrtYaVemABE0QAACn+NUosGwtoolhGJ27j6c4aR51tX584MYMpWTUlMpFS281jnYkg3XLrD6GstW/WjmCLQUeE6MMV3ZBrpzKkf9wWWPYLIEldt0ZwHjcHlQANAAACeAm6EFfSyVEYVBud402FJZA+atbOZYzxiopKQw6LF+YoKu3aLjXl9y+1Vh/vGGKk3EoaBbRCriKAeIvABEPj4DMkyP/Agsko5jytlYABMIAAALnwr3Q61isTa5F7P68rYUrZZzWz5Nx9aVFjwGIG5JU2vqPnkbaNCWxFpiNoep/+XtSwRkTtDbJI7m18nIXRz/8yyHULIlTqo1rrf+oAARIgAgl34V7YeGytBIKZuuzU05TOIF+jTp+WMQJA1hmNPbkqhJb1xlwFxOxCKo+hj93/f/TVFtfcsHkz5UdV5Cgqd5uMdzcWfAR0sPFhqv5jrALpQEIvfivbEq3mAKSmbptLWemsgf1d006WQgaEOGY09udUJt64zgLidkRVH0JC7t+O/+n/+5IElQACiRxVUyt5ZFHmOt1hY16KWMFbrC0H0VQY66mFoPqqLa+5YUn9v/0VVFapl5gQradaeL6GTwuhwpUQyaDG9ykA6lAQ07+KK4BXdg4Hksi3Dz93ymZ+pvbfEjgGcMzHdiU//jXFDroyg8IwMy3TZj1oNnfLOA0/NKX6wgQGLpFq1dQgDzJYXaMWVJSfaYiytAACjRICDUv4o+BbuwoDyWRbqzu+UzP1N7b+qJnDMx3FEou/8a4odcmW6NTLdON62s75Zgg6fsjie4kEJBAZ0q/UIGoOFsxKkpOLZjVoAAUQIAjPmECo4k6IiG6f7s55lXzlzGKuOCEHQaEtrTsz39xAIhRCurGep3f+qHUUBA40Rs5PMX9HHW7INQO//93YRSGRf2w+ytfNEnvaF79wmqEAQU7+HzCBUcSdERDdP92c/Tm/afRBtkBkFwWD+us7M9/cQCIUQrqxnqd3/qh1FAQONEbOTzF/Rxy+yDWI3/ZaoiIMOCdZA2w6E1Urh+kwNMoAvkAQUr+N2CAc0xtEiV08bMGLOe5lCmmXi7Ir//uSBK6BgoEy19HmGmRM5lsNPMM+ikjXXYecq1lFoGuo9BVqqIGlh0Bv6x3MqunrhEHQhmcu9T/8kQKIu6VL//xc4mfIxvzilGhYQOPSYyVrhZTjK0iaZAAcbIAISe/GYgMmEVYTD+OrZTQMc9EXS5OZeKciu5A0sOg/+v3Mqv9cIi6EdnDqocqi/f5IgURd0qVvX/g4fD4uci4b8MCyqGT0m7jZS6tIs4zIAAKk/GeaIXRkpQivJjW6iaRqDXjieYVE7wF8ioOMDvkke9SqFCwgiDzPllfpSyuIFFBVj//kJnOcEHgt/oQWOjWm3pYL1MFF3qSg5SoAuUAAVL+M80QujJShCZGNbqJpGoNd1RPMKid4C+GSGEhv6aX3usjDiIge1+ut/xx1NkGmG0///9+ie9uBANmnUU+tQLuBmLwNAyVij9pHeMa6RgWAAkm4agIwDXpJMxq2SP++05e3h1kW/D1CtmDGViHXEQNZIhEcGWuD1PqGLCJEx8sEDApkZAdkGXcWPS/7+ItKVC1Iz///3/HwgqS1CFcYSO/7+17PaP/7kgTNCAJ+MtfTDCn0UmZ7DT2FPom8yV9MLKfRSxnr6YWgumGOXY5OAgAABKKjdjHIWEkFsSwvg1+NXrX2sJvG5R/X5Wps5TInYa9XJhC3m6ixTgyNeBjK22JmIZmMyfdYBS0JmgFIhiBl0cJPz0SJKMFRIN//poQaHxgKYKJ//2frjLaqn1gE1AAAQBhBkrM0iPiLNSMyx67ErTKHMHUFspq8TWFxteSjACaQwwRUtoNiIZq1MzaXUXUXilC6wQXuEsmzmP//4/mlCySZLL4/5v4rk3RG82HeQiRw9///qtGOeaFgDqgABII4kk2AhpsUhZrVc6hfatelu6vMaD+Z2OamO0EgpFMow8LUaqmFiCTTkWFnd3rEjWhWch0qIxxhP9E6sUIKNKP+vRqkzRHJAPAoKiB7///A4khgJHzxtvF6AFLAAEADCNTxvGywcpaHLSsKV0EXytfN46f2nmNRy9yX2LbQYiQpwl0GK5qMzrwkCMK7TPlK2clyz5ioPGdAGm/9jAHEi3u3Z5EGIoeMKCwiHSCxP//+guB7RGtsuVT/+5IE6oAC6DNVOy9Z9F2mWnNp5bKLvM1VTL1p0XQZqymUnsqAQAAACh3pEbpDDgpLTjfkrJTdBF8pdnH96pc9fUnMYvYn0mXRGVbQVoIs5lYOQrxJC8BL2Fmz0rMxRcFjUwNNS2kXr//3//FYE9RRpq9d/bnzYo4pYqkSExUhUJgh///722Ek59zwCACh4X1OKWeILNSg3SDyOWQFFsp3cd1um7lugvba7lAMmeBWBfw4EFhEGpgva7IFxKB0xx0a6qrFSJQKhhM2rf/LHj1Br2kzkB9eu3ihUIQqXnJtcNf//9agFACmpxRQowAIgjCOyk5Ro8KNlAdgiHiN0bcXtdMs27eTWVDttdLgvB5CQCMNZLVlOi8kJTI8d/U1BSGEzZre9dpA8VUGtfQk7KeyligWJhCCR4a///0hNQmaq0PrepQJVQACioAAkU9hM0gvGNlQJM+UqWUUlYse6/eAqWNlqut4hWzFgDCeCmKWVNvY7q7Wyi6CDlF5ZRCyK//1D1t6tqMBnqVg4ArAaHBYCP/zrwKhLTa0y6DFH3XaQBiw//uSBO6Cgvkz1NMpLZRmZmpnaYmwjCTLTO0dltFmGWpdl5z6ARJewjtIJgw2VBSZuMpqYQBS2c/u51H5r0uM13VbLdmoqtSJSRG690ttuDM0bKLIEcoHKpSWS//1ApM1fQVqViALAVA0JHs/uesglbVpNDUBg46RGJoUlqEg0ACIdoepS8TZMTBQEWxP4IQdl6LduxWfjtSj7PY7/srvU9yUFUOnAOjpVBLKb7d5Rj369P9qzorBNwQoUYRS7X+wCCIO3//Q0SEwCCY6YPV+ZAoHDgdXEtcikrEC2V7HA0ACIdoepS8TZMTBQEWxP4IQdl6LduxWfjtSj7PY7/srvU9yUFUOnAOjpVBLKb7d5Rj3den+1Z0Vgm4IUKMIptf7AIIg7f/9DRITAIFUSqjVt2KNE0+ZWNFjqnvqOBJj2wASSgQAAoG0TC5TwDHAMJSFoS2RJlTsrgWkL8hY6HQNfPJmpBxuAAMiwEaLLDIYpVEaYCDJw4JTslbjTUAY3r9FzIQUFUHf/zUWLlAP///kHAq9ANRxA+I8vjI0qMHVNVaWAP/7kgTtAALKMtXrDyn0XCZaqmRJuIwYy0ztCTjRjhmpnaEnGiCAEpl7C4+oLciAUkHjnpHhs4bRry/d9s9FT8f63pnagvXws6jN9U9bYJPd5C92RVuVOoIx3r9Fq6GEBqhb/+dRUJmgBnf////99mmlIJ1pMq6ayZLci7LMtjuu6AAQNwusKARzcOmMYrpGNBQVIzGAqgcSBJDfxTMpF9SyVPNXpbPKeUckKYQyLKBhy0GKw0aOKs9OcAaAUi5ZlVibnpp4SSTXCX//R0KETWa7f/ooQjQSiBUMf+ADC0i3MZaWHTEPkbUAkABZVoh9lBt1r/AmgXwGQKCmpZFJ3vHkwj2FNCs6uP54fZfpO5/yIUPBkoJbI33A0UeFSpHbp2gX/+5h6BAClFWIYU//RQFEBYECZHf7UGHBsDsJrKhNgqesfMAu+SSE6g6AltLYRNR0U9LUA5EywTUZY0DeoT/FYt0Hd/HxvNnJlX0iPNJF/gNcEQDUqH2Kj3qYkPhqv0dKEOe7HEgsykb+pu2JIEIfBDP//8hEBlKrOpmFTslNTCj/+5IE7wQC+D1Su1Eq1l2mOppl507M+MlAbaT2QYOZaZ2hlsqPKVZx9GQOz8dN6MOAATJtDxodQt0/Jhij2EgIuIrPSjvKaN6qzucLztz9TPdqRP69LdUa27LnmI/KUE8cn0sX37Jlieb/jjhD37pw6As5yK4/N+JIEIfFDP//8iOLFEV2HYnywLpJf7HfmQsn8/5gAgAALlmwjMQNzZwK7YiMMGgzr2SN1+mTcvzuqAybC7eSFI+yAQEgLLIDC9z2Fv3Lg1XiXxRYpDke2+9TsyLmAyj5NJVm9MsdlEkPUomA1EERoOIT8Y4WKSSkDBDu7o/o1pTyB5RsuimBWyjS5CXba6oANRAVLt+KWoW1vCpYdTcX7ZkEjdf7su+b9PaMmwu3nQm8IEROtBidz2Fv3Lg18iXxRY0GN7v99b62GR6gNPKkuf/+8XesNytcYCSWTSMY/x3hExrYgMFM5+7x64xf4/+retJtMPC/atFbBIqLlgmqmgIAByPYZSIW6ecvCmcDKo3VL3c6LnWX23mV6M1GQ5qSqJjXK6YQV+klHUnL//uSBOsAAxpMVLsPKfZkR+pnZQW2zjD/VUwlq9HCICuphL36azWSbWpGtVQYTSqxTff/Mben2IpPfdf+rXy74Kh6kgB2hQTyAYo/15dIwQUIZYSOsXEi2N2N1AAyACZrPxlfHlT67W+ETqfGjuroXe1d9JgtAWI2KpfuRuC26ZfU+imKtNXOcVPMyipbHTxNe/rOMa0Tc3DCMGUz6HzrdJ1KJw7o+D1EIS4+KOP9fjA5KoZPBpoIFUNQ5L4qgKzIAMb/E1DKOD6CnKARmgaTWLalm8XwvI51PKdedJ9oflI+LwzYWoIVTlcaJgnJvBIHf/yWM04RxLqf/6Fh3WLFDj9P/7GClNrqBKaL68nLjup6Q7kchqIuBAAGN/iahlHB9BTlAIzQNJrFtSzeL4Xkc6nlOvOk+0PykfF4ZrLUEKpyuNEwTk3gkDv/7KxmY4RxJlP/9Gh3WLFDj9P/5UClFxdglqL/7t9Xu2s8/O3ebgApUBSrn4spDFHYDwMRDWBmzR/FY1so7OUgRN7JcMrhUWLdy3WnHxjV1Y3WwqEpTf+pqP/7kgTVDAMXM9U7L1n0ZMZ66mGNToro91psPEfZX57rXYeI+/Pj05Bxx3/51JQ0BosFo+13/28nCKgBHw8aHGQRE0+HVUvxmgGABMb3Fp0Feo6FGFmMthgnVyPma59t+9rCS/9jsBp2Eo2L2xLqlWuqxPNL89FB9K///6xe+VwxFk///+zjKEigKmCY/b//9f48yzF+6uM5uCfDnTxD+1eiMPLYgF+gE/vvxU9h4nshKUV0eFG3eni+Jj/GgkUW0W4zq3zytQx9at5uPyXiANXMWNUkFIAy+zOezWtmr3fq2rnQtZ8JiYrRxOYw2Ml32tSPe+bVnrfOsPiEGg+shKTTFQcp/tQAVSASu9/FTODJKovJqIdHhOW768XxMesIDYYNhHkKWJrxXGWZQ24KvX9tOa+6pmRr4jF4GHidIblujq6v82utZDJIinIpKsZQXPfbKffpm1Z77TqEryv2f+UjiIcAgmi0mmdo1roA7oAn9r+NFE0pg3Ua+cxO58amSyMdt5eU0FWCVKu6fNTfUx/Ji7MhicVKUTj0ntZKDatu6T7/+5IE2IACujzXUek6dFvmWrdhiD7MQPNfR5mP0ZWf62j0MfpyJfNOqJhv/9nFT5AGtXUPuO/n/i/ubjdTfdU8q2pDMnydYzg7frv6mAO6AJ7a/jRRNKYN1GvnMTufGpksjHbeXlNBVglSruXzU31MfyJWxMGqkaqPEqlEL2u0nmRLzVpEjF+OvMGnhIK1dQ9xV/PX9+63G3/FTuS0uNDqhZDBx02xOsABRtEBK678YFxoJhVNV8cT6KtCSMdqqFZKIGCgiObFPZP5M0YYrcvvU/0XMM2PRpV/Wrv89f/9kKaAZIv5Mb0WesTRwM2vPKIzrinSgPnXvwR/5G0//76AX0gStt/Eg0kAWR+o1XzuMd1X4ppT1jXEFU4gWJGGWdUnXdR7f6cMw4KDr3p2W7alz6yx89parN8vABkQMKB5WNl2E6lU89W8id1tw3DhE49HfyC2qNmWv/xu1QC/gDdu34ahsUGoDKgmfqi85iufEVyHjvqPLOoHx+4pnxv3/5RrCKep0Qp2r93Z2z9/LPqN69YhGso8s/cuuaCBZwW9fmtN//uSBNoAAvZEV1HmWfZaiJrqPMg+ivznXaYIy9lsI+so9A272xm9HZNX4J1Zl07u0Lmf41T6TNfmAPqASlt/C8FFAKIYqw/Y9cnlFHe/Dep4e9Q4XWkI3X5hp8+/xYCOsphBrJ/ygl+4UFTEueZEnCeuZOeFCCwwHf1POpcsvjgjHMynS9iSrFNH6GJ2Tu3D4tzGOTMSAA4gEAi2nILlEbRI0Nj1ZmyVUw2KERH9tDD4t4pCSZSCGOTVelfW/3celDrUPgAj6dW/WhhlFnnpS/6aVtW1XfLhgBQQhEHxIuP5SnavnrjuHWPqBzEfUpH+AFWpnddzGBQ0AWggRUd2FwhQrAK49KreqJslVMNihBBB3TYxh8XZhwQiTKQQxyar0r63+7j0od5wef/z1oYZRZ56VX+mlbVtV3y4YAUEIwTxIuJ903hOp6bmyrF89Sktg1feBGzlD//Bgz6FFQACaZB8oVgbiDmNHjUpimWqQpl2mRdZ7p9pIyOCQGCuEkFJZ/7B1vnJ6eD0Tqy8SiKDW39abF0NshoW58wNFmqBdIJAJf/7kgTigALfNdbR7DAWXQpauj0jPsxhCVunmQu5ip9qaPMhcwxJIvEiamI8SIHML7pudav/90WugmaGJuXTRGLSkFdvYpRzqVLGYXJyA4AKz2HXADkJGn4yHeNSlplNqkD0hebaFUfEdj81mRShBoCQBkNkqBzWf+ZG+86PVyOSOUy8SiKDW39abF0NshoWbNiowOmrmJBIBIGJJF4kTUkSwiBzC+mm51q//mX0HQWfMUHopOnY1RWnQpqVUhZM83VfQLJHBBdn47HiICyDsGXChUm330Rmyji4nXpMTzPwhCfOlxCxFiV7mEaNJzm1a1beUnFvctPxf//M4CYLG1b332m6mcuVG9C2IGhibmgD0PBLCsNfqQWih4iaSiyYa5L07yqwAXZ+Ox4iAsg7BlwoVJsB6IzZRxcTr0mJ5n4QhPnS4hYixK9zTfFVba1beUnFvctPxf//M4CYLG1cvvtN1M5cqN6FwgaGJuaAPQ8EtzSv9UCiwUUkaaAJKhJIDjmvSOeG1QAgAACbn408TLXiIgnCGJo0wx+xJRlI0Tlo7GL/+5IE4gDDgULVuwZq/nbp2nNhLV7L8M1abCVp0YMZ602ErPrBavVvU0rZwYtTaVWpCNxVLz8WjT69Rp80irf//XZMKW37z0H2FCL3/zX7mmBQGxxB/+63aF+sZfd+uWACAQAJufjTxMteIiCcIYmjTDH7ElGUjROWjsYsFq9Vv830PuDGrdU3wQjcVS8/Fo0+vUOnzSG2//+uyYUtv3noPsKEXv/mv3NMCgTHEL/8X1varJCug39aVgAFPgaY09zc0iobOu4qGTd5mEPHolVKjAlrRCcK7u9j63W6QiaG3tG9Q2SWl3pJWg16zY87qjWNo55WXmP+jCoz2xuTExQVhuY7Gc1mQJDr/6xqJM4K40msDgAJTge0aFOmoNDZvrLQ47/F0UR6LKlVAnLRZwruOrY9PW641oj9o3sriCorogrRbltiDePlRfvnvZ/38MIJCuNvm7zCEy/ZdV/VUwNDp7+udHHaRRlKFQCAApT8bmmz3UaMzETELDgSVQdlBzINB8ISQ0zqXrK2kcwxovw8xritFide+ZLLct9GiKFO3/U8//uQBM0EQrwzV9MsWjRZZnr6ZYtMi0DJWGw869FZmStNh60yn3HM398U3OnkmtQcS33X/tRA+eOI/ipO5AmChpBinxRQBAAUp+NzTZ7qNGZiJiFhwJKoOyg5kGg+EJIaZ1L1lbSOYY0X4eY1xWjhOvfMlluW+jRFCnb/qeT7jmb++KbnTyjWoOJb7r/2ogfPM/1HWmrXsoZVNcUuYmgAbAqwGq54kT6hprtcEEUMtLg6PqtT2XvJYn3v1uU1m5hrtd+vp8x0OjcsydFhtg6BSItU0go0yF7h7RFWNb6/q3a9s44b1w89JWdJJmqdv+boq4gA2BVgNVzxIn1DTXa4IIoZaXB0fVansveSxPvfrcprNzDXa79fT1x0OjcsydFhtg6BSItU0go0yF7h7RFWNb6/q3a9sqOG9cPPSVnSSZqnb/m6KuIqAMAAUvx8FxiZVil5kVGE+3zbDuyqDJq/01E/fEy5bupCils/BoEZJQKoGlm6i5FI4sOUdfDWQPGH//421RK9pvmekFQwYIzh6PdOnQV/2DqMowshyVZKi3Qy//uSBNqIgsUy17sJW1RZZmr3YStqinzLVkyldglRGWrJlK7BAgAATsPbG5ERDhP2cvP4JHU+qjXnRoloO3q6jf96liWrR5KsdvUY9AvzlQFU1BRsdKPxEGJgIbI3e1aITKC9///fZj5+z7+/yEQJBKxq3WuBX/iibFap1KdHaAAAaBakKWEJIqXFOt1H0R+oGJkvMMAEaE47UoZzVh/OrjU38/hK9vxddR7mbFA/q2Ej+UTxFOSYb8i1rRlUfem20kf4DZ8qefEHRyWZ/m1Rj5aeo4Tbp/EhL/V9zu8IYZSFWiQkzrunGupqK4o0KGwMjoTgThzMBowWe9awM+PRjyoMoQljGB59WwlXzCouoy815HW5IVR96XLQPwVV1D10KYuzDvdszrG2Lo58D//5Il3///N3IVf0VQAABQPWASRjwdGyc+C0KzODIxUP3fKrQGphghKQINIxO5Kwo/pJFvNMqTWO0QapvL5fY0JKTubgI2rxAGAmKPhxLO7yVYdnrnH+f6K0hbXPesSyOJbE2mBDBEwz0nv+/cMvHUAHAAAO4//7kgTsCOLYLtc7DEPEWaXas2HmaovMu1JsvTZRbJmqjZehst4Gm1kI37BbWTl8SaLP5eVSBwaQSUglQHSOCrT80gdvFylBLoaSdQnNrIvjcF68PGB5V36S9GHXEK/ys11bZKsjqTUSDB4Eek9/vW7zWS5wKwowzCw5QHWFgKrPkk7YIANwMbESsCzgWAsLQgGRgKhw8eTUobQp8Tff3h9qjAzoY8C0siRrhptNhKSmoqg4kIO9pzXg8pfB4cinXXpMYhZNxE1JkXk25hRjf/lhN/+4s9y+WFyQWwqt2VjtlwHQMfhSwQXI0s/TMGRgdnDx5SK20LeJvv7xC1R4/TjwWWIua4d4mwlMoBmFeUB3u814TKXweIIp116T5WTcR1I6Lyd0Yxv/yzr/+oRfnclkrXs1XuUAAA0DGAxItDZhx6qJk/KiAhwCSPQs0a+aUU96EPVHiePCj7zljwuv2WETlTAnFCeqW2jvBhF6wJwyB8BuuJOMR8p9oReIZt1UPv5NrMHcC/Z6T1maXvZqF0BmS+f/+WGA///bdjgIxeMOIn3/+5IE846jHS7UGy9LdF2Fusdhh2yLsMlSbT0NkW+ZKo2XobLMLFUnTG41AkF3ISL8BJEZ2ZaIN+ZD1R4nc30feZVPhJfssIelTAIiEmagtnl4MIvWA2FQDgCJcR6MR8nOsHfhvK6KdPv4GpZAIuC+s0k9Zml72ahckMrI8///wwPBGFx4U+KoWEkROCViiEorXXwPOyEmUisOVS6u7zG+PB1atp+rmYJEm7z1p39yzWTCPBJF/fHrGv0jOvxJVZR98fGPhqsPn1FpFlQHKdSfswO3///9aNqbELmF4iQkRAijzEfpWuDXiu9GQXFeUe8flvaz9mu0wWbcLvdQqvneTqUQAqNeO5vXmWeOGugROiGGEPtXEBWn+ihnTLI2mhRXenk8R0DboI6ns0h9ik4rARqXJF//0eqaT0FjGKAElQaMFlgwAS/KGcYFkG2ZGCgtdrkqkshr2vpe3eXL17fKmUEx1mK34jlelvwxfa4q1uiavRJVohJFKbJ6ZHVYJRMJuvDzP9ZbHKs0b8ryyphKePTEF4wtPf/9bKzSegsYxQAk//uSBPUO4y4y05tPS3RjhlphbeluSyjLVGy8rdFzmWnBl6G4qDRgssGACX5QzjAsg2zIwUFrtclUlkNe19L27y5evb5UygmOsxW/Ecr0t+GL7LFWt0TV6JKtEJIpTZPTI6rBKJhN14eZ/rLY5VmjflfsqYSnj7EF4wtPf/9bKzZYCGDBygALGInLuiW1c49XQaD18LCwCG3NWPFnLrLdmtpJtQGZnXDmuTzFXOVs0ZqMI6DOA8KgEWe4ANONChQR7TFZAMo+kNPJ7nH+c4q54NEv2aSklAsCghMasK//8EqDTZYCGDBygALGInLuiW1c49XQaD18LCwCG3NWPFnLrLdmtpJtQGZnXDmuTzFXOVs0ZqMI6DOA4KgEWe4ANMtChQR7TFZAMo+kNPJ7nH+c4q54NEv2aSklAsCghMasK//8EqDVssDDEpyzBydBGYxumUhKjwGDhUZMRJntdxvVw2NSxF9GGmdY/8eWrpRHQQJYeTZsuC7yFUHQ0K4qAKRWC27iMzPVWmKjl09t9fPh0ig7mxdYe3maKH9EpNX//KNGLv/7kgT0D/MVMtMDeEtwYkZaYG8JbgxIzUoNPM3BiRmpQaeZuMsDDEpyzBydBGYxumUhKjwGDhUZMRJntdxvVw2NSw99GGmdY/8eWrpRHQQJYeTXsuC7yFUHQ0K4qAKRWC27iMzPVWmKjl09t9fPh0ig7mxdYe3maKH9EpNX//KNGLCAAUcBlAIKMWRoARAxS6C5brKRRdNwThCcbFFyo/ZrLjMWu0ZebfdevchagxW/nJRLGwMIQNxzEV/pFUGhCIwWyv//jRaXnVZGqaHhMEg2IHL/NqJFP/0iFZeScHWBkKDQOEAApYDKIhAyyjQiWMTwlXAyvU/TWDZAqNqDSNWzWXGYtdpV5uOr36QtRxW/qSiWNh4hCWOYiv9JtDQ9E4jbf//Gi1XOtSNVQ8JgkWSq/x4qGDT/+phbEjnnDj2A+gAAIAAJNglDAiEW4Qo6T0HNZuTCd6WxcQgDqcwqA/TFuJyT/rI0fUrNHjWWQ0EZOtfgvBGVhPXwkWhGEG3ZVtZx0v1z5mf7TGcyu4gca4mLB9zl+gGDRV//bjkjLnegAAD/+5IE7giC7jNSg28zcF3GalBt5m4MRM1O7TENkXQaKh2mIbKAACTQJQwIhFuEKOk9BzWbkwnelsXEIAqnMKgP0xbick/6yNH1KzR41lkNBGTrX4LwRlYCxcaCKAkAK1QpYpQdC8mX///1szNN2Q5dlnC9uv/wHwnNv/2ujjlhlwsJLASqlAII9BAUCRYAB84IaQsZOjw/4JH5wxVW6zLKutqTm9DSF0rHYCUHnses2JTYIJF613I5lxWonJCU6f/dD+56uUQiaJAwb/GwLREMd//+WcyL+sxNsEUNIAA2+BWxQTSQkJDQwKCznCF+P2yiNgLHkJR6k8iOpScTU1KlFgKj+4bkGxYKCHWov4+pUkSf/3UuOH8mPqTHkIkQkFpv8bAtCAxz//+WW2Lj43cdJ8YRAAAkABI4CB4KMMRjw6WEujMTi6VzjA1R5dbwzEqrxW/VfiR12N20FoVbtB0ViE7DHlKLAMEIBZPdOztdFrdli10tsxfMz6nGDFRSoLDzCg5VBBhnf3IKAcQGJb//KcsVGk+UynUSawGAMAAo0B14//uSBO+KwvYz01NMK3RfZnpqaYhqi3DzTu2w69lZHiqNpB17KMUXjwyeExi+Tp+1MxgmqkpuYLYpotFNPlz2rNbgZzaBClW4tEIZy3MrXPSBAYCSHsN9D4qvU76aqORIhJQbVdv///+1Xq8qT5MOtMcSlu//6gME6iTHflQmg+efzrkBESWAjsaAKUmEZguq+hsdByaRo1QyclR/tRx4judrY/rX/GMRNd8pzfftr+aSzGu2Q/z/Fgc3JmZK7InEBMRFYlKv9mVTza2V5ZM4u+9zAoKHB+hn//lnPlb+poj3MnKgSAA2uBM0wVFRIND5ZQZvI1shIkP2+0ruD/D13O/Rb8QoytxLHt6r6hxumcHsewcHzxsc9WQYpZ3//9Dpl13hZaDI4YTj/ubQJCLF4T/////HD3tZl60ua4mUdtINAEAAkbRcfcqH3TR0N2hTCAHgrDPIFS9o6zKtLvdJ8RZ42v/B0+hRLWQxJIXH1vFurcuQ4icYfQj9gWS1EqaLUi////20q3/+92MN8TEtyMC5w4AogKvT/4qQO22vX8qn3P/7kgT2ioNBP1NTTCt2Z2ZqV2npXovc+07svOvZdqQqqYWhssAAALcJAMj/Gq6ideCAceHhjD5WF99jcu+Zv9qUdSdkU9Z7qxkJgIEhd/luBg5dBFKTFFjIb/dRiP67sYb4mJblwLnDgCiAq9P/86Smkr697VY6/YBQZAAINohiRBQIi2ysEWAMIBIgzol7C3YsDjEhS+47+K/7FFi/Od22xwn6/AJESdzaon+5WRmJERCEouVySr9pUTChABggAg//rp//zqjCSgwTUwt/zFLoxKevSpKy99UaDAABBoEMSIECiYOucAaDEAAKMNKNeQEgxIW5DBPPb9/Ff7Yor3y/dtv4TOvwBhDHZ2qJ/uVkVw5hmHCb7a5JVjTZRMKEACAQBBf/rp//zqZhJQYJjTC3/FLEICBdiUuAagACpAABOwtQUKIP5Ahg0rMc8z4GLuwXUk0Fw1K6d7jKFq35t/wm96yzZMDxPuQuXgcYgQlyEiIBUsotMsbDGHmP////85AQQPKKAg3/21qKUl2RYxTc1g2xdd4AAkAAAGgS5sQWTNf/+5IE74iDDDNTu09K9FUGev1gqZqMDM1NTTyr0YIZqWmnlXobQyMkaAK1mlfJRsMMAIexu7tL15Rir8qu/Zmm/GdHSstBcJcbrm2xgpHjg4j2OI9EgGx0cLrj0RwocNHJmf///nmEBw+aQDA6e/1mSZVhJiS4pFzAEAbC8AqZCSAIwMxCpMrBxocES2GATUDCREmAYuoaa07uLWn1f//y3ioyOYIyTGPRn8lPiJZnJxcRZvRgUEx3AuLnDLscCyUKXZdb7f//7iwwofAoeiQEAETR/76/2pMAKIJgAZQCyRLcw3sBNx4QMQUrnsMCHTxl6+UFO7ixYlZr//+W9VROepQpZMP/inxNZ+clx3Q1gZJ5zHS7udyMcT1W4ut////uYYwmHRaJCgCtd///6kF10s2DqgAAcAAAFoj9QVHEUB5QvTGvSf4jjkwV9AaRRIk8YNaDJrONvHkbH/8btNI6JLxerPvX86qngh0QgZxSOImoPx7k49ocfSvWFRFxd///6sCAIYKoUXUDZ/yaGKvc4yYUlXxY0n2AAAAOoEAFbivg//uSBPKKotcyVNMpK+RgxkpqaYdui9zJSG28rdFvGOmdp5W6osUEnRnpGJ6x27UZQkeEEdEHcd1rzgwPmpnJemPPZLF6n/9Mxvfo6YCxpEJIMDYvjYYJkCDT///9WQhHjVHScfBejf//OWf6ZymdvO2HHFo0CQ6Cg/UAoAbhKImQoFILggn8HTTwWbcnxwUec5JUtiurQp6K6n/+Y3tBiw2O8d9vH/hbhBcvKBdy3C1tO6P2xIs91b82p///UeEfFGYcaIFf//+lpzelvRWXORGLVIHZqmaw2csA4AACDsKSVkIxRLDIXpDkr4iPf30lSIjlCFoWjih+LR/y2ymuW7i1/6TnAWJogCzOphJy10C+cH03ckdKh///qJ+c2SWVLf/jBGNXFXXjBYJWIaLOI/NmqgAAcAAAAo7FREmJqiq4VolsndGTLvTyqY8wlsFtLU2uGbYtrNv/LvzYYj8SFknErJfHzkoCnoMOfBQAKyOhmJmGIc7Y2mr9xIo///0QRBYTHBg5wmHx81Zr///7s/YtIAAEAB11QokEUwIwEAw5g//7kgT2CoMjMlNTTxN0X8k6vWGHXowBK1GsvO3ZYJlqtZQ1qoQukQmbEoUOgY0wQ62N84Mjs5vL+83l//Jb/0WoCZQ3S03eWYzFfD94puJx4F95FpNwFDzQpWqDdctdB8Tc6rG4FEoO////xA2DQXkgjOsNjcrOymvQwRRPugVDwtYaaQVxGALeBRSvhnI6JFnr/QFEukXWMeuf8a02xqR6vD0LejjNj6hXt4dCDLzGlFGxF/bUcTFQsK0om9CVxlU2FRIf///DwdFhM6CQsURA0YHv//92562VJtWDJIwtvoWEBrR+SD1PZHwQMt0clD8ehjUepcuoGZztZnJlm2PzQgCGbDTebXmdoDZCXmh+uJcJsKT1aqPoSahae2Kj//+lwGAEGCZ0Eg8URDyj///+IECMyLLHtckYTXUAAJAAAFaHTlAjgK7GJhYsgSJQwtuRKVRlkiVlaYZ6AkBXwjlf+v4yjahsRHQGN5bq1ZcyJxUXnFNpgEhOVFMIh4G4KtN///sERhg4zMPYop7f/93UeRAY97kyb1sHy+I3MkjAoAD/+5IE9oqi+zJT0087dGVGikhsS+CMUM1M7Tyt0X0ZKimWFboAE4gSGr+IdBIgtn5dSMHuB1NTIyzVur3VFQJ7Oop/2/n/ttFgy8+fWdFMkfePTCx1LoL7s///9giMMHGZh7B0CAsPd/HFyDLzAvVHsWLC7WirmSiFBKgEAF7ClmwSMozZao5CtYMYs14YXIbyaiIHAbRFoUKVb//+ZLV2DRO27w9b/b1vc5/JlFQXHScLn8Owv///QICARb0eQSz/86eJDpalx8IIZETSsDmqzJAyJQjACAC9hSx8CnKS0qnTXFCwJJOFuL3wm8hQShYJ3KVBqjN/9NdaZwhiebrbLh3yuly9+s8gERTIUd43kOwxZf//+gQEAi3o8gln/9fyzPYybp3IQ+G/8Yr98owj7tKB6gAAAFCEACtxWmyrNtcmBr0Y1GOvG02djbaur8jhco/vzMzAonPvuHayswM9k7sbdmPctReSnQbhk5GaNn////+YqjDLIp3b//8ib+ifQzv7ql8zH4dELvoAABwAAA3Caesl1TNpi84kIShO5bWW//uSBPIIgyU+U1MpK3RWhkrtPMV6i1zLVawkTdF9o6p1hYm7mrFXbS5ZU/AnliUj53//I4VpuHK2Q6V+M/+HK7XcZSbZH0RRl+VIm4qlQ0mIuzjn///8xVGGWRTu3//5LXvo3dUc/3my3KerhzQab8N0AqhAAp+JNChWmQ6igiGlRhe7Trihm/q2/jgQnsmZaRT09dWgCRrKtcvyZ2CP3ctt1q1LzJMA8eqXJf///nCBgFVYqgoHn/4gFQfUTewODiAfIix04Cq1GlJH2mhOAagwAn+JNChWmQ6igiGlRhe7Tri1re157UMD0fpGXXTy8yNJa9z4+VF7Ow6XmpqU0iOgCYmlpzf///nCBgFVYqhHb//+KJOVGWYqbGcjCeWMy4Un2+jYeddaPQAAdKIavw3DgBVDkutQXHJdTQFbNiVYeDOl/Wp2p+UHWU5QVpJDsZenxCUliI8Eo6iWfEF1wooePn+xDj/4T/9dkCK5+3YUe/TahsS133PVLTuTY3tbSkTUixnHGnf3umXHPd6gAgACHYLqGhr6SGiun6SXUMYM5f/7kgT2CILCT1ZrDBN0Y6nqimXibsugx1unsK3RayNrdPWV8yDUXa8yqjAgFrM5fLjq3k/6kcXlIy4HupoSspvy/GFNBWFyXxDnFtSns3NiLiWXSdLYhZrz/Yhp/8J/+vaBFc/bsHD8yktjYe7jb6mxzzy6ZraJGpGMMlgNF2tQgtRuBgAAmWh94DMD0CTsJwpFxtyuSqESCLSsTIntwQVX2v+k9uEEYXXREuR8P/5m8QCNvEUVIrko+sH6EINYJ6prTra9FURiw8GoSgolbqchw+56/yoQhzkqtWYjNshp6RkvzHTr6EWlG0IBK7BjbwJ0tGAxS5v1dqE4OEaGqn056CkR3H4we/Di7gLAFVYI5/PpC3plRZNPgbBIjUJ6prTra9FURiw8GoSgolXc7kOHx5635DDaChzois62dGdmu+lK+0UYZ8BjJQAAAFACSnNg6ZgbSpUxsmmlj4q9V0to6CMD9XbGSv/yfX+EKiJfc95/7yDWRZQbusxWGyajlvZ3lVmb5WZfhpBeInF1cPrqSehXRvuPg0Bh9xP5/LljU73/+5IE+wiDJUbVUwtC9nJoyidp6G6MwS1LTCSt2YIn6mj0FfOg3BVQ3tHQAAcAASlwaFEAXI07izIWaxZRSEEi6I2SMwf7vMlf/kfr+IhKB47/1YhqsR3lUwTDyDFHs5kEVYjW1Vq/HoeVxecA1zknYxXRvne1HQ71R9z2pSX1UyHLIwuwCERHxATCCC5dwkK5MIhIFIGyeNbzZkf1fYW2HtHEHLqYdT3USOBp/2XQjhXYIKCvIZ/mlT/hY7+EgQSUDvQQFmQ2EN8c4Xudq1jpkGv5lwzpflDyB121HgoRC4TSxhhSqBBTcgpdAYQFRO2N7ZILlMwjS/nWhc6mHU91Ejgaf9l0I4V2CCgryGf5pU/hUOfCIEEsgc6hAK7NCHPHadmlixuvs7bMvflY+nrxMrqH//EAACAAFJ+GVF8SyRQhRE99Faip0e6ina+FN6JezKvM7TL7Xt5f8umSFbm6zCIP1U4/5xX/NaPY9WpYs3Xj7uyyvmRcWv/8211NkJXZRQEgtLS4fm8SnvqXflMaKoTbA6gzF/oDv+wo7Pluvb/Y//uSBOsIgvQ8VOnpK3Zeifp6PSVMy105VaYIb9lDm6u0kwknAAEAAJ38MqL4llihCiJ76K1FTo91FO18Kb0S9mVeZ2mXNr28v+XTJCtk3WYRB+qnH/4r/mtHserUsWbrx73ZZXzIuLX/+bdq/NXNTDI5XLnd3ginuSl3OOY0WhNsDWiUv2g1h/ijk7Fu36/YUBgNz8LzSkLujxS+5VegMJzig4PY2mAyeVUyNUlXUj+k/0zka/XaeJ4YrQPHfTet96bvuXW5aYG+vAdLl59FEOX+5QzV55kZ7nJeYMvY1N9dleK/n0ShNJeTLnUPayH/qCe/zLf9YMBAJzcKHlQBZ0aaIQlWoYAjmIkweo+igveJRpaWlzG1vdPvX8jt/mLTaZK2KdT/XvnOde8eJd7JbyQ6Xalc+Y2aaYcv9ynqfPIzjtIvMGXsdMOV2V4rlnqJCBN7iCmqcuypAhPj4YVrWgAACrYCS2nAVIhowdACoARvWDGvjPVRTJdyEZ9nWz/eYRB739v37+DpwxPf80nOwxrv6/ubde5OP7RiRTPSidY53f/7kgT1iINlP1BTDxtybAfqCmHjbkzY80FMMG3JpR5n6YeNuLyghzHfv7xeFRbhNn6fy/WEwGjhU7UWjckiAADcAS3bQkTeKJGHyDlbBJFXOjEZaO9vPBDNAStS/cCAKlv//POhRtEH31Vi62kEvPf683dT+9IpX9O1badyoqGD0ESHJuZno5piuYePqantiTySVEiRkOBsNEAAByNhq8uWi2RT0HySoJeNL43HV8Rknm3FDL9TtaDsCgTBNzVtab/mX6EaYuBDQaPihS7/wtck9apWUEuxvCjk7uRAZym0NQcTiiP3xTlvCaselZb9XhYq+QsfqbvdEAAFywsFNODVSs1Wj2cuGeWujWOvxLqTzv0ig+Zq9PcnKSCGd1NfZce/PYWo6CZFdGEHX0y2JlOVjooUWY5zMQ/uZE5ynkah58qf+2UNFhZ8pr/QpMIgFaBMi00y+6oAAeQyW5aFJFB3qlLC7posbfM+Q5odYVi6OyhG+l/WZ/7ETBo8uwuTN//1rLelneyBHFKLzI+/kSt3N01v25T45mAijQ4Ww14WRab/+5IE4AAC4TTV6SYUflrImmo8KHSL6TFThiBvuXqmKvTCjx+1YtQ6Tjk6S2t96GEbXcu5+v10AAeQyW5aFJFB3qlLC7posbfM+Q6MsxCVpbaFnGfFV/ITGCMJizCw+f/izT7EId7IQ5yn90fcskEt3Z0yv2xZ8c2ARRocLYReChWGKAJiPckct/Hd6OEW64+1/m0jHwEBybhWIuAuK6oqiaDQAwQXFBroKxOBpUVG43Lt3Dzz1f/wyAgPAarqt8zoEgaIBjstAGHDTOt/yqe9KznNsx0cw00Hdk66JT2+u/Pgp6zWAer+2eTLOk6Tf/Ge6CgABObBEYwESyoRwJakAqADAaxsLIERrLzTMan8Xu15rfWpPPRNAgFInc3z87rYSidZII10rDh6WRdav/l61zPhU3NvInhsDbVGhGHdlYi91almbvoJIcGVeHi9+7x2tapZy8tT81UAAIyyGNyOQJzGfa7aspTIck0Kh6bVY/ipq7Of/OESAYa4k8Z+6yYghG+8gCDA9XuUJHIaHGNxqlkRNGScN3UXOwdY2SqsdCUy//uSBOSIgu5CUtHpGvZdRupaPQNuy7jDP0wkq0mZn+dphY3xIszvmCgDN9rP4bRd7Cav1X94AAXwiUk1CTfiQ0bT/OM/WJmu9mhOTMtXPQy9zFa88//PFjgslcSfKv3WogZfeQBBh8rfSI9KGzG5VK0QvZDOG77t2PYeqqsdCViUSQtw+xggRg0fDodC8JaVFherUAAABCRAG3cEAKOpB8jG3JLFPlBEn69zqKQhL9uOVOZMI1+n8bPhABwBwEwmHjiSSJ/91IrPTV90b8ut3q2hHOejSZiVdMEoOLYeFggQWsIMZY7kRZ+5EReX/9an/Zeoxc5wwTx2PtrICFCgNzcJEpukFyLbpKEq9RCUbe5+F+Ql+37UdT3K1+n8Wz4QAgBABMJiRokbIn/3aK0qq+6N+XW7yakhznlJ5iSdMEoedzwoIQMWsIMZGLci8+ORIJ2LcvnM/JZbRjKHw0wTo+GGSbUAABOVhptpwAdpgf4fBw80/UQkJGjHuWTYbjJDt+N/+RaIkdOWnbv/+/HdLX1nR0an+5uv2VjTforaxrE3a//7kgTjgILXQlbpKBxOXchKaj1jeI0FPTusLG3JnyentYWNuVV721bbhMi+JDEkM15lYJLPScqSnO00I4REzm92W8pGkIg77xAAAcCCbm4JIN+hQNX6SnEUkoRjEWlghxkIQtOHzvV6XraDvPrnGMPiiVoUMdrENZ/30iTnl0cVsRT/lb/r6uWGcVyS73Tb29XLketVCQ45B1tFHAi4QMoKjmkzGm9PHHhpqUBkEpubhDsw4UATXdRFV3kEEvZvOvMyxuMhMMvrqWyHmr/xBQNOLvHH/8daO63LjkBXquV5nTPWfHr2NGZjdbbIsPubCgYS5EGq4KXsvlJ8jVm+rcU9YcdIvd6IvyqmwhHQ8Bm4JLw8XV88eM8sc/rMlsg+aH/5OOcXeNve440zHdblxyAr1WCvCemcVoUemrCkBMw7qtqRYfczFLEMsjdSyQu6ZnsxXys4YXKkkVeMIFkxSGKsASoAAgOSGNtuQWCEg/BAwlafuFyerXaKqMZfzf9DSQrd9CyEp6zucU2/gigEuVI82fltQStIiOCCJtjQ+QYxRCn/+5IE3giDFlLVaSYePmLHGeph6D4LaNs/TCBtyW2paeTEDfagXFqAnCMtZ7GAo6ittCUtRvyJdWviNgjKCRptB9a+gAEC7Ya425A4ocJ8gcSIx+6HqWOtzkHUViT+b/oaSBE8eo4OjCa+vk4pt7gQqgGytRMfbPs/KG104KcTT8hxSCmgWuqmit6w88L2nzkIukpZd16dKGElBIKSMVAAQVtzckjkCtqT6riATrVdNYIck0ZyYYYZEAPlBhhR2xp8VNOghJaHKDGL6ZuspECsHLNYq7Ksd1uTuwILXDhHWpXdzmdcHEeAyJGuRFS8gdcqhDT4ulSmLUtzckVTUn1XCANrVbJYC5v6tiucpSEIfs5ZyVO7VHhpzxzwIaVCM1BiRH0cxynSBXMi7CXZV+25V2BBdw4RxJE7u5zOh3iOEcapHhF0iXBlnT8jUivSBuA9yzcIAhOBupijChSSAwiEWA7Ap1RuZamkYILNVQyVIfxx9Z6/faKlp/J1kOR8Pz5LtmvXZfg/F+yIRmJ+SOaQ7HRQh3Py3t5lb1mRhxYqni1d//uSBN6AkvJS1ejIG95bSerNICPxiykbW6SIbnFsp+s0kw1+ZpSL24R5ab5ZeVYv/n13lqEpXcp/7jgCIAfG6mJFCIgX4GDIKcAaATjS8zBUjjDhV6pwwaXD+N6bPL6euictMeTrIKj4fnzN7QWXZJSBli/ZCI6AnyRzRjsdFCd/y3W8yW6sK2MWOeY4nCSCTixYBqUZQUjVPpk3tcLT7s3//+sFGBEkKUCKoFVMRgZcjghHlZj0HNZLNgIMhhqS1xi/NXLmKlMvDPgiEkRLZ+EKbphCdf111PSutoI+dNNTTpcrRHyTQUWDBYspbFaGQu5pnwfl46nzVYPg77JATqOcKo4csLCRhZSCtApEDnmuxZhjshmFZqUHOyXLEBUSNUtcYvz2XPVKZ2GfAsDERLZ/wpu8J1vV1ij8prVCCpJTnnDX5ThPkmQougsWUt1aNR7mjnsOXe0i+pYc+KZ/M5nlxVo3i5UAAF2yyyNJwWZY2nIB9HXUNQ8+yto/zO7b2dWrPYXWiYEgPShaSbnnmnbc7fimb7X/KZb42tVzWtIdyv/7kgTmiIMtVcybLBtQaWbZhmmDagxQ8zWspGuJgigm9ZSNcLL8tyQ8xYv4bu5ozZsvzVo8SNwqZFmWSZuf/PBAjcTn7fCzxLLAAHOM2gZAzpSlaDxuxJiZoYZj0O12jmGm00MJ5d+tTtlilJfKQGSwrHieKNGqGN656SJGb5IPSM8VVPij0NS+luUPMWL+G7vgo14ucJZLpGtKmMgUQKAckTxFax+lKKAACrEdbm4GQpUBUTOJhmaBqYAfPwg6a2VQ+7njIe7HmH/MEdYUEd8FmQjZ0zDiSQiqwXNul0oaMba5HSqAZqYy7EqHc1RVa3caZpqCbx6l+4bM/6tFr1ONK8QFqSGBFiuNy8DIUqAqJnEwzNA1MAPn4QdNbKgZr+SD7s2Yf8wxgxxQJ3gLMhGKqHuJuREUFzOa9KaMba5HSqAZkOMWxKh3NSVZbKIRMJJd/5O/F//y0u6rhrfDGgMTR1UAE73pWhIiTl96QrClR/tp1HTaIPyFIdMrAX0gbMDYcB50orsIBP6keX9qcI7CLh0nwqTQ3CJnD7AgEQBXDLn/+5IE3ICDA0pVaYYdvl9oCephI10LvPM9TDBmyW6c56mGDNEZnaXwUTsRvY0dofmxxi5AZnwjuH578p5FoAk73pWhIiTl96QrClxUqbTqOlaIPyFJ2lYC+kDZgrDgPDvFcwAE76kcL+1OKdhFw6XrJobhAk4fwAAkAKbLkZnb5mxOPpzUkYz075KistSbxoy4yFTyfYACRutlkbTggKDblIUjzer+yUHET73uIbdY+nnSTiKbWcifeBxyrFcQ8q+PXTQR0wXTekj3pGoNFzDPlgyErMTW+/fpa17zPfT0z2WygWkaRJlfC7CVgCEzddLI25BAUG3DkKR4l1f2SgqT73MAoZsHaqYqgjZpRL54fbuPxDypwd3TQusCKm9JHfyNQaLjs58AeIbSlNKiP3xdI0wQ2ZUXxxEcYPlbiNQmhPtwmQAD/SaKUgIwK6UC2/YEQwCfmiiJ7vAWuOrkCXwYyMCDr0pKRQCR2J8kil05FkWl2FTuZS7TMQJIDxAZkdhlLyg7gjamy7HUP+0fOFv4uFST+uHNLRP+3Lvpc0JAAAKg//uQBN6AAsRI0knsGF5Xp6pJPYMLysTLV6MIeDlfG2s0YZp3HMGS4xBSbS1MC+iqiUtSOihAgBMZXBq5XPIvWSb6axo5RNHo0bIoBI7E8KIJG3eSyEDKg4gM677LsmYgSQHiAzI7EtPKDmCc6bBbwylaS9c+4SWic2GxCH2iVZd4sECj3t/pBAALgSzKaB1aZaSB+aMNSsQxryQSS2+dsVBYsIrpRDJnT3dAFKxM5ruTEPYnANqNwusSHL0V8+QJeVqVrvQIShqXZBT5COvwen3/vu+UEQARg8jsA7ch5TEwQJKkCtZXUOnUXVgPCRg6liVNegEgvP220cWEUWiiGTOnXdAFFgzOam5MQ9pcCOfw96ZBY4xnOg+FwhRZtbkYOheEABoaGxiGtyMxSmCydN92gY6gEw8xdmXV2DPo6gABWBJJUoGXpFpUI1I3BVhd1A8L9ULQZSFOdogsCR2CAnBiJIMZE2SAjlCLlHTLWGbE1ObbEDJ2Pc7JnSsYUX6G5FsRkltjzMvOHKdtwSZ/9V21x3SorLExgap9xV+zAAAl//uSBO4IgtpIz9HsGapmxwlzYWNaCyBnMGzgYUl6mGZdnAwpwCPpbctwLKC4ALgABDnl/3kkQsZtpHk3TJRdQU5uMzK2UK+QBbWFTyjpllGdianM4xVHPOmsngyVjFFfRng2wxkgO2WZl5ocpq92aH/qXnznOtaN8EQxsVl5t22ADQX1CInPS2U+RCYSIczN+x2Ut8CcDuGlpq5wytG4hXxRXRKHIz83JVHZQrFSTQX8I3ncp7pxroTqAWvhjBnLog2JCbjVdvPQjzsBiC5Y+GvJ+RPoZAo3rjf48BAhQJTuCeocE0uZ6v1BEjQApQO5tV5henemrq82+dzs0NHzDaBUFFH/MlU9QbFeZC/hO87lPdONdCqgFr0M1Hh6INiQiiXo1eH8dr1ZBGyOU/76RJVkOfsHljIUFQEgAAFQCLIMrSAUWAoQ9IJAod1hpTJWMBBm5ufuRMxjxCjlBhfB1diMEVm0XY3RMLV3ldASKjzR1Qvd9mO2g8a0xqCMKjPV1FmaseZ0VxNa+XIXTnS6x6EdseaMFEUoYOtn6AAftAkluv/7kgTwCYL5Pc1TDxmiX2k5g2DDakto1zMMIGuJcpqm9YSNYYYwLLYoz5HoiQr1bblVaF6ByKb2Xt9kSxviFG0pheg6UYjhWbRdjdC1pWymgKKRzTVP3fZjtoXK5ibDVJbYJFsasdYzawIV3/k/OFae3oR+dyI6nCTyMbN///6w8FdjHtHY0AugcYHKJJyaRRFMBjlqjwRk60VnYl0mrbSOKwCQPBklbMTTmIjuITxWw4IEIWvaKePIRE6VklOgNYIHsCmCYEZVathHsbH/ld54wMy4SDI0TrnhOLAAUjhCKSSXJ/X/+kGCCCFKBmQAukKaYvl8BYRe9FPSldjmZX8FpnSSRw6jtlbATpEVG9dNsyv4jdxi8VsPIi17TeOwJCjpWkvQtZHuFwTBjJ1qgwyGjGzlzJyeJBl468vfMHwyWlF9lzkJJt5iyjzKACAAApADZnoCQpRJhQCMWsHhF9aVApFrr16uHhNCuYTpSRcV9r9Z2a+0/iy6tPW+be6xXvHiplv5aaxvVvfZP6r13qsb9USPh43u0eTsif3Rz7Gq5Qj/+5IE84qDFkxLuykawmHKOaphI1gM+QEsTKRswZCp5imTDaFjqSTkifwXPdfq6kfrQCSQAE6Qq0DoUBKFNGLRIAEgi7IgAkETTnY5NB5jQQXdMNQqLZ88pbce06OrO/HTuz8b1KMqxysVdRtcvMPOO29mqLzUoP60vnrysMRLUVUj1CEnWXB4bGoNMaj3f/1EklKUCCKt5wAUGl4WKrSmKs4sBZ7YWVZ3MVCYFHWBKqKzvGNXY2wdmEd1tx6tXOexwNcGdueslFPVQl2ZK+HVGVBPD59vSAWP4jFXHeo9k32Ct5cFBGju/oAAAyEKfRvPyijUDgxqMJclc6mA2aes1K778a7OshbrAlVCFO8Y1rGGN+QI7hbcerVzmrHA1wZ255SVnJUIuQt4HJIqCeP7lPzQYMy+aiiCi0JijoRooMSH3AVIoUMWHo7//+KVAQAAApCFDBkANsDGKoRaqQpQVNHFkdiSqDRI45iUBABFinVzWxYhq91PKaGeVttbJqVMEVPMQzI8WmaDc/660FQQ9EMpNU4bRdOA4XYbmNAOso0o//uSBOoMgxtBzDsMMaJfB1mXYSgyCxzXNGwkZsmQo+YNhgzosM48Y0JVw6s0z2f/xYACR6OOWNO6DgHgViMJ4tMShC/V12rtYYChjmJSCCLN7TXiwQavSV4k0HH9bbWyakrgip5iDAo8K5oXD/prUWGYhlKUuZwtCRYqIGN9e3uVmSl8P2CqGBIWWgO0M/vbRu7RbTRJKlAWUXeHdSNW2PLplIspAZxI3TKFshAUUlLZBIBBBGlHBQiJiqEUIUs0W3cdiX4SWZMi5oh5bkS2EvkQQNOwmjfo183WZ3JHqp96bGWZ/aXTUlzzBHaVMn4BAkgEygKHFphmKRpowEprKRZSAKuSN9lDL4YCmVKCoJSIRlRwkyL1QlyFK1Ra+5sS/CSwy0uaBDy3JBNQl4MQQNbiIK/AJ9OSveAXm2bcaQX3WpHFAy9UqVEpOWVAAAuAw9i+oQi2jZk1WaLHdSskDhk70k5POpkzxfhPA4xjV/JCY4bAmoJIMaxzTKFSHoOWF4K9OK50osaVDDuCKBTW7vUHIRA0ZIjXPE5Ak0cbeSOC4v/7kgTpCAMKQcw7CRmgYkl6HTGDDQt1PTTsJGbJcZkmXZSM4WAChdolaEotsd/V/6kQCFKgTEnqXpdR8lqiwD5YgYDGd2AqHLJBbg+OQ6QqfVQmOHIfCyP3NP+kdBxYRYK9OCX7yyfDroUCjr70Gg6FgoCYdcgDl7V3vFxR5NxOEnAiLD+IV9nGfZ6gDAQBAATiEJQ5IsaAbyumjo9ouaLN3IDf6SVJA5mmAtNV8WRB8baSMamsBHIktQom9WaVunZjGcMiyrNckDQgkYQ4oWHAQdQIzxkJDzVqRj1TAoxZgNgAKLSlDzTms/nq6dLFAAUEoBIlFyMGeDRC6UuiXHuK/EvJZuqMDeVksZufu55qtNloH5D9XOJDZTWAjmkepxCcPWxqG1MK1GzhkWWzXJAKIEjCBMD5kGmrPOCQ8VqS63FHuABNQUD7137bPZPdvqUADfe3a72X/gqC4AyULKYSebdZrOzKjtHUesYJHSDXItUkBw1YMHIVh4TgzMECRBRKTN70pFsmZmUelskNog7oKCbkG/l5RP5RkTyp/PsMgyH/+5IE6gADFjbLmyYaUFrG2ZNlgyIMdNsxTBhnAYWaZrTzDThLeSMLGrTGHHzi1pdZnEySN8cGkkklSsLJEazG6HKJMZrqvAEWJdozGzKTsCdR6xhieGMuXrJF9WBByH3y7ZJoxLmz76zXiZm5QPpGkNoQ7oKCbkCqagNmxQ8gqlLhcGwESVe0oLgFaRdznuVWn2sT/UABZ5FLJGprSAKCPFETKbXkxjSNUdzMX/D5gyIIw/a948R/SiJrfPfdiaIsnrVJjRTMvaW8/bNb8MSLzjblEv7M1CO4aJBUcgDlzw6qcLr5Rvvq9Wft4WAOuquS/NwthI4gwKjJ1/1jN1EO54tywR6GFIox9t+PAAWlSiE1vvnHYmiKkPWkRmimZe0t5+2a9KYkXn8fZLNG+hCswQJ3HxzGThBQXJ/69nsr9xdiABAKiJHBccD1OY7jZG6vhbypLdm/Iihw5zy0i7sVnO5md8Sne6nbXZ/FcgP1HF/UpKj1mGZZHDiUR2iFqlSBNzRsxSsUg8mx5b8OoJ7cyOzjf/mZZKRJVSInrYMEzRUV//uSBOeAAxlG0ukoGFhfRtmnYEM0CuT7R6SkYWFUnyfk9IxcOh59yv9vQscEESpGSIDVYSNB0OQZBMkToy50cUku7NStcMWG0LtYjMONWHs9JiBZqR2soYat8Msnzm6dURaQxEGbmXmKWLsOk2MiO7NYJFWw+wGpaejRiDs8BRwBnDBJbvbWqnVYUc1gAgJEgkuViw5wHswW5k1STNRVNVHjIWuHxnyBqDsTzB5GZL2nHskzaku2vaZstIHkSk3VNosPRTEUtjOqhoKQEtJE8mermhEhrIi6T/PCbSZaLCqAigDKBqhyFbP/Z1AAAKUhy55OiyBMQhoIAZoyswjQYQOOVKUQEEiLBZGDyMypXOPUkM2pFtrPM2WwHSK7FVNkVDUlMRlsZ1QjkKQEt9Lkdt9FEIYzIxEE5tvAg8+Dw8KsFw4RMvIiMMoAHV//2QggEEl1ENQE5PFJmmpmS1Tl0j6DMTX61UVPUWZTu/mqvdnN0oLI+b68Q4d2jqwd7t3MyRWGJgz06JMMJtW+HJ//yOIfd+RPY+AksULkQvDgGbDiCv/7kgTvAMNBT8ubJhtwYIeJk2TDOAvhFzVMmGeBiSKmDZeMEIQPJGtAyv9/+oJIBBKeRDUBOUJSZl1MyW6cnSvQZiaXrVRU9RZm296ZK92pujgjI+bmpSGnSFR6wfu3czJKxEwY61EmwDar5hye9PyOBD73jIWCpSU/2pQzuRkcBokaqIBUIlBGNGg9/3/6gAEDSABUrFUl81MleUq+wJMKszaik0yCumpDYMIGRhbBB8DhgANMnVmNzVuTNF6pfJ6FL+ionKySfQRRVOMEKDD1PKbTHz/OzBPM7gxOPBN4sRKocYEIuKgYmxZc0+5CUV+Huq1QAQAAC4xG0HDS+S4lKwYUqGXXFFBTIKlMSQmg0DFDONv6VMn2Y3MS3JmgU1UobN1Ci0tCROWFz6CKVTjBCgzwTErAMmqoUBxh06kpICls0ABcPHQqfU1trnzzrbi5dacUXYoQgBJJdYWIIpGgSlcqcgTB1olUVAcFJHBng/hGS2wjW8uhjLMmWmyW+eeNX2CTza4c54gqWsc+mPlZS4LGE4aaLtPHyOGAOpRq8tf/+5IE6QAC6kHMuwYZ4GIJ2ZdgwzwMrQczTCRkwYcZph2GDIgIQMJxM0c81NupK/qaAtbbtpdpJvyoKBIbogBsdaJqPAcNJQZmDzhHrb5q/LoLGrETLTkq9OnCr5pP2uHOdQqXxz8x8llJILcThpoXaeJsSGQwBxig9YWCtEBhKqhr9ZX8Y010UFgAHAABAUiHbhroOYBCB2EgrA1ZG2fmwNvizyJLOTHEXWfXbfqkF61NTKwM3uj5paiYV4iG7viXnr76lquLHot0zpYgZXfE1P8UZWo4XcKj3qJzwgKqa83lCAQWXBFFbz/7F/0AAUAAA/DPAuUGKGAVE4Ilv3ebu5U/2db7rDtu5drz2H3qhlBZbMmQVIHJRqvbAW549JbLFcFJIxmde1PfGrE0MWiuxpHCFOi7rRppWEzurrTVSubZl1uo8chRwQQcEh1MmZsL9S+5ll0AeSaTRxtu6pOhDi4uDmrjNjeqCKj69GUhVQKF800yXIiUE+fCGKHnKtPLkhIdEFkRZ00lOzWNC0Ka33kW5KXnfMjcjv6PsfCKGRxE//uSBOSAAr80zTsGGRBY5ppdMMMVDHT9MUzhAAGiJeXlkZaoch/FAhGMvSFB6e3s/7hQhe22yxyNpyH9aLngFKM2N6LSqu6dItrAoP4lTIsslBB4exDIh5yrT15ISHRi1Io9dJTs1Rk4hZVzohVsVt6/Yy1WqO/pvarPQUoeLNSfSeA4oj1f/cIggkggGVkE78CQpfR3nskdleLVEOx02VjNDCGRsEdMyCQ68xpDywnCqkXoTmb/+RmCMePbcmQR6FXIpwJ1jNHz/mznphom8IuxC0+7RfxJwycPAIIhRS7XcZfp/oeogAEkAAuMgvfgONEATXnskdlcrVCDsSRs7GaGEUhXKOnkEhq8g0h5IE4lUi9Omd5fIzBMPHtuTII4hVyKFAnWM0D5p82c9MNgj46M5bfPdxUwTI8QjKWbb9gS37Hvz/bt+63erP5IAAbitRRIx7LUx1jVB9Jjgnkw3rnJxkQqKTCx5Drp2rGoTmcKy2Xk4JCN2DoNFSWEfKJYTeVFahiriXjByLDCIv2uFYxqTu9YuUjMjzplzK9fU/QGSP/7kgTlAALWS9Fp5hjYVsl6zRhij4vFJTLsmGdBlR4mHZMM8S4+0IsWG1hs6ZxFRBh+USM+QORxgCCQwAgAoxtDbAUyCypgCeM6bIL3MefLM5wYjSi6qyWggp8zeIR1nhMqdinYo0E3bixw3TV4YfLDDMr1aYWOJpO71vlIz/Ho/GJAZE/TmlfvmbC+noqMqDgwva32P1obr+Q3c/8G9GfkoApbtptbbbvwoMAyWHmUnirXZQKtkvcxJzRwSKiZLpOq3gmrFZnTm6798XEz4DkrSTrzJ6azUmGBNRhO8cwncJDqFi2dSTSIHETArkRhA8ke5q/oYNdvmH1AAFwgqLASBJOqia2CUnky70iAnbS4MxYk4cOIUR1XQF6sWEailszpzFqZ98XpD5ZNuN1575rNSgwiQTqYm4k5AqTmTsPpQ5iAjnpBmQ/92/e691UAG7Q+NKtWASLqCJH9ResO3UMEgqhKuVTBAx6VJlZgZhY7luoiR+3YsTMmNNTZXfeWqKSFgGAMMJGUSZCCCgChEYDQEbyiAgI9yPRcKDhALED6lFX/+5IE6YADTEVLEwkacGkpSbo8wy9LGNNLpZhg4UsZZgmTDKCt76AX0VXLqUKj2pWdvXP3jkAANxCEbGaagcniw2F5yC3J8uMow4k5jzmWhZRYd8vKmqLojSd+6iGCP27FjM5BjCYUcwrnvLVFEhaYhiUqsJNQChEgso9lGCDzQuCJUSEEsWrJHWtHXBoF5QwYU1z9QrbnUvXTyQAbMzu6RNtbd+OYeINGAgZS6dI+IhO1KC54cdCxPluQ/R32nF7BkCo9U20haWMObLKW/elqRj4orlmtI0FmgQxLJJNFlGAVOm01p0NDYCm2k1W1UXpVruu/rATjkjn/1eRiQ/DnFsfVynCsb0igueHNCqkRbk3Tfac7YMgVLVz5CqVtWOqWLxryf5+fr6WP8+y+ayekCGY36QfJNVcCp1nFNBtJEYD8mUtaWKIbq7VIvq9aAcU1aE7QQkEtwU5kPjLz7ErXpi/+kSlxKWRVVATqTD01ouu6eQKBw3DLIWGx2HtfgKcN24JDJi2JIHAmOix2HUFRINzaZEt8y6TVkyQ3COckhGRq//uSBOkAAy0+S5MsGMBlhwliZMNOCzTTS+YYY2Fjm+owkZn2RZcPPnxSPv55c9dbsX8l/ed+///mZ8UGABAEGSUSVZBAOMQXXqYo74y0X1CVroEHNHHA0uCpQZSThAzkTHTWi67zNASBwXMvcFj71X4CmzO2hRMzJCcVOubC6CokG+3MlvDL9FMQWjaUfBpjyqEAY0hkNNTPWCq7zKhGkuwW7lD44XAgAAABqEFg8aE8ewqxjJZprYTQ3bsO60wZYYdU7nkWbZpThFCjAxS5JsJcp8JD+ny7nm3wyfO2wmIsjPwhlzgRFEl1A8CIqTFFknr/2GK9QxvyHs/kgMBQQEvxE4SukePMYo0U8jLYfF6nY57TpYY1TuPkRHM0pwkhRqKXKbK/PiCD8G+3uZN/DJ/tsJsjGY/CA+JAACjIeBMgEhfF0ob0KRHIDNtqGLevU5lRapGhSwHbZZbI22nIUUWWGMC3NyKl8pSde/NadaioiGdXqp9NexA04Qusyuedc1/BMQiHCM2OPwsiHHNmAGfQQYfawA3PAQqpDzsosTh5If/7kgTpAANfaM3LBhl4aqhZnWTDLgq41TEsGGUBaxpmZYMMoKAYQeSWCH2O9StNaXR4Cuustmkkl/FwiCKIpkgbyjTLaqcinu37XXD6j2M63LrVEPmqpwhcOYLnmMbDyEQCGSZ2TkqQmU+gDHSJY0ZFAHWNZlhu+CeaswzALD6wr7JZL1+aze/VnbwYEgBAEAmSK6Px1C1vrlMNoBGvLUrC6t7VGOBI3hptyd2hwyIrcoZIgNeHOVKTnP3bVDCllZPZeBVemK4y/8n7nL2fvFwRUaZySZgocm9/LIzn++XCuuf3eyeXrM88uX9MjhZX+gxAAAN6Gx1lo0NWcUAhqwEW6MJDcPSTmxsRIEJuDC3oN2YeC8hgdNWPiA7w25RlIObfu0VHCicrJ7LwKT020FBcvBBPMzl9v+LoVKJtD/nyff6RGeX1/s7lH4U//9v/zX6fkuZ5IZ9oMgAXNkZYau2Wb8Rk4XM9UXaTj2WVG2I1UUsOpdNLln0sju76enmh/A8YjfTtm7ceIhsUJSKod7s86fCKXh+WCPjHGK/fz+WZwjP/+5IE5IACxS/V6MEeHFoNGk0sIs0MpaMzTJhlwaE0pdmjDHgjLI/f+9xVZajPjlsQ9y2PbjqwFXJprG42m4fYfeWm75aT1cHoU1ZSKkkW2Yvla01c1TOZwaeaH7BwTEbkE6sotlBYIAYYATAkHj1BZb2OaRuDrTxsbReoWWKG3TIo/aFehkPKWK7qH8dWBAAEhRwYTH6jDWVtkbsSL6fjZLTw2T63pq00ZZRdQP3NwR1EeMTyoIIiVAYPKgwBmIzVjEUMGmwvev63VfrSCwsPic5CKlcvvwGyMcfqgpbKLbVvj5NxP/0/+w3DSbwAfuWi16SkbX7iABAAARDkg4aPrAFBgMjhgLW3mqSfRNO17makvvSDnczjWJfJ5UGIiUIDBmVChBTFDJjGoZpWfen63VfrSCwZPYESRhCGthlnAkLma7p/wtfV1na8eJ/oe3/+yzb/v/zG+u9tf8Yia26TWyyOWhE0MMMKb3U0bA937kRiTBIbpoAGXS0yKR3zIkc66mnUHototbNW0UMNIBGRF2vJjqj3GYZ+/l71rj3x321p//uSBOOAAtxS0vkrGBhXZPq9GCOzjSD7MOyYZ0mTmmZplAyxLeFIoY94nJ13ktPb6////dr/67+gL/Tqa6WSXcODBgme068u319fpf6fM+p4fQaieI1TQQKI8tMikd7kiP01y8IdHkWtntorDJEThbHayGZINZmJ5J/T2VTv6nPRtUsWlpJLuu/9G+r1f39rX9p9sGASSpIIRGcpI8iY5ox1S5nYNyELx9g9zTwVhLDMutOXtiDk475kkOse01M4TT1RCOpVK7KSxQkvkXs2QiqbSPV8iU8NjxpTXU2zvfrJzU8i04f0/W9nn67+x/wrMulPv9syw4CUj1sskbl3HoD4UjC0RjLQWQzAAAOSnA+FPBU1CwzTrTlKrEHJx3/Sdh1pkZoTQ9Yn1LV9qhRQkvkvWbxFI2aC7cqVpMY8YFTXXP7S+tm2X76Qz88qV7JnuVf4f+Vncy/ecKzXAxEGsCKAjB1IonpVudTmtjxq/ShHD2NdHKQzhnIRWrFpbXYy+clIqSr3bukIRCszxbd8EHL64W6VW940+6nEIjl+Q4ZeWf/7kgTiAALTK9foYRseXA0aTTBijwxZozJsGGPBl7RotMWMBPt/meXPL/Ty/537//Ey//P5nz71jBgX/84427cC4FQkkSSKLptrSeNX6UI4eevHJJmxm0JHVYtLb2hfOS5UlXux1UYhEGstxZ98EHL8wbQCmLWnTIoYHNUKhlBQ2WliL3ECD5xDLGpqhxDlX6rTI8h3Xa6xvSS3SyRICGzE3kcGC8elHPCD23RMnnNafb9yad///+4ICBjGBDRuHs65EOk+HmVM0Y89it4D/002K8EUuugaECeM3v3FQrGP0F+onMiR3bn1IF/3jjAK4j1b+ive7vPbg/T5HoBMMACuXfqpqJGYAIIqWTC+BxY56Qfp3FmkTlQ7/8lnv/87igoDgQQKB4MMswuJHyRJw2i7qbtGvvapG8E/OZKbU+xkpzNFRlfdUPxgVBoXGtbkooXjxPYHUhFzowWMIGk4LNWhxMeFSEVx9QIGpKExFCNsFTvc0M3DBIcOHjCwN6o+haTYnKakrUVW4KUawbnfJYXVdhlK7o9O42/1jXkieyNIpYn/+5IE4YAC1WjOEg8YKFmmmioxYwMNHNtjp5ht+a0c5+mGIIjqQGUQECkDH+fOdmpZuvb7+H41yTBVrSqila1hMAiIXLvWMDYumy9v3q93////6wA/JZHGc45LpuRot6aSJjFzQSlj1tnV7fP2m5KT08c6bsiQBUCSFFPzM3gadIBQQoi4ot4+j0WIWy3HzjrqelOf3y/QuWA7QtAIJC5d64bCZEcxsmebzahs+ZcSdWBE0C1K7a2luSv12xoAVbjSRDl2/gEzWQF0pxJqnc7ZqVi/XpffvmP47VHTK39xbQcIC36NF4mGpQiNyw+wDBop57/s5/t+ki+EilooIkRxLifBDYqo5DC1fJX4vMlVMFi+eDEkImMe5xojvH72MMDQAq1GiiHLv/AJmsgLpTiTVO52+pXX/pffvmP47Vtrn/i2g4QFv0aLxMNShEblh9gGDRTz3/Zz/b+Gie5XmXjClRB43ZqihqWCIxOLCFrFCxJh5DCYkSiyB7pDSDQUPxU1Q82L45h5CwAJHECQHbvxXSloBh1pYHU7Lalvt/ulKO0w//uSBNwAA0ghzRNpwdBp5dr9PYN9jHTTW6ecdtGeGmt085raq90cdD2nS5Rb/1LFwdDgkj4tY0bCwdTr+f8T+oh2+sq8zDgYQKYir7KOmbltf67PVxHhIfS8sHCswpcmQPqICoB3IABSXYSskJgoQSS5BDUgWzUeDualpBr3RyhrTR6Lf95LDMHQ8B5JY0tNTYsRj+f//3R/LGc//LeO0zdBG1pN5O5O45bPYaaYM8trxrxtJv85r+/fklIADkiQKBkM3TzAlyaBJ73ugbcvY13dhrIY0YJbWMGu/2yxAYFjRASQYpv+33f0d0ef6iKHuOO6Kwg7Kboym2nsSWl0Ep/57ZjnfveuiF1JOdoAAckaBQNu/EM3TzAl1CTpLUsKPQeq+7cicaqjBLaxg13/yxAYFjRASQYpv+TXc99DHR5/qVFmHHdFiAyqWdk7R0FCbZNZ7Ge6tTcWSPc9VZjfkgAI5GiQDNfxscjmFyd5WyWHP2uRA20Zxq+tCUFlUrHqn/1HB4ocFgiOFRAXZn/v+S/VWcc7s5CKH3IzEGQDF7t9U//7kgTMAQLRP1brBxt0V8ZKqmSrTsotB1unsKMZSxordPMUs/tdDkpG2IWlY8ql7xSXL4q1owAr5AAHZsMxQEDh1V5pQ0Fqr7XIgbaM41eFoSgsqlY9U/+VGAXDoPBKCEOCUcJsz//5l+qseWezmGGjc8wxjCkH5NLtvRIhlgmIIYTYeXfeouXc0VtAQADSiQLATd1cAAUqlCDZHC0XYIW8b9HKrfps2ifgKCtrElmFsOemb9+5bMuoHJCCT4tjA2f/RKoOznf5j+nk5ShxdqMBu7GkAPAYFWv/j///+bgtJ+E7/57vhYumt0Rti7IeNyHqPasOvFB3sF2n8q6gQBSljyHWTrNdMBy3rmU8orX6nfw2D/n2a3dbqEok5bF0bEOKtzMnK5UuwMI3lhaLQ4ErYkbBwadfKaImHZx9/MXHTycpQ4u1IBu7GkAPAYFWvj4/n//t7YsI0JscThoWHnximgmcQoKCB7VVPq6O+hK1Af+QgBFXCALajFTo9JY/C79RZ+nvL7VGixIg0RN/+AgKLhAx3EjsHHMIDXxJsihwHFn/+5IE4ACCokFXaegp9FYmaqphBz6OAUdPp5kN0cyfaOmGIboaerLpopHuDsRg+AIFO5//+VURzlTIRjGqjV5hpyLEGLGsJqvAJUr1dAb6QgAk2h4ISeCpSHpLTwu/UWfp7y+1SpIyKDY6d/+IgtF4UGw+48ew0c4cKvjzZkwm/fNXTKKOH3EtiBY8akT3X//msYcXm2oQGthPZYQuTSnUZ/2395ACbI204Aid8kAuDVLyPkbP2qLJGGJanPaqsGG4UoxBZirZ55/0sqbHoIpiz/xJnqhM6IIK//EhyvhhxZYkOu/jP0pc0pQVLxwcaXnWIFkpczqiv+sBGSNtOAIm/JALg0oRyPrx+1RZIwxLU57VVgw3ClGILMUthfn/SypsegimLP/EmeqEzoggr/8SHK+GHFliQ67+M//QguLPixdh1gFPpSnf2bvjVQAo4kioAALtqR8VukypSvjmtAmaRFYLaaYInSoGju5tzZ/N6Hx0kVAkAEJMl9Pt1Mxdv//y9tXMKHiUHYIxP+3uYlL4Zk6yxAsHyhZUQbklmtQFbU1w//uSBNgAAtZO09HmKOZYB8pqPMccyojLW6SM1xFJGWt0kZriCt2GQCo4UioAALrpw+Gx0iMkCSvjmmhW1KF03TeOBZcPBzXd0k7er/23FstBmRMMYgZkfJzxnd3ndTd///l7anMKHgqB6ARr//+rt9qLs7rkfKvxLMMD5RRhogW7LNoX+VACuiTSZYnQ6eJ5kciImuDIVTIc9itvIZ0YRcwFqRqzyKVX/KqGhsQ5xoe3VbLsvddxC3M/0qniPY0TKLiIEgnFJPYwd///sptMjJomPcIxETaobJg1ffvWxLwArog0ANkyxOh08TzI5Fia4MhVMh/c+krFwRHGEykNLPELTz/1KlRbEOcaHt1Wy7L3XcQtzP/yMWzepnoozAwfUtuTA7///vIqaZLLPJKuWyfcNkwavYaekeXpPgAZYWkmAABsoKpBKEpwRUkcBW6VDpPHjj0qYFVzELu1U2rj//WngdVA3VySjpZ6RoqpziLeq+fGO1jkCohjA7Hixp5dDzjTzv//5IuIzIPmCQBPbY54RcYFxOWHlZc2VUdwgADLC//7kgTqAyLKMtVpIzP0Xkm6nSTFjotIzVSEjRSRcJmqtJQl+kkwAANlBZIJQlOCKlnFO6VDpPHjj0qYFVsxC7tVN7j4/1p4HVQN1cko6WekaKqc4i3qvnxjtY5AqIYwOx4saeXQnONPO///wfEYDA7iSEJuMgSEXAgSJlipVJcPrnaGMAfb5Lso3NLKCSsT9KSKNWw0BWdhtiEq18aJFjo/77//5rIZAghlIVVbjPflkCur0+/5KFkFk5m88IZGCQ1XGxCJCSydU7//+oVYhS4jLnXMeZaoEzLFmTxA8xdzSgdSOAKcTbYA3jUsTHFECJFDbghgkOeDRa2olwkEZnmopSzk/7/+m4hCE2Roi6iX1gcM647/9KqkujaaC48FDKxoPAIMcTBm//////9Pt/3W1Lvrxx1XFYR0gXRmIjTxCu01AKcBSKQAAG2qsK6VIVEeE4ttKOWd/MVtVX6TV26JvNgPr//+ai8soapgOw3VYc6dxBZJTd/1m2dK3EgmlQjJjc3LC84NoVzIV///lWO2lg485WuhKGNSFOzgDqAvBUr/+5AE8gMjFTNU6WZAdGMmap0syA6MAM9VBJmP0XusazSEl8MAAC6arCHVQqoTxRntSIHLZ7i53+JA+maU+tB9L//81DwWHRYqYAiUUw6sFOZ3AwQxUpu5jrq1JtPcLgszsNZdI9HoxXqRHNb5tCv//+j1I/QA55pMQbGONwA9VQAQtKACV2WwnEzOaDCXNCDdHHedVqGSpFUPCV0OLhjHVgEHlubmRfvkFSFIJO6lTbxIhVL0b/+pP/TQBYTNK26iiMSkwe///2BgRKLxzWPFnXYTeixtJEES4BQokwAIWlAAANpXZbCcTM5oMJc1Abo47zqtQyVh3JHfWtMNlvgIue53RF++YRMNMHndTU26Dxhqm9G9Di+VGgLgmJDqlkFA3CUSj2///////Ma/WinLQ4lFqXJjaDoqfhthgDgHNEEAACCwQBLZDTg9NQY4jBCAElhDLSkWEEpfpDZbubSJqkSnEYKrCY14zQcBmccYZW5c//c3lOUdVWTV9jLL0Kn/rcZZTBI4WKxZAmYKN/uSCI4bYXzGmo2cF2DGjD0aZUT/+5IE7gEC3DPV6ShltF+mepolD36LpM9VR5Uv0Y4paqjzHdpQ2etfzISNIHhYAACCwQBLZDTg9NQY4jBCAElhDLSkWEEpfpDZbubSJVSJTiMFVhMa/mg4DM44wxdy5/+5vKco6qsmr7jL0Kn/rcZZTBI4WKsLIEzBRv//7iBS33tHz/9aBmct2rsxGRP67/64NXO+6/P3s3Wn1AyCEgLrNMJBIEhMUwmMEGp5GRpD2GC1qz7cqmIviZD1lZWGJNiYoivoN+Wm4D3+e7qELcXC/c/O03XlAy/ouipUTIf/2ldSNVtgSgVwooeIj7GjlN2Xf/qC4pNo7i5BKmVEX+VoGKgclMz2kWK8+dYg+iElqamog6KPEIVH5keNPkG9/jc66gULcXC/c/Oyu2GMV/9qF1QG4CcM7H///7NT60t69Fbu2iKhiogO6OSqAAAwAggSh3pWZUEuyYafjxaYUFCdiHAAEFEJSRYkTW124XZ53U57AY7LXdonND0g1SzlWyPQLnJcmD00dOaFzOE//5yMd/qxqnpcw1Co68cHl/4sBRbU//uSBO8Ig04z0tMoK+RtJ9paZQV8ywyTUUyZDxFbqOsphAm6XntJABDwQFhApYftqCwAAARpBZaW4tZhwGlEIdbqpCMa6iqj/P+s7sZoFf42uSqCmPl/aVLP9+o2fwHodikpDT2MRCmcX/+ZGN/qxQYs0xBJA4C8IGX//8KjsVPpVplX3sxnKQhUKQ5DT8Gl6qg3IRWG/xnWTzlZUG+5VACRzEivxuT/TZvDft9iEeSb8VySiivA72/373p9EToXICJs9MqKY6E3///61UkYPjo4RKlB8XmoUPf9wgL2PKTO9rAE4WgEQTaKd2hctgY5+FQSHQ5f26Kuc5L9RHJys3hv7t7iMiuW/xryzUxKeNE1VkUrKkoKmnEzWMHnLOOiLHQm////9+7pQeaYiWUePlij3+k4inc0sM2sLWLqASAHwmm7mYhMkiYqFo8mjDo3PShAM/Ma1W0RHi0VMXRlslke6TroWt0e9Xf17nW1AnNznzCiBGcUEZbXzHF0SjwIjrI2y///mqxpUkYNSwKicB4kCsDlHz/+dvDrpe1qLHdQAv/7kgTqiIMnMtJTbDtkX+pavWkibMpYyV2sJO9RZhkpqZOa2gGwB+Jp4zKgcVhMQCUiPxhUWnbQUHXmBGwEUJFlqyA2W0Mj3SdgC1uj3q7+ve6+sM9uc+xSCW6CVO3vplk6ycbiijUT9X/////+3pqrmGqxFMRvNCsgpTP/42x63l+qtP2GQKBgoYZWro1GKQxooQYdbnIdRgwqOowccGBEosDSZAnN1Hv3KeOlILs9ve8eV2UR0gCZZWilzWEfvZ3bXSQiCbAijmdV/////5hMMF9ZRREitHuTV6uX4t/p9jXhAoGCyDI0lLow5yM5BDCJ03zKMI8h6EqAA6hFRKIzdSF7pffaQbtb3jjertYmSAOWVooiGCWWUqTdJjIMzBoo4zqf///+cPTR8cIokCqKQZAhHovH7f9NAhAXAT2IASATQ4IXWgWA5gwtH8FmC5ACKpmF0z+KRvHgJqaX9L61WhbNt1MalTvOMTiBYHvZu2brHzokoULLjdRjGQdVpyeYHN9///////7YHcanSWIZBkCCCQw/CYkrOWmv0B8KhU//+5IE8Q6jFTLQG5k58GOmWhdzKz4LhMk+bZX4gV0ZKB28qPgEQk9+ZQBCAuAqsQAMElh9RCmhAATAxMPYJsJQBIK5xCaegylaTE1NN9L61WhfLb8Y1Knf4zuOFgO9nDY3LHkQKMBBxcTQwxEIlWOPlCFX///9UEoakRHEIXFIAouEoCwilnL/xMw09oCUlRVAVomMZ8WFxZU1Zyj7BgdR2FPUq3C0ggb7ernqXpLdnMrR13h/+vSWCAxOEZnz9tjtwgFu/+q7aL/HTSJxCTEJzQ43/SPWIBbclBFCvyQyA1tP4W8UTcVMlaV3HVEuulK5zYkWcLSGB2J6LVz2b/uaxavD/8mwrAQY7frW6y3AQV/+v9f5SsYUnCzLMy///i8XEAaUIwEqpYm/1aEAgAcBcr8mcxAhzMPlcxoFDAbPNPUwRhsyUYig0MMMjoW8DBw0YvKmA/U4yzGTSD3W+JT+E28aIzvEgnGX/V7EG9mmUNsjow2yE5MfOGhY6fc8fLK6P/66/Wp57mhMVIFzQBWCYHYPxYIhE+n/+iORaDOCXbqf//uSBPMIg2syTxuZWfBlxknzcyc+CdjBW0yk7bE3nirpoxWyuYBEAcBrMtNTElFzET0yAIBMybl9CoaZkjFAwYEQG6p5b4eGYFXRFrDiboqL4G+VT+FeC1fRoqA9LG2azECzTsPMshrNkIzy2FBzby54+WV0f///qe7qJSli6gti4SxuQHCL///Oc3KkRyHdIEAFAGgUj9jxRYBAys4mATqBtIkykbVWCwEY6LA0BDkNDzKR8rj3Nkgw9+/srKZGcr0O7no5I6nNZ+5EsaXJdGtmUsngrZ42Qf///11Ew0ZBjRZizGUxCbP5OJVuf9jQIAKANopIHHhzAKCa/ksTkBNQUyEVZEXkMVEC64kZq86kvLF8d5Bvv39mSmSHK9Rdz0nI6nQT9yFoy4tm1bUZgByxMY////rQYEjGMWU1mIpUJs/k5IgmTXW6L6XGPSsAIAAAgEolBixbIgQoXuKSD3HQQSJGCaQJcDNx1E4hEWgiCCE73nkUKuTnblrecw51oUMhxfcvmZZK4XXaO+coZaXJyoaKBhnOERoYMP////kZeP/7kgT6CIO6Pc8bmzpyayfqA20HwAvky01Nva1Rfhlp6bepqtxagucw88s//wOn1WQxDBi4uqsYEeJriBmOYLTAiIkSBCAYNgPXGXz0OJjgNw9BKGzeBm+uRTK5a3nUe6XCBp+Ey4vHYchtz5Wo+2eIKai8m5UEhMCDOcDwtBAwfb///8iwy4PwygacgPnliogOA1RMkyITQc3TMDmx2iQBDHKGoBEOLMhGPawrWEWhoYACTuOBC7V/D85jXw9GzAj4OjMd4lYhE4aEeYBr1fYQyQ7Flscah5YkNZv///6OwpB4QHTjzlOYqHv+syMCgPgvkDHaFn1+CzR/EFqjyDTEUVbsMiuw1MO2QsBhEOPpI7V/DdeOXNO9Tg0WTUszw1oGMojceCPV7DCAuKYFMhrGEa////o7CICCgihzKap///5hEEnJgfkt6gAAJwIWxEWTGwGSAMEGDSpN2oGNuwJDJ+y6qcI6MNAXVOO2aRKbl1JrP63LsPtJSESBEIqGnhFSSzz/SqAoXVpeSp+IljWr+AIqxrL////uBQ+MUjKokkf/+5IE6Iqi/THSO1k6dF+GOiBvJ05LqMdI7Tz2QXAeaZ2WFsq2FXu+sAAF0CRuSPLIIBy2SADWTqU+IHcJW6H0vWKpviQxL0w415Jq7PYaz/Lm5e+qwizUQrtccUOtCpYaH1Y9EJC0Vp8ASsa3////MBQ+MGjH5VbVlZv55oeEyMnTCSB4AIUtDSyoYOwMsBXy4xhkbEsAZZJ4o374YPqVDwSRCkKGlmc/X6eoZiMEUiutNIazIamm5xJhs8+j///7URSoORFHC55nkDz3SpleYzptSjDpa6C6tYURUWlEyZUAQnkkwDoCuB6ommUbfx6WhYIvgp9piJabVIpSSUsJfZrK5G+iU7e5uP4yWff2NtzVLJIB0mDQpUiehkrZZuCI1AbF5Oj///7URSoOQejgyPkOhj+v6abTLljWJOpvMscd+wAinaGICFg7UW+Viae6ZNkrpK+cfpwHSc+D12bLlQbD0HpOj/GRU0MomwcpPCwlaWJMzhcfdrypxp26zLe//XPcsaJQePkB0Xkh0yKSg2JPM6GoqabKjx0cUMDaqJ4e//uSBOsM4uQu0ht4KvBcJ4pjaSWyi3D5TGwk7xGLn+hNlJ7LhBHttprTxvXPu9gAACgDClIHhRBXzuUeGVxMJ8aUWe9BwSTloyq9WJEYlnEkTUGtCf+IZd13iCUSLRLSqLX/f//8/djpEYNuYSexKZIwWO8z6cbf7KoaZNmrZGmP4CewTXKN6KbXwl0ACSZAKAldA+UomZ4k7w6BJQDsoJG0jLVm4OFDAOBwwFwTZM5dLcZ/ueHarqv6vF7sH9t6QCDS55QV58VmaR8gJyRg8LZq+yfn/9T7CwYOCQ0uSNRCh//3DTiRhgAslSQ1h8s9fUOllAAgH8CAMQ2AYsi+ZcQMSGQkhCGGggCXo+qnC8FzOAYWAuwCQxqleLRWrP9zqdpWSuSlq1Ko4s/ogBTRswUBPPhDMeFfEAbFiCQLKf2/n/9bCg5AIV3KiDD0//+6EZikZkZLJp6yjWHxMe7XpYKKBDiewplsoLPCY5pByBmt0sL5m0d2NXiyNLwHGEU0nOzpPi8OKCZjz6qTKj0oODHoo8GxlFNN//ElLETF01xUn//7kgTujINQPtIbKTw2XSZq2j0oOcz4x0RtJPZRuKRoDbSWyMatIJ3228zIsEBmfQ6njIxo0utQsBWtsCpKOa+WOtoAECG0thKlUS1zcTPlC+EL9rymhnxLXHbQ+qiyFrwECoMOWTnZ3mRWCUoiW8/3VcpnTTiQovzT4OiDCo5X7ao1N2fZSdJpx4Fyc46eqqcLG9v/2ejPVbKtzlR6qvHpybv/MxLrG7YIUT3EreESxeNACsKHYOHTRinl2Wx2wu5VG5G8hh/CwPIe8/wO2sRxkqPQ4cqN61Zr/jqR0hipsp4ABgnLjcfPO/s53zFOZhIWePjYaA+IX+UA4H/f/7Z7fTPMmL+h7JMaXHCtuaSwjYi+5gg45+K8RGiXgUBRlVJJOtKKem1iNajhP1Q5GBWAYs35maSlXFsJhOAzojmJS2szkpIjBTZPQEAZjo/H553+/6KcxgXiueRiKEQDcSXdNRcBoLt6P9ftcq3qwrhi1/7RMvrYDb9JZfmX3NUAEp/irBS25II4No3IolP0+NVRiurv1R4PLQ7DDcnJtTT45EX/+5IE3wyC+jPVGwxB9GWpKpdhh17NZS9YbDzr2aWea42GKXsiJj/XM/pmWYzICBATwl0JxeNx87///eTspgkA/Jj5rHuZog2O//+VMakv1a9Q/mBApYAJT/FWCltyQRwbRuRRKfp8aqjFdXfqjweWh2GG5OTamnxyIpETH+uZ/TMsxmQECAnhLoTi8bj53//+8nZTBIB+THzWPczRBsd//8qysVN8O5KljfOdAAAK3FJdQBbcsmRDag1eV2cXH09ndHkJIM6ECV8v1nw9olkXBJX0kf7//pU7GR8EoeMlwIgHj44y///3mXPQQkx0WKLBweqCL/7Lr6PoixeL6TbnqQAACvxSXUAW3LJkQ2oNXldnFx9PZ3R5CSDOhAlfL9Z8PaJZFwSV9JH+//6VOxkfBKHjJcCIB4+OMv//95lz0EJMdFiiwcHqgi/+RaCNdDwOu0AIO/fS8YoAZAAgAHgKbESDERDtGmiJg0UAX52zy1qk79vopnJXGNvWfa/71Gs45ozESaOyQ7T++V1MkB3zFvUB6rjbya9Nf//+rxFM1XUW//uSBNEMwrE8V5sMOvRWR5rzYYdeiqC7XGw869FcF2uNh516QFjwQeHSX88wcUSoAaAQgAZ+K2KsMyKpX7eXDelHbzvPTT7t4meXD9PbNrzNVL4dWXDt7m2jnZQuSCviWeBUUOQLHmf/+8ymc6upMwo8ZKBksPd+WUxgQf+pFetygByABAII4xrlA3bFOwOviGZqRQNEr+E7AWKN8+VNon6/eN9esbcereQk6WLdov+/uSHEIIOo8xaS4GLVDFfO15rT67/yE7yhxTwytorDoGGUJWQT///95oksc44OahY5gKAAAAMMZWTRrDhblk6OjgzVHPVp7CBYDhRTxrn9MI+fL9yxN6uU7+KpwkKhYvav+9bal24BHAfw8wIocgtL0nBzu0zLWnpq//2y7Z7q23Jc0dxcElIiHOQR////iywsZYOo3QAlAAAAQ4hsINBrCADyMI+5ItGOr5xao0Nwo2sLahqGQt/OsZ3H1HS6Nh3+v//iWJdUlC4DElOBI0jsjXDeV+CywOgYuJ8xBV86OoG12AEEA+iccm4n///3GEHTG//7kgTjgAKmMNbTDxN0UYYLCmGHXowEx1tMPG3RjhjqnZeZuvYAEoAABCHENhBoNYQAeRhH3JFox1fOLVGhuFG1hbUNQyFv51jO4+o6XRsO/1//8SxLqkoXAYkIeEGj54htrKzlbdpmySy2w5XKYxlz+2+KOV+SCDAPonHJuJ///9xBAb9mwAcAyAACGfAuLIcD5MsE8VqR9SVR3bAegCjG9Ky8d0AVAq7z/++IOVGTo4GpQjuYG4xiRCDEMP8mrGiGc74qmeKUyDvVSEN9qNem1ky5xobXO/YIylXK4ePz8gbVjymZ8ADgGQAAQz4FxZDgfJlgnitSPqSqO7YD0AUY3pWXjugCoFXef/3xByoydHA1KEdzA3GMSIQYhh/k1Y0QznfFUzxSmQd6qQhvtRr02smXONDa537BGUq5XDx+fkDaseUzPhUACpIBkAADBcQh4gKURhSyhkG91LdrkUSyGe5TjjUa5k189JFkh0ZBmVh4Jfh4Tgsj1MVQtinfOYfGA6gMJS1QVDtchrTpeeOzLA1rnH3TEvXadJmlEgZxNpH/+5IE7YAC8TTXUeNPFGFmmuo9idKMDNVbJ5npwYGaq2TzPTg2lYAHSgDIAAKDUaPFzoGWi56SmL33G6zWzLKjE9gXOYJWSl9+oThk+0Eb7ehhL8PCcFkepiqFsU75zOJWISSxPPXBSMb5resT9ki/wD3fRI9Vaurwoqgym0hjShpM2lYBZTASSJ4iNiQdQLzbaqUV4IqpXpo8Xc3EmH0XtiBIdZNTsMZLEMW1ALA5ksS57p943ItDmVvPFidq5dz4s3wzTRiveOJluNsHnIekVhRY9KMKBClOw4e2VHSCv4AWUwEkieIjYkHUC822qlFeCKqV6aPF3NxJh9F7YgSHWTU7DGSxDFtQCwOZLEue6feNyLQ5lbzxYnauXc+LN8M00Yr3jiZbjbB5yHpFYUWPSjCgQpTsOHtlR0gr+CQUo0mQAAAOudQRT8h7pdMVIFxFI3YMasR/EiSilv//8uYyGAVXE4HPKbMfC4yhcmNAuTnVpokIDmKQNEyJklaGBglFQ+WcBCJxsRo6NDADIhgcIYYX////+rodV3q5UABBsyLn//uSBOwBEuE012FvZoBdxprsMG+UC5DRXQSF8olyGiugkL5RUESuyHul1kqUjOBjAQJD+JElFLf//E0F+dpcCQrsvY9nZ2WfnmSswU64tp3EjMtMqaGrlaPs8MJxmQpXKlCWYzDMQk0jzO5ZH4rnSPLur8Ko9B9K7UltY1n33Ep2sABlNqDRoQzB40w1ZJmDkG0bdNZZlXx3MampX//mspyrEc0UgeKfWTmXZZhGTeNw54KdiF/qvLwUYOJOBajR0H6GW3BoCcvCMTgPAOaBsZAicjuJAAwlJwASSdqcSRNQMrDUwAA0m1IDRoQzB40w1VJmDkG0bdNZZlXx3MampX//mspyrEc0UgeKfWTmXZZhGTeNw54KdiF/qvLwUYOJOBajR0H6GW3BoCcvCMTgPAOaBsZAicjuJAAwlJwBSSdqcSRNQMrDUxUAQAoAxcZVCRGPCH0JwRowFj3DUXZ/aiUgYEjn/+axocM/jjWz9UZPDTLQ8+oxXkuaSinVrE+ay49aLahvVrDBmjRmZXT6Vp7KqFqpdkLQ1lLiuEcnlFFrS//7kgTxgxL8MtnpI0tUYwca9CRvTkxk21yEPZhBjxtrrIezCGoWWKH9gCAFAGLjKoSIx4Q+hOCNDAse4ai7P7USkDAkc//zWNDhn8ca2fqjJ4aZaHn1GK8lzSUU6tYnzWXHrRbUN0rWGDNaMzK6fStPZVQtVLshaGspcVwjk8ootaW1CyxQ/sAeAywAVMCowl4CyxoXWPvOhHxdenJ74ztIL4/Na7jTzAKkM4QaZ3ZN9KUJdFiEc5bGSNjBlXsVMxU3ExNXKDgXVNDWm+LJ7cXH6+hoRMHIa0//XNP7XvQtYASBhAAGYH4wLnQwmR8Y2323xPx67nJ7wJdYQrsPzWu41HQeS+sR5k9O30TkMggwTGMB8BwoVFKCjPaVMxU3ExNWNQcH1TU3Pxa+4uP1+jRqDkNafT+PGFQ9Ez4dHlCtqgABAQCAACRgpnI4mNycE+FxY0FhqRJ3GelMiA6/zWostTij+//k4cHwXYACGwJdC7khyAYOX4ajd6H5N86W5KTNEk8RQt3qsFIlHwEn///S8QtfaCIaBkYGB4sAAYEAQAD/+5IE64AC8TXVwYN6cl4murgwb05LJNVVRhkaEYAaqij2IXoUMFNOeTHO4MYtENZIw1IkzyGeldEHr/Nai3xxR/f/yw4Pg2wAobAl0LuSKguFX4ajd6vW+dLclJmlXiKO71VA6MMHCOUTDxL//6jwhePnwRALLliYEkggQCVcDsBYCy1EyvA64lVQI6TV5I1WjNioLvq774WWOMNr/5ygwfAMqMO5hmpgMqk/4VhaGUUz5G41HWbZkwgdXqNL+qnGdSiSw5ycZR3NK5eYDLyjzw4N4lBJIIEAlXA7AWAstRMrwOuJVUCOk1eSNVozYqC76u++FljjDa/+coMHwDKjDuYZqYDKpP+FY6GUUz5G41HWbZkwgdXqNL+qnGdSiSw5ydqPcla74YcaBwWvMpOLDCUASrg9ZCNOqD3cf+A3wlNaphY13neHYdMERa6+qiV1iSCMNKFfxUsglJhmfHQH8k0nZofg8obV2eYtqbm55jlc3s609FMhh72WyTVImLWJxdUCN7UxIEjci91ZNinmAEIEA5akaCAAZQtsVyCjKfXQ//uSBO+AAtk01GnmQfRaprqdPMhMi3jTU0Yg0tFuGmpoxBpau9fajkprVMJzWd6/RKmFGlkPhYn1/rVMzzMJ1JWBr/41mkhvMYn6rLgOcvsVd7Vi3IzNiu1jcak3k9q+NS0vf7i1jY1TFI39N0y9hKybYnF1RzfdEgwPsPMMS4HFFE3AJhVUbCIAGADqDKLw7nYMS6Thvc2H0NmRy1pvICIwYywAMBD2kiAXZ4FQYy0nEgNG8AAxUCC9xf8s8RHkoQyKyR93OWBqKomEmQ02cdoRDwcovc1gwUbCDl1n7R6MUjTQxAvwYKZEgAYigiwwYeDmHFQhATD1ljZ4DwaLEjwYJFCpWspgxsWMDCAMGBytjM0qzuzk5x0MuCjCgALhYQHkoKxGcUNYIzlViFMra0wlgSuk9QwOMAFkZk9jDh4xIMVobtDjxPxllz+rglTnxnXb1jMHFkmR6fikjDQoQEB8jDAdBpTIrQxv7933p91gAEADqDKLw7fYMS6Thvc2H0NmRy1pu4CIwYywAMBD2kiAXZ4FQQy0lEgNG8AAxYCC9//7kgT4AAM1M9Q9YWAEcEZ6Q6w8AJz49y45vYADkB8lxzewABf8s8RHkYQyKE192+WTUVRMHMhpsxdoRDwcovM1gwUbCDl1n7SqMUjTQxIvwYKZIdDEUEWGDDwcw4qEICYessbPAeDRYkeCBIoVK1lMGXixgYQBgwOVsX2j2d2cnOOhlgUAhQLhYQHkoKu2cUNYIzlViFMrZ0wlgSulDQwOMAFkZlgjDh4xIMVoZlDjxOpl3/9cEqb+M5fY50HFkmR6fikhzZs8MBMogp2S2MD36+i/39FVBiCAARxJodbYNxKl/npmPvO6bxiNhQkRz/+X9BRUMr/+MuBSyDZgkPQdBqFQ5OKHFmHmF6n7DYdLS/nhYdEuaGyOFxQPLN///4u69zKzBJIMQQACOJNDrbBuJUv89Mx953TeMRsKEiOf/y/oKKhlf/xlwKWQbMEh6DoNQqHJxQ4sw8wvU/YbDpaX88LDolzQ2RwuKB5Zv///uvcyV5FAABKHHYLRabsFnyYRMZJI5+ztt/19m+ac9AFIRv36nMULx4VzyJQ65SZzoW//+5IEZYiCfzNYPz0ABE7GawfnoACKuM1ebDBr0VgZa82GDXrCsjmGDoODwMMBKRZ/0zrcEwFR6HdrPn/0qoyoHAVOmP//8yxx3REjAqAAShx2C0Wm7BZ8mETGSSOfs7bf9fZvmnPQBSEb9+pzFC8eFc8iUOuUmc6FvwrI5hg6Dg8DDASkWf9M63BMBUeh3az5/9KqMqAwEFUO///oagqSQ5axigACoAK7ZYIoAqFRipASBu1GLmXmtOcJY7C/LqX9eVda+Da8j9GCEscaHe/+YVE6cjAPUqFMqlOccAoLBOuVKxzaEdqhSeEFKysbRCWNygjciFAEEp8DclmLiVV9HYSxuepLmmFJfxRLNx5JcB/Xp3/y5YBFt5u95nWqHJgwITiZ47LcAgQsE60lKwzPztULnCClUrG0RVjYKgh3kf091CdP3V+1VG4ZIBAmWQy6el9itT/HuTU7p733Wg/k1eB/ufhMHQ2w63cmZxCgUi4HyMW1h0VlDntsOZ9s+c+Z80Zcz4yw4gejhEOsIDrAAAKgA+6v2qo3DJAIEyyGXT0v//uSBH8PAoUyVxsvGvBQBmsXYYNeiWzLXAywcwk8mWuNlg5hsVqf49yandPe+60H8mrwP9z8Jg6G2HW7kzOIUCkXA+Ri2sOisoc9thzPtnznzPmjLmfGWHED0cIh1hAdYFUAAgjiUVmT347I1FjnFtN3l1pKbXG2ybs8EzdKIc8+aZ12zWSkTzDe18//HgsxAnpKRJGRpnGApBQiCjfIQtSFbu+snU5WcxSkElFxD///2kdc7eFEYplY77wvOhyP0+XMLi0uPCMem1I6oo20jMxpBa42YmYnXGqi8P5Ox4Ueuc+neswbT0V0GwoEtHOGwMhkMf9vET3ic/+7+/hv3TNxNKTUDnTg4JHEixcikeF5mNjDInJZykU+FuI+Z8w7VRh6EwS8FklV+EQs0P+8jluNv/6w2RnwqoI7zFgnGLBNIkqz677iK5/qqqnik6PW+aIIs8XMDbAJ3///m00u6XmqqklDMV/JPiKJ03pkEYiepHYpqevK62LaNETIb+q/Ey/9xuD24Mjp6CFdnstb5zUAS6NFA1Kjur2ZYOZA9cTnPf/7kgSgCuKbMtcbLyr0UsZawWXmXkr8yVzsPQvRXJkqwZYbQX0n9szZy375OZltkf3P74xm2TgPSqoEAlDizi1ufmHPdgbBhyPT3xstudOXXo79CbiHbU6N8Tp6U/zKuwT0i7/tb2yr6MArqykDiEwSlBQXVP3MoqOf0fO1U2bHmVC540EdQ853//9Ssk2NqBAJQ4s4tbn5hz3YGwYcj098bLbnTl16O/Qm4h21OjfE6elP8yrsE9Iu/7W9sq+jAK6spA4hMEpQUF1T9zKKjn9HztVNmx5lQueNBHUPDn///YlC6AggCSOIMM7IJKo5pItmjWjZgWpaLBgPaR8kBo/c3DulalIMK/I2HiCW1UwLEhcEoPA0CgrLEZv90r6JT73wunaDzC0CYg8kSUl///+ZVP/sCJHEF+dkEfUctx3qqNGjSwI1IUV7AevH+RdaP3NY7pWpRthQ+Rshb6lSAHEhcBUFgJAoEYwJM3+6V9DqfcfC6dkDzC0CYgWDElJf///Tci4v8hULAICS/Dke5osr6ZMgUkPiRKR6ttIFqQax3bP/+5IEtARirjPXmy869FRGavNl516KAM1g55zW0UmZ69zzmtqI5GlUct7CApRxztOtGf/sgATyJskh0Yj//7qssMjyTPPpqqBjXMKJoDSP+uC1My5pVD1zN4jeZFT7VDAEAAJLgSJu7OYrLrz4iUo3hYwt43cJjKxVxtwqH0bJ4qBtddoYFTBZa/fqv/7pAGVxSQqog5Cs3///zsyznbO7kNrb32UYOXL2aNcAmmGj39ZIc9pY4LmgCZEwBTAAFNz8QHsDxcsILwmri4xWTazoqZRcoXVQ5XSJgPEJBMZNqzz/1LDdUmHIeQAlEmeX205nYZR3y1yjuCawgoE5AAMEDTf4VHsGvCihjKgo0JGaUm1pH02hgApPYTEMyj5bXdkTATVxcYtM7mtjVaqV81KqJFK523KAU5ASIRSVYG7H//48EywhBrwhDo7vz93ff9dtxrv55+NdlU7YaSvCBZQNN/hU8wzUUkSihRosK2F07zaWqgoABpPYRHFf2/jmEIOogjs/ruy/u3bMKCa6hJHzA5aHVir8B0mX//sgoAcTCwiC//uSBMyAQrYyVrnmHSRa5lq3YSaWizjLXUesa9F3GWqdh5j6MEgTgqDkXQOKSikipitGc3+IaKHtt+OLWWY+hE0/x50QDK1iNTFjEp3uFkHEBQADSe4iOK/t/HMIQdRBHZ/Xdl/du2YUE104UbFBCsBKkW0mrY3///yggAs8iMGhgbCxCjgQZDFoVl1mwlNn/1Uqx8vL/uXjcpG8Me3////l0cBi7NNL6bEvycs0aGYaw7d04ISU2DawqrK4rYEEPqxrbZvF6imr1BGyoGliNehJdRem9sT/+oi8U+Do8jyfOMheZmUKbN8RHuXlvN1U97dh9RMzRadJxCpoWDXyjTbRR7QMmrMVTDLcYgEFJ7CIymLXY5jkJGn6uOrVOy1ODqqOgPZ1APSRLTgdEw1dN7Yf/8KIphnwaiQW1M2u7+Ml97NH9Lee+5M/fPhMomHNmUhp936BWaUxZk8+QNozjRjQqoTbqgAYAABSWwJxGEGGyQ6SdaMDFX7v1ah9QAFeaGywDlUKpLFFOu///5REQEhsY4PEQ7UOVm+RiMZRv6djzP/7kgTWjELhM9W57EH0Y0Z6tz2JOsr8z1ZnpWfRZBmqjYWZegJRYsYDdDBxzn+wGbS4smPD7muDwuHF7MZPgAwAACktgTiMIMNkh0k60YGKv3fq1D6gAK80NlgHKoVSWKKdd///yiIgJDYxweIh2ocrN8jEYwMb+nY8wCUWLGA3Rw45z/DpC5yiKlvkzKJAb73RcAmQAAknsEJ9bQkGp0HokFRmz9U8d6zD1/gDhzj3ViG7t0ZLODHfT/8s4QAHCY8cEhxGHtDTj4yot4zJtL/9+N87QUWeo2+4MYJj39YkOIHi0mLJSS3lARM0DY5TFhQACJWwKS85WBwRToGJgcVs/Vv/rMPX9oCijiayeIamFebiGSHLt6xMzM8dqi4DyA+oICpjyz0Kp/JaSdIhZH/ZLUICU7FeoERTP//7g0Lub2eZ7I9NS9jenjF3xBOrlRjdAFkAAAlXBIYZIijWeBHaK+pEcGbD9Lr0kA/kOsq7RtpuJIsPWp4g/z/1ReTU2WvC0dQTblK6UvAEJLRjYdz1+p5UxO0cBWR2ouXlNACZ+Lb/+5AE3QACsTLVUSgWhFRmWqolAtCLrMlVRiDW0YcmKhzBD5Mf/5c4YnOnz72Tpm1EqAACZQwi4/EUZe5vQ18JT5bJ8fOdvIavRDYTcgb2h5xmlvO7RbRGENYm+MOv0zmYQ7Gy89LUsXM2lic/XNbY43hwPNXl8o9OTLH2wsL4TNYE4hwnCcrrl4kCWjq+H/6azjpwZRtvFgvgsZXRawAQ4+seodSGBpmGLK0sX3l967Cbq5Wl3RuLMi1v3///vFgGlrgUTDxCWmdTnsw3RMQChNpdP+4I6z5H/wRtzUSpiDBcEQ////uaAx6gj7LwBWAAAIQ4+seodSGBpmGLK0sX3l967Cbq5Wl3RuLMi1v3///vFgGlrgUTDxCWmdTnsw3RMQChNpdP+4I6z5H/wRtzUSpiDBcEQ////mGuPizLsjPKSgAAABw30cIzRc7RAHCDXVfJbemDV1RNHj0i5CaWOOQroSz5ORNLemZnelt45SsiKXSqCJ2UCUcHEJ0rf0zIVKEcY7daih/f6vEU00BT11y5REGf//9IKnROaULLAoj/+5IE5ooC7TNVUexJ9GwGamNh7D6J/MldRhkvUU+ZK6jDJepcFioXEKkAAAADgDhSw1okmg4VQuZpKVx6Vy4HiDVJJSUxyaJMl0VHpRz/+/15FVYhkqVAKRixEQIE1jb+myQqLxv3Wyr+/48immgS9dddRYM///6XC7WnnLJCoLnyo8AgEcNccCTG22CGJww25q3v5f+HnZRlIhJ6pMLvSi2iIRUwOLIF/d/+765tZFK0yIiVVZUknOJXobSSWane7mVC/nlUalOG/NEAlXQEf//8NFgXCAJhwUDxIwgLGwCgAa44EmNtsEMThhtzVO7+b+Hm2VmDkP4gaCseMU8WDkGxgUKFC+Z/mcsRyhZpGh0HRJJpDDXUQcVkYMKOebuqhJ62hYZ0vqyDZT/////////////82o1LRkGxkR9RED2DagAAAOgDKBeN2ysCxg5VGDIZ7OEmMwiUGFGwAqFg+bfLZi4IBYHpaEAaEAJMEg8DEMxwRAgwYgCmLkhigAIAMAFAALjVSYHJId4GmupnJyZYoGwKAIQzDjEwoFAwEFwI//uSBO6GMxgy1bnsS3RbRlraGekgi5jNV1T0gBGJJurinoADfCjIhIwEPA0wbegltgqDLaMSJxK+MxDodQcMgNhUbNqCiUDfsykoNdKjJg5M2INsiNWR1XM/iwyxzCAgAghIEp2tmWy1It4+CM7DmTqqoTULmUgYrT1QmDIaVmgCHzEgIMIXrEgQKBJiYAUE7FxYGahc5q9rDyI2DhSN5/Z1vyIUHhG4N3pOo7frAA4MoF43bKwLGDlUYMhns4SYzCJQYUbACoWD5t8tmLggFgeloQBoQAkwSDwMQzHBECDBiAKYuSGKAAgAwAUAAuNVJgckh3gaa6mcnJligbAoAhDMOMTCgUDAQXAh8KMiEjAQ8DTBuKCW2CoMtoxInEr4zEOh1BwyA2EY2bUFEoG/ZlJQa6VGTByZsQbZEasjquZ/FhljmEBABBCQJTtbMtlq5bx8EZ2HMnVVQmoXMpAxWnqhMGQ0rNAEPmJAQYQvWJAgUCTEwAoJ2LiwMxy5zV7XPIjYOFI338db8eFB4RuDdk6Y0NT6wIAAgAcdWigToTKbZP/7kgTugAdIPE8Wc2AA50eJ4c5sAIqAu2D89YARTpesH56wAhWxnstNVacwKrtS4J6BQ0EoNEkTwI5sOoNDpsUkyiSfVZfRSbgqbjakuUzL7dR6omH25kfXPaF3H/9uOJAJZprf///me9KrAIAAgAcdWigToTKbZBWxnstNVacwKrtS4J6BQ0EoNEkTwI5sOoNDpsUkyjU+qy+ik3BU3G1JcpmX26j1RMPt1R9c9oXcf/244lR4mNX///9G3PpVDAAEirxXmHTlRKSUV0s5fqrXS9LMs0l/Ns02+HKs6Xl0Gika3Sq5ZHvP7rd4CwUhJQyaWxHNaKrJ3TcLqr2Yd39d/2gwSvXuFHA+IP6v/+/9IYAAkVeK8w6cqJSSiulnL9Va6XpZlmkv5tmm3w5VnS8ug0UjW6VXLI95/dbvAWCkJKGTS2I5rRVZO6bhdVezDu/rvnaDBK9e4USsLHP7P/9ICAASBwLt49UhchEDDHqhy6pN4upXPLhqDO7l2po6HHFg+2Iq3BfYbim6WPzLl6MYBw5TICNOpMr45XW7Hc++PWP/+5IEcYACizJYuwwydFBGWxdhhk6KZM1e57EWEVEZ69z2IsL/+mPk29trY9f8qd2LFXxihqAEAAkHgXbx6pC5B4MMeqHLqk3i6lc8uGoM7uXamjoccWD7YircF9huKbpY/MuXoxgHDlMgI06kyvjldbsdz749Y//6Y+Tb2yhHYP5O/IQ05lok9hINABJK4HqWF6FrOGKIgsQMSa8bcjuTPbsSwstcRWNL86IBU3ua7QU6eWS/t+1BG5QzpB2z/PVK3fONZytH9p8ISY+iDprknB4Zf/3a5hXtcOuA6AQAAkHgfVWrKRUba2SgE5rEDXjfE8kHrrEr6VfiJw+2cuEATl+/It8clGvWWXU9SAOxBVxR2udOLqkbOLZisYIqj/vCJxNEXFnDjX/92uKDdjZY4YkLAgBRcqselY4lt2MLpeUnk1p12y2Wnav7G/lUymIBOvJ1gJYqkMQoQwDqD9SI3F4nKDlAcRxH5mTv3veFtKdrc3IdP//xCPG+3NuKye+SCAAII4uYtSpxwrztEYi8puT+btlsu9v/DjyqZTFRtqVr//uSBIwAQpg82LnpPZRWR5r3YeVOibjPXqw9B9E9mewdh6D6AZC0hiiLCNQ7qTuLx7BygcI4n55O/m+4XSna3XIdP//xCPG+3NuKye+3//rxXUoIAAIBHFnoxJABwR5GDWc5vC+L+fUsbbqkHWYK5GMoDuVaYNZvLG/KiZWoGG/l7A4TDEYQaQNLpTnvu5Uo9mdcQ392EgxAoiMaVgZP///YZFPuS9IQAAQCOLPRiSADgjyMGs5zeF8X8+pY23VIOswVyMZQHcq0wazeWN+VEytQMN/L2BwmGIwg0gaXSnPfc5Uo9mdcQ392EgxAoiMaVgZz///768nUbUDgAElXjdA9MQBJIEYxLvvVfyEjlRzGY5NvZoUygRBxKgWD8NcDwDjwy4gVs+g4VcLQRiA5gqxbccc1F81VzUHI//G1wHOfQzsljDzP7v/3XbbqKwcAAkocboHpiAJJAjGJd96r+QkcqOYzHJt7NCmUCIOJUCwfhraeAceGXECtn0HCrhaCMQHMFWLbjjmovmquag5H/42uA5z6GdksYeZ///U4oNukqv/7kgSngAKfM9e56RWkU4Zq9z0itIp0y17sMQfRTRlr3YYg+gBkAAAKQ4xp0+obKmbKjlHYyt0sidHjMtbkUqVjmEMxiGZ2H7I8rHAYmJSlMxCi/pOjwgsfG8Kt457WZN7+rDhx1TSroOiKCwXEzQAHg87///gZafqACQAAqAxl4kE4ZJPZSkk9jK8XpiekHL6uHWllgiSHeoXAr1OXttMFkZB0oakOuoErq6sbh8BVCy82ILi4Q/61qipu3f6tMdfNTcIokg8lQDhwCgAUn+LkpS4jAEhImqQf2z+XWks1aGO5CJMuTgShcXbHWAjoGiVJDU83P+doF21/ywzkb+krpK3OR3bkeMqDs70AgF/+qJ2KBgmKH2CLcdJFTBoCvjVIAUAE0/xcmVIRgGKkTZJP3Hy6UlmrQx3KiiXRhmLi7ZlgW0NEqSGp5ufjYB88v1oq0N6qH9rNPTVfy0u6/i8dMD6u+BY1/++nHXAdz9wugXdfjVIqADVAAlp/hqXBqnoGpna9Eu4jvLOXXKbIvDRQDRJE2BQEFCwh7iv+dmZmrc3/+5IEvwACqjBXUww69FFl6sll605K7Mtc7CRL0U0Za92EoTqHVv//Zapr/WG+v28k2DpXaTpMG/0JMlXpJHx8wnXTf9xYANUACWn+GpcGqegamdr0S7iO8s5dcpsi8NFANEkTYFAQULCHuK/52ZmatzYdW//9lqmv9Yb6+G8k2DpXaTpMG/0JAS3yR9ewlF6Wvd40AZQAAEnsNDSIGsiFHmyxpbvTlsFI6pVzRoSIlNAaKEqEEDJtcqkyVczLZfOApw4ZmM7kIRvznOYTJ3ZRKnoewiPANRcqUDwEAJW//+zkkcj2H1kMjTiR/T3h4AARew0IMMtKg2jHZXJrvuWWwUjNSFro0JESmgBDBKhBAyPoyqTJU8zLZZ/MQzQSdknchBBvznOYTJ3Yol+mxXD6i/QDAAASt//9jkQjs8cL4sGwRUbY8eDrkr31hmoAKQAACnuJm+Tedo2KlDQ5AZ0dhx8rbPLm40O/E+7g5EupENiqdgODApkxs9vjovCINEggJmsQEmQYp613N11VU/Wo8UJZE0JRseTr//2PdTD2nur0//uSBNUAAm4zV9HmQfROBmr6PMg+izUJV0ekqdF7oWqc9JV6MarehrGuzGjZRhCMRLkZ1cwAqAAASf4mb5NJ2jMSUN/IDulYcfOoZ55v0PeM70JRnU0VHJ2FQwKZ4tfX/qeHQWODh1zBXMoZ/+/nLUz8yNj/+eShjeaoeizn8/////mW+KF1GylPEdXXxwsK6UpQcxDxBwSOeWHABqAAC2/xm2N6th24TBNHWLz7V3JqQfrEsSJ3Ub1rXIA6rCRrZWr/5LNDYPDoOOOtpWf+aWlHNf99NS//1Ys1Wl+lDWf0iGPrEsXQU7h8ixVdi1gBqAAC2/xm2N6th24TBNHWLz7V3JqQfrEsSJ3Ub1rXIA6rCRrZWr/5LNDYPDoOOOtpWf+aWlHNf99NS//1Ys1Wl+lDWf0rj2i5KSQUQpph7EsLM9cAoQAgW7+KlgmBSg7tuI0e67ZpMMtIoIubw6iC5E043G0XqJi00Ohgljs59czA2MhbuG+bF04O83TRUtvbLz0dT0jRP+kfUXKfUXTxeNrq3/OQ/3XlkYU7S8X9oIrcj//7kgTpgAMuTFXTDDn2Zql6ymGIPspozV9MMQfRURmr6YYg+uXdPz/40AUIAQKl/FSwTApQd23EaPdevIPL4TqSbUPDFMZzqFEhzwl1RuVUJCFY2z2ibrj+BGhIPZJ4z0oEZM81Fb2zn/zD/zjhcKgWKemg1LDYktq+qMf2TnpoWOCxO+EDASFAHc1/uYCAABq/gyF8LWZgcSE0JsnUpvNKbUT67ENaI9G2i+rl+JJi9BW5HfKTMy9AaDMC5UOCOIACHxdGf68oiT5Sv/OKiIwjQ58DBWvj9NNxk2fDpQ0sOTKLEPYcBAAAlfxWaMz+ueUSpaH3bX2/E99dojvsNbKddH8Jds/jzNkjO0vcTw8U/z30iMQZI0QwIMUANEsmjF/+prfVnftnjUajiP2a9rnHG9fy+dtlNsBRduKb5ArCP26rih0IikAoAAATn4fvAnngnGlbe+d8kRAzkbSdmICcLgS3LId1rFUZKsS3KUv//jzQkQhoQgkSJ9Fr6nkIdBTH7dt/1l1Vb/bM42GGlpwnBAG5t3/lRUThaebqHwIdDQr/+5IE7oADJTPXUwxqdmPIKuph516LFLta6bCvUYKeax2HnXs/Ek0mm8AKAAAE3+M8EoqQTjStvfofypiBnI2k7MQE4XAluWQ7rWKoyVYluUpf//vNCRCGhCCQeDcWsfD0iYpBnnyf8VMRF/IiusgsOEJ0dEF5f/////1KMB9laOJkgiwIJa6zZkwA0ABAKY4azDMYKAThJpZpZuxqhEHdTKMYC3FB9wHfU2VnUHDX3HzPXaWEsTwMFsfSWRDNwQ+M+c+FBKpseqj51UzG2+f+eb1nT+v1ZVRI3HmHqLDxihz//+trQeMrJzx1GgANAAACmOGswzGCgE4SaWaWbsaoRB3UyjGAtxQfcB31NlZ1J/x8z12lhLE8DBbH0lkQzcEPjPnPhQSqbHqo+dVMxtvn/nm9Z0/r9WVUSNx5h6iw8Yoc///l8TGYfvOuUPoBaQAAACOKj3PIV5CThY4TzmNkVgzbO6s46TNVtjbccinWrlav//zl+ZiYDg+EcjHsKUql1DtZ+M/XPPQLW02Qf8L31znKRU7tzbN/dxKW1S9X///l//uSBOyAAwIzV1H4SJRgh5rqYShejFDPX0ehi5GBGevo9DFyQ+qFjh9hchJXgLSAAAARxUe55CvIScLHCec9sisGbZ3VnHSZqtsbbjkU61crV//+ZvzMTAcHwjkY9hSlUuodrPxn6556Ba2myD/he+uc5SKndubZv7uJS2qXq///9AfGQtaBRVlqDQAEkRIKAAAwgdVhyK46oBJSwJFhJdbryoY8sjPYrW2gbLInBm1n+TPMsyuNmydeGEaywDAsHewMe9ElKeD+fn8K+OGiebYoxQscxeZv6421U4jhXtIP///+SAAkiJAQAAGEDqsOQ7jqgElLiRYSXW68qGPLIy7Fa26dmycGbWf5M8yzK42bJ14YRrLAMCwd7Ax70SUp4P5+fwr44aJ5tijFCxzF5m/rjbVTiOFe0g////5KAN4BAF4zkcmRYIbqLCmOe54nkjhw7IKUwrGTPqHY3//ziCoSYQ3Ary3raEDdMYWM0yBv1AZLcch/vR6hbh6VpOtKy/UCpVUCOzLtmUcZKnIk1CdR+OacJ2bkY2kWcSmZvAwRpP/7kgTpgALxM9dR6WJ0XgZ66j0sTos0zV+mGY1RZBmr9MMxqgNoBAF0ZyOTIsEN1FhTHPc8TyRw4dkFKYVjJn1Dsb//5xBUJMIbgV5b1tCBul6FjNMgb9QGS3HIf70eoW4elaTrSsv1AqVVAjsy7ZlHGSpyJNQnUfjmnCdm5GNpFnEpmbwMEaQAJUwAuToUQnUMExl2NVOoaVYhuzZWS50x3YwaUi9FHP//8WgaULMc8peSwKRcCel0M0gpynyyLlhuZjB1GJSghEujLTBi+THnzxMCJNRtrFpr9V/CevHa6d2WSMwAEqYAXJ0KITqGCYyxjVTqGlWIbs2VkudMd2MGlIvRRz///FoGlCzHPKXksCkXAnpdDNIKcp8si5YbmYwdRiUoIRLoy0wYvkx588TAiTUbaxaa/Vfwnrx2undlkjNVACgAAABDiCQOMux58Ksk7+ZPsSyh9Y+X8B48QwmOluohEc0hLye/u/+JUK0h3g2SiFHSQ4AYPPJF5sxZ5CUbOEtSgiupwZprtWk1frM/6oURF0KFlQCKu//9KgvOnCT/+5IE8QETHjXVweN6cGQGurg8b04LpNVYhL2YSXUaqxCXswkuVMzAAUAAAAIcQSBxl2PPhVknfzJ9iWUPrHy/gPHiGEx0t1EIjmkJeT393/xKhWkO8GyUQo6SHADB55IvNmLPISjZwlqUEV1ODNNdq0mr9Zn/VCiIuhQsqARV3//peHtKDqHNQCAEAEjjbcUmIJGi0pAkTvWZJEW3p4CopZTwXIWYRmaLMtzloKwPyR5DI3OR+Zp0U/qfCGahyPiGLHF6lCbaS0vvdPG10uqqs1tNarIeoC6R+UpUGpUr///lkNKhkfUaioIAQAUONtxSYgkaLSkCRO9ZkkRbengKillPBchZhGZosy3OWgrA/JHkMjc5H5mnRT+p8IZqHI+IYscXqUJtpLS+908bXS6qqzW01qsh6gLpH5SlcFHfIqV///tN4Oln1Gq1BCCCnPxWiDJcFY40wRUXB6hcWyAyPTrYhXHMU44LM98aleQfAsv6qTWx2ZXN16Jr35uVJ10DloOb1JEnTv/6hn2VQ85MZt7MzCMaNsCSc0JBIhnsP7Sg//uSBO6AAxU0V9HrTERhBor6PWmIjBDFXOwxEVGFmqudhiIq07yroypoe312TC7a/eAAVMBWbgjjYL9v8hoNKsDfHFmJQ/EZXXDReyByUTJLT927nLtSr9StBd6ylzkClnZnHZ7xcyhAsuIGC0HN6ksnTv/wn20VuyVzbiKkrB5giO5oowOi7GXjShQ47+Vk1Q9k1FQASWOM3odfJaFZIZxmeCYY2xOaYzeXRaRzWsKhRhg0rvBo8znKSuwql8r+A5LWUr/qmJAOnEaH8Zq0Yt9z+vLWus5nPzz9w+GYww9KHco4IQfVAr///Cb7lHE6lABJQcZxyB8mkVlhn6eAWa/LnCM3RcLDHL0GOKxrQXrRS/ZyaplEj5Hl6k7/54RQ+SGhPhJr2YI1X5vRdWpvlmqdfPH7i8KooouahaaSxIxeuiz///1p7/YQg2GyIaXJlzYgNZOFsEDUPyKaFiUUPIlyM1n2phb9QdJ0a1mOi3BZpgli6FPDkRYPOILA9LtVPut/M8ZYbVmCkGV6Hi8QVso8qHh2MQcz/PHl+7fEJvUOvP/7kgTqgMMjNlc7D0p0Y2bKs2EotIuE0VpsPNCRZJrrzYelIsk4BEGw2RDS5MubEBrJwtggah+RTQsSih5EuRms+1MLfqDpOjWsx0W4LNMEsXQp4ciLB5xBYHpdqp91v5njLDaswUgyvQ8XiCtlHlQ8OxiDmf548v3b4hN6h9181CSkAAi2ONwXBl1/tMwlRVNB7ZY1NtYxdi8MiX/AKo5LDnLm7F7V6akGDFKsMZk1C4f/+GqfuvlMBBEoJyvbD58+Vd3k5+lOKKBNA0DKFupqmOyb7Gmf//9t2udpABKQ429ck25WlgIkSye9Tt3o+sJVaxRCrBGztA9pE6NNWYk2JlMlKBuQTkjv3S7eZ//hjp3dLVTFEHhN92155eKuz8Nz8q5JMFQOGkU/ZaHxPqQ///+taBd140lTrZI6JMpsAAPBgNHERAKuB4dCQ0I00KmdipAMImOiHWMSpGOY+pjD52nMf5tL8G9LLIDBCQgSWIj5G0HZUwm7zP7QG50VigiGSo9jINYzB7sOHKq2XWTXf+Y4bfTGqxP7SXFhw5BH53H/+5IE6wTCzy9UAy80QlymaoFl5oiLUM1e7Bk20XcZa02HmeLBceAAARhQREoJwQS0AjASgFXE4p0XIfkRj7HQERTAkBVRVCLPSkuTnahFNAnZL1IZgiMynDuMQp1i1np3zfyo2sWPOF2mdK5qCX6kLbVVE386xlGInvkgRIZhzgsPv///1oFYxEq/eQjkqXnTCMkmYGTQTSOhHQPNYHWqkGggDFhg0xxBxcwcAxJ40u6J/b7pxGVuHKAhI6SgbruCBAQg/P2vzsOymPlD2E5CtMZry50LDS+kL9uWrIG3G7JodWU/auS/T3vM+Yu9QfHJUvOmEZJMwMmgmkdCOgeawOtVINBAGLDBpjiDi5g4BiTxpd0T+33TiMrcOUBCR0lA3XcECAhB+ftfnYdlMfKHsJyFaYzXlzoWGl9IX2uWrIG3G7JodWU/auS/T3vM+Yu9QeoCJ+ZRqlaL8tYyNEY2OHjakEY1Egy7LjGdoYLMEMeaYL8YScU10i+hjkOQlwjBLV1cuKvYmf418yTryHtc1WZ0ROIcxFnza07D0DzYCey2//uSBPKP40sx0wNZY8JmJjqTZSPCjFy9UAxpi0mMF6oBjTFpl67+Wtrys/NYotcgET8yjVK0X5axkaIxscPG1IIxqJBl2XGM7QwWYIY80wX4wk4prpF9DHIchLhGCWrq5cVexM/xr5ap15D2uarM6InEOYiz5tadh6B5sBPZbS9d/LW15WfmsUWuSZrpwULc3HjBFFwRHxxIkLje4qsaa8Ie8yZH8PbBqKR7K1NzmuWViJWIWCMncPkvawVTU1K+Vw1TnFHAqSYkk8SlJMv2ezIUwsKusipkrdzOsO48sG0GwY0zXTgoW5uPGCKLgiPjiRIXG9xVY014Q95kyP4e2DUUj2VqbnNcsrESsQsEZO4fJe1gqmpqV8rhqnOKOBUkpJJ4lKSZfs9mQphYVdZFTJW7mdYdx7g2g2DGAAAQwuYqb2mUrjmhIOeWCDrJEOmQhvgVQqqYsAkuGBK0QCu2HoefvN8JiYdSHCEJViCWJR8Ym4VIF8mP4KCBoAEU5I0nEvFVaRZfrfMsc/VtPnzlNqJD7+/bmjiJz///xR6Fw62gAP/7kgTmj/LVMlULDzRGWuZKoWHmiMsMt1QMPRDJYJbqgYeiGQBDC5ipvaZSuOaEg55YIOskQ6ZCG+BVCqpiwCS4YErRAK7Yeh5+83wmJh1IcIQluIJYlHxibhUgXyY/goIGgARTkjScS8VVpFl+t8yxz9W0+fOU2okPv79uaOInP///Q61SScBQZJgUj7s2ekcJHh25CMnFcRMI2gBXZQDZRdMvU/koS5xP8Lbcp24kRzqAHbZUoJWLpD1EzRINkQLBRow8MUzFJJFGdPXXZjMnpsmhGIu8Kv84hRHSqHEVCVP+lJ+vk6oR6mLWShpaZzGRjYuNYIG7skwh8CfBjWKchg4oSXAxZhkMN+taG9PPATpxJI5h7iEgumdVjzsPU5bvP1LJnIyHmBEMjiMOJJKJNIjhBpuSr1ylUbeQxKiRgyvcHL/okEkOqSSStRd1AAAOwpsF/SB/UaKUv4RvmRmUpgVMVlSgI+5YI+qXKvyJwU+NLUiUrnX80jtSOy1F/4BBgcFwzZMbwt2fMDofluxWMJpfViXZhLsnM1M2/6lcVBD/+5IE8o7jLjLVGyw1hGOGWqNlhrCMMLlUbLzQ0ZqXKcWNJakcW0BP/9jWzAb1bUKmUEFb6AACV8MsG+nIyrzFQAmvVHUUs6u1wmGkVmZG8TgtpK10ma0Yn8VWYF2onUCr1KjIDYz1e3y7OHBQWNkc1ppPy2uxxrlEcdrdtp8pLi0GCw0XQHXf42A2MArG2zXZUAESRxvjIc4k3egdIW/Mk/Rj4HEjC3i3Pg0y8xD+s5U+vd8vMhJ8vlqAg4oOjIbm6exKItxqDogVJ9nvCMyxdL8txH38QtScPAYMMI///+lW5pS3oABAAwu2EQaduSi8GKVBLbwo3r8jQ6kvRE8ss/wQmmbFGBVoGpNXvrxpsT8IH3X+b6YYHQsFjCjXtuKOQOgy2lkRCUy29bnUJGKjsXU1E30QrKLBoDBhhH///03ck9LVoQASSMJmnlD3cdCovswqhpYVvJlVMMwlqLpe4lpuxnODelavdR13YtpDFXDUqh9bMsrmgQATAeExgPDXEMYJg5OMIXX59FvrWuo6+CqKB8mxHhhp4k///9X+iIxt//uSBOiMwyAuVRsMHZRcpdrDYeiGioS5XGw9CZGAFyqNlJbS2GPWE0I4KhmrMyotiqCASEc6ABAWG2qiBiiesy2nYbyDFVtGc73wakDgSdjL7FQqd8+ey1o4jwG+O87mQnzfcQxgmDk4whdfn0W+ta6jr4KooHybEeGGnxAAACMJ+G19EwcPplFv18m1yY4NEysBFvSCEQE4ojGkVZ+ejOLwYMSlYDCP07UqUzxsVDCy1rjecP1cdKmYy/IogiqMqDAISwu38mi/+ZhMYHRURDiGGi////iFCXm3MMFou7aMw8fd8RhjGgUOJtsJlx8HI9mmWwkxJzkKKC3+MAGB5E7NS3HatikqyiGUcWEt6nFEI46kMv7Zs67upG3eYi8UNq3NySEbYWUoSAsFOj6//cjxi/X8flGliQFoqHIcFHCWtQQAAoocVNSlLOVQGLazVnRCnrSODrQQT4hCKPRehU1vHzv6fvoL5lZ4+sTY1/91P1VsxIoN4RrzyMb5iY71xr/6sqspS/6hwWGBYmIsLQoH3///5VMWtKClqXNIAiABRf/7kgTtDPLMMNYbD0O0W0YagGXoekwYxVJsvLERkBipgayheQ4qalKWcqgMW1mrOiFPWkcHWggnxCEUei9CpNeBjO/p++gvmVnj6xNjX/3U/VWzEig3hGvPhjfMTHeuNf/VlVlKX/UOCwwLExFhaFA+////pP59SEHzdKAAAAMJ2MId2BRJiIH4XazAAF5SYQ/Sx5z9hHsNQsGqK3W8tfex18uqiVcNYRjO5rOs/NbQFgvTGpBandCIG5ooJj5N2//Qx//UXDYoMA080Gj5wtHgx///rNOfcgxFHLpkL+EK0MnSRRPQIQkpaGMP00HAnU/C9x8txGgdApE0Q5abyv3ZVWmRZSXHm3pQsCFqs8dX8tbNh4C1IsvgDU0wAsFXFRDm3x56Z1/941/6SQJ9Yvj6/+ZVUzNSUK6HBK5jhI2FAVUAQRaJHDfIuSs2MgdulwdWWwT9hfh9VsnZZpbfKp9NtVdZ0NMCRphv5+sTPw5Jki4JENPci5Kxf3ztHOZ0/VlRB4ZICUAcRJA+Hp3///jXHh5GZLvuoBBIA4XbUeoeLgz/+5IE7wQC4THWuw8rdFumOtdh5W6MEMtQbLztkZUZqUGNPGGAhMU9BxZUgFdOqtIsLOBZKhSW3ygvy5OVGyI8o8wSSYb//kifhzYrcbG3Y6a387ucz/7KiHDJAXBMRJA+Hp3///KAEXiYUAxkBvEIAUaAAAAoeNyyz41VeJnMcRM9LzQJF0QgooRpAHndUuWBBSIpDETJ2j39mZ5SVfWxYBzlhPcYiYJHJU/HVf5Xs+2WE8ceJipq3Vmov///NMHBuQLA7B4REsAxSz///+YMDgKskNWEkCcAAACh43LLPjVV4mcxxAz0vNAkXRCCihGkAed1S5YEFIikMRMuaPf2ZnlJV9bFgHOWE9xiJgkclT8dV/lez7ZYTxx5Qqburaf//+aYODcgWB2DwiJYBiln///6zAAePGrbUgXqACRWworxmq8sJfiWhhEjMeOrBS1AzAk3gjDEcSpo30kvluvZVnsSgyFKpks5p1Gu40PX/9zZDU22sPoGTW6s//OO/+lx5ySDYoECmtT//nJ2nmoXIphqRuwTXaFFxvOI9bDfoAAk//uSBO8Iwq4y1znpO3RYhlqzPSd6jLjLSmw88RGQGWlNh54iVsKK8ZqvLCX4loYRIzHjqwUtQMwJN4IwxHEqaN9JL5br2VZ7EoMhSqZLOadRruND1//c2Q1NtqT6Bk1ur//nHf/3HnJINigQKGlfy1CCTDIUeMDQCBcTMCoKhMBmrYheBBAF1z8DGhgLoFi48bEDRMZ4gAoGAsGw66KidJQ8f2gwH1FGBLiHk/0UwVItAMWSJb//+IqOXK39RzJMzuSBYQLltf/9+bSajOrK9HJnFg526X6emPfpzvaALrn4GNDAXQLFx42IGiYzxABQMBYNh10UnhNfOBYGSCDDXMeI/0Wxk1XAuPpEmev/8yUetqb+aOZJmdyQLCBdun/91ZKWXOZVOdHPOLJnbt/Xpi0TVncLKgAELuF9CwLBB8Z80MAxKDG1GhS2DWUdleqbIkgpwSRKqgMJYO6sFtZf+I6ht2K6x1hEmk2noUHgeYSsSAVK43MebaF0kls6KgePCDARRERf/12T/0ciqeUc4eGAQosUQSB44yw4mXi6q4yiof/7kATyCMMgQdQZ5j4WYwZKgzzHwosJG1rkjPbZX6NrTJQeM+lBYSquUAMAuv/hpTQZewfZylZVp15SWFInHN+hW32bsSfsRwYv+IEFN9WKcxBDhGEKUK//RXsn/pZVPKM4EXA8dZyVnRQutvHkwTfYLLOzH5sTNS9XY+Y/B4HoKBeaNo/oAILtBf0ONM+3ipMgawvEwGrYS6kqVpRBzCPjtWS5ZLot0zIwMCoOU4i+lhaWlsOAICGnf/9mTkDpru5ywSv2////6uYew4xBioFSZ+90vMRvXu9JxyxyC7KKJv7bznVPd9rW4IApufgjRIz8GSQXHEgTiVImRoNAOMuJrBQ3C6hBhCaISV76QdOLHfSepV0dXNY8aP//an/nlxwfLDjCxhOEpN/peyN/L8/e2bciGmUvR7mPeN6f2lFgjdPfCTfRACZVoRJMy0mJ+1UJWZZqG0hTyRJD3H0Rsm4+FtWMTc4wIWJHnL4qxfxHxyoA0qwGnf+49GgQMtjxRoUHlOOPN0/dpjf8RxIPOYhAIJMQcbOdBJ48Mqhc6GgME//7kgT0jIONMtAbLC4AXAea1zBMuMyBB0pnmPaZcSWq3JOa6w0mo/utHplAC4nsEo9lDivBhLJ6AgH1MQhNHIhj+SI1jUEVLZlJKj4v6xigsqC1fyuLigoaeFlGiQWlxz7fT92mf/iOJB5zEQCCScnyf5GZ0uj5WOorMnZJqv4eWs77ld9qAAYf4QEOE8Ld2CJ1D6ltq3KVF9HGXQisyVToQCSXMIFhHqZaTM+0ou5juRL4nJvk1E0HXDTihD/LGkFbF3/2xWwjOQxgXXyLTWv05z9Lv/9FHjY+UCXcVnU9YYhfOWTTzkMr73yYsAXZPgcWhhcXDKFpo4s4udgKicSlmOt/2zLKcVDgvIUJi6F+hUCg4QZwVpWd+nM+VKO9tnq6oVCIaoKxQGe2bm/+OkGeTMSxqMYxm0wugpM6u9KlANjmwHpDUn6pknOMO0ZPlpbSD4ehkRFjZSVz0szA8jJBaeehJUH8t8up42LpceFxEnMIppMY/mK+clJXZGbNdooLDg/IgFR6jvQx85v9DhxpzeEtNZpnb9/dPLGtSF5Hm+j/+5IE7IzDFDJSmektlFqIipMxBbTM1MlAbDzxAUujqszCnmoCML8CMCqbNU+1/NJLjQIzxoMobirprKSawAsxapN4iA1xw02l74UXW412jQMx8uYn0zZpzR3FoGJ3oTz/lzSZsJC6mPg9DLGs1qt/RaqciJX/blmLD5QFoSSo5NecFSz2Ewm1qk4vPva5pg8A6QBGV+JIlwIRhwndhgewswGNU+p9XK6YASzaesUGWB7kNGwv8S4ympUHttLMBLxPWIB6aTUZzpF1dszh/8+q6fE4PthbaMYijFHojnLa7UUrGdv/0MQWHCQNt/N/zpPLo0ayJUdxzC4nbnCbBk+0uAIitxJEuBCMOE7sYHsLMCjVPqfVyumAEs2nrFBlge5DRsL/Mucagij1dhUoZeqrKhUEncynd1ZNHLqL//ZqXlwEBkcpXKNU1i8eZ7fPR2opWf/66GYWHCQNdrZfhYDocQW5hSh0+TeUEY4kxayAsFkASOWgOy4WHjsKJL5EA0ItV52ZGQ6BwGapU0h1+zUWyYuGhQ7BWgIN2//6hO0oOVya//uSBPAMww1O1JmKHdZuZjoDYSfCDdUdPmw8sQHAmOfNhhcIQaaWLLZD4kgzICM6eZaGro6tM/XE5gYk1fQKTQKFlGihQ8YDIq4JzbTYGaYmu+pVIAwDJLsBGkxJcMc6YAoUxXXRIiQQBNp9Cl79XKFnTPokGYjgpu//+mF7OQcrnAIcMChJlEWYL75lzZuD3pvveaGssmjaO8o1z2LHSkLWcIaFau5buysnr/vHfupIMtghBNkuwWobSxhxQVTysDKVKsgIwSslrKGY3mi62x1IFXDp///9aMCnKNxZxoFm91vVszetrujmmOc4xdQ6hWKFClXnFUFkys3dqcq2dB6/eyK4vAsNh6pp66kEIJsl3C1DaWMOKCqeVgZSpVkBGCVktZQzG80XW2OpAq4dP///rRgU5RuLKNFRvlb1bM3ra7o5pnOcZTDqFZQkKVe5UHmyyWd/6yOzr2Y9jFi5hcRcij3ImMOwkRURbfEA2S7AUNHsWloWngR4zL6rG0yhhMiXmuVP/fI5SBZGLcCjP//5Mo0nSeEgwqYznwsudy8lov/7kgTXgAMLNVMZiRxUXka6lyRm0stNI1TmGLFRfyhqnMMWK5n/bO/Aveb//rPUgpWPrNDxnzYv/Xl2h316x6xdDhn2aUXJX9p/kBn8+GCAAWSXAwITLNlCjFVBTwlzqOtYbSxBnKhLzXCi8axJyBYLFlgUZ///LbN7eDgwFJWMThMudncNaEmd+2eeBe4t//rPRprKcw47dP/H11s6USd/82sie23OrIpCM7oECSAVW3IDksUYyWG9WkFJsruwSJ7jvH/7SWGB51mKt//39qLBB16vTZRm8NUzNHY0nPhFOaM6VDyOBqoao8QKSjGeO4/TPuMMRNdqxv572SFlZoRMXGLxJK0i4ICEBO238CqwghlSF6dQJc3mBhfYL3Cv/Ubcvfqkp3//eTPHRQanp81VSpKX7UzKcPhFOaM6VLkcDA1BUn4FJS/Mz8zwuhOTZgVM3NcrTSLkDa6kw2xkeJKA/6+qCOErbfwjBhi6KLFZLr8VQJmyYoqKMcRf/ZTjJFyb6GRf/6ihoTPa9zpmelM9EV/NHSGBd5WoQ8lHyoINSBn/+5IE2YAC+0LUmY8zFl2mSoc8poLLbUNbRJhxcXAoayhkjbNoJhwsv7+FD9JXBaCqMa7GupXJctm/dldn9M26uBsCZJLgryaMkuEQ3Im0NvwdUBRLaFJCCrNTEX/2U4YkG4gvdDIv/9RRQme1jzc99Hz4iv3R0hiu82oQ8lHyoINcHYgmHOd9/ri/Vg3fdvFE+jsv0PjrqmJkqAOACA5JbgNJLI2MkcSzFCcdO4Vra49LJfiLHuZ97oog1HlirbQv/4cSgSg4cG4uSMkDjhr3RVwiUWNnlEDnTTwWOYUiA5gjamMpM5sOoP1mSpKfPv2I3/Fhe0KxyPM/flo9iQD4QMF6zbAHRWPVEfFZKccyjtDOTJkvhjWoV9+1h1KbCcdKbv/9aiEQcYImqcoIx4RK9WZAVK8wzU1PvmEBe2c+Zmvgc4bAxxgZQWOwEJC0ZTyUQVOR3vVBIcuuDIspNa//f1fhmPtVACGrJdg5GZt4eny115ZorSJvqXTlB73dmb1WbPJhyjxQ9uL/EAOxEAyHHVnyLG9yUeP29LuGlIL35roW//uSBN4AguFV1bkoHERaJrpzPQOIzI1DTuYscRmTIOpckw+DuW8cO4mQ4Lcu0haVrab4vmmaRQ5W+b+cYzJTex6abFYNpyP2m+sgBDNkmwcRmbbRZMjlauPNDtIm2IyQh12s3/vVjek6tI1m0BuXrf+ouwc+5nfaJn7FZtvW9DONUQPv0kICHobow3LQge05MszO/9pQdDW9RqOa+FmoceGhkrMmmbW/foQRG5aCoThkvJB6jEVKfeofM6VU+rOJuK9lhNVPavZIbavpyA+vDBg8//D1KKzyCqKUwQIlIBtkirGhG9JTgjkugXvQRpZDBjgyRDnNDlCOD1ZZ7CA97ZUvRih+eu52fPtOhBDbloKhOGS8kHqMRUp96h8zpVT6s4m4r2WE1U9q9khtq+nID68MGDz/8PUorP1pqcmpLbJKsFIhu5KcEclyC9lg6bZgxw5CCONMmlDOD1NcyhNTrZqPYUzJ09c6T+f/MeIENt2gziUnWhEFPJkv4RpkgYKaAyHbFUwkxu+xze8fAgEokiSJZQmJnf/Glo+WdocdUyCDAv/7kgTdDMMTTdQ5iBz2X2g6dzEjbsxBB0hnlHYZg6NpDPKOwyyik1eGmvkTH3c4BSYrePsCM+/f52BmL1JzyAiBKp0EaCAZZmJ9trxfTXX4CAENtygziUnWcEFPJkv4RpkYLFNAZDtiqYSY3fY5vePgQCUSRJEsoTEzvXyyVL274M1IyCDAoRSk1eVNbkTGtz2Ap7b77AjPB7fedo/bv0MturTAi+AaR9axev+z9BAA0ItJOARhgsKSBg0jXgrMUokkCFQmQN8oP1n+eIu2ym3YoOc2+gtKOH3Z2qIk7yJWxz0Q7PeszIV7uRtwwYgeszN9GrIplS512c/KpEO5lHBMi65I9EHmmqm6DYGAMItuWgC4DCEDU4iH4lldg5bBEyQjAmmAFBg31G/8+ahPtspt2IGd99BUow92dsKTvI08p1oh2e9WMyK93IXcYFAtKKdYt3kY1SjZxe5KhvNzWrugF3XdrusaEUAsJRtuADzSLHpoJhpmeHLZs60uv+cl/imFlFiRqFI/7qsg+QxVAkEot4JHpGtIGJMWg05Cl0YlADH/+5IE2YADB0XRmeccNl3m6jc844jLzVNTRJR3eWmZqVzEidvHzDrBPQW+SmpznmfP7O8/bZny93cmdirXR/ZyKgonMCIAWEo23AB5pFj00E0WTw5bNnWhMTwo37KLUcSNoqH/91WQfIZKgRhMW8GFkrrSBrviWkYoehkoAY55g1DAJOCjqC66ZRr3439I7KX7i482IwW7fjVMkseKAADwKjabCAoKlNOYhbyCZDmlJYGEf4iM9P+PkjmwPfGN/M7TBqDG9wV1yUJOlT6n7GD+NTjjVXWV7ltbKZRdrt49bbSVFbYv19/oJ6xo3SXVzWvmcmk9la6n6k0AADwTtl2BIUDRTTmIW8gmKckUawMI/QWM9PLMc5B8pv9h9bCeBlKNQkeYRTKwwfBdSDgnQRrWjvivm7bhxjtuh5V2MJWu1ifq6XbK4XrVmfaXzJgatXmM5JzqXSg/XCFlfNljcgAksLezD0pLX5XR5ZjZy/31+9n0XOYJMld2MZ2L5+BRr5RknllVBklWl6mv4nTHltTUBZm0onYt+LaRcJPjKo56S7e7//uSBNyAAtdS1dEiHKxZBpq6JKNzy4jTU0SVNnl7o2ookSJTtnT4ifyE5yKZegxttT8gkLK+bLG5ABJYW9mHpSWvyujyzGzl/vr/2fRc5gkR17YZnYvlqHS+JRJ7xURJiaT3NfxNxy61NQFhCwKJ2KuRibYXCn7Ko9NEzhLudH24DofuygrYa6dILU0ASm9QhGdi4oGux4REC8wGXCzMUNo5yDmQHGiwCk8QBCuGyyIx6T4pK2t5PwnzHiwiHY/LeNKtUPEbXViY5J6bRtgjNsmusOEwquo5CXUtweF0GMuea1pkTLd8UmJU3M6byNbOo3vk9JsAIJac2CZ5xIoIWcQCiDCgcWoW8qiztsrfhTOJl0XlATWxssiMekfFJW1vL+KM86yEOx+NnyaqrxGi0SyMbSZpwz4EZvTWl0ixW6jkJdS3BsF8YXNJmvtrYu7wUWJyphdf+HVp2TqlzdgB+xAQLK6rAfLC2+pn6psqet80ZPrje/SONRy2FggxvIi0+xnRniXeJeVK5qRH8U2IT4LTBlCPJ+hXBigr0KGRSChey//7kgTkAALJT9bQwR+OWQia2hgj8c0dDTZsvGfJrCLnHZeN2eih8n7q/nvHboqTx5OLQX0J3h40mESEAZE3G24AnMFXkQj1K1D16YWdgu9YXN0tenDAdC1zwFRfiZJxJvx+VKOZKR7xTYhvBadKEeNXqgwYoKHUKGQKQVZMz0Db3K+Zz59hv/zY6/PRpAo+Rdf2wAgeL7vBFDMte2VDFy56DksuJI5rtO3Sn9qpJr35IqmhRISGTWybBVpFKnkE0SniwTE4Jw7MMZGgUMFAHOuJIkJhJmuQ4eoq8uJCC6Z/KjMd0FCI11vg+IHYBoAgU5L+BgYXWs2NxGSvEwmGmp2tOpNxCL0F1s76/iRsUvcqEQHh5PttWdOZ6o/dLme0ClnkE0SuYuMXHp5ZOlio77qipspmuRh9FrTqhHpH2xGa2QKS7d5f9mHpMd0AIJacuAMcOyV+vZ1GcvZdBMXFdkKh5fcCNgGobbKbemKuVSk0CCHB0jOk845fX3InPpH6iHTn9z7R22Fsi0epUIFhrvA45QCOEJL9kLSjIZkamnWIqef/+5IE4AACtTfTyYIcTlmp2r0gI7PLXQNRJYR2uXYgZ92DjikzwVZTUzQHtSbG0cHaiSgBFKUABwVgRrS6ZIsK5lcW8pj/bR/F1jsgv2AnJHS6XUutzV+m06RNG1q1sZ0/2cvofcic+wyNRnTh/c+0duC2K0ey6BMMs5Ry2OEJL9p8pTydXTpkWZnNyRWrEel5SPGrfvuEowkmnICNJpEJ2NKhbNFepoECsPiITJ4zOXc1atRWYm5FM/6pHmdDmdXNe7EFPvDcLAjKNojMZK5Dt+Z9LfscKtmoDwmN875mcy0erthXBakSv3kPElJ768y52UcITcd/AgxtHYcsZrL2omVWw4CRmOB6RarK+JPtWdWRonEcFQ/q3d1D06uandiCsVb3CyNRjTZjych2/Nulv1nBjPMgEojGZdv7p/HpEeFexvL+NIUEaHX1/ftvNQAgJIu4Jxg6qGii0kUOcZlAVMFgXlWvQyhD8JwojUd0x95pj4bU6USq3rO5/5DJg0IOp7TmuZKss7OA7qdLKHSr+hOfVJyrmKKcIop/lmDwz8lW//uSBOqIgy1PTjsMGfJkyemjZeM+S3UNQuesaZluISfc844ZQ5P04c5HYzqHy/BgORgv0ApGXBOMHVQ0UWgxQ5xmUBUwXi8q16GUIfhOFEajumNbzTHxBP0olVfWdz/PKdQnL2m1XMgqyz5wHdTpZQ6Vf0Jz6olxq4sUNDmUUfIi8HA3hSSK0P0w9oz2wJf0SA8F7XUACWiVf+rwCwbsNoetPLjGRUZjEvj5qtUNHch2pdpvQ3rpCzMVXh5mc7OX75WG/rCm9NIZqUQ6aChzBZiGOSE3lN9l8m8qucu6owWWcql06S3r+OMxxBLVaT0kjbkAGTovFooW54qomKpCN7fvV00aO5Dt13t6G9dGTNh68PMzbrcv3ystzUEUzpwjNSidNBQ9BZiIckKZ/nmvTifSsn0L4gV9bBEPDGNtfaxNAAAdgEhSgHBX+mGuyGh0brvIgvGGZQEUsPjuoqQVqb3NTGbxqtbWqWw6Xjq19ZelQ5PHgJOFkObUWzryY8oNZbOpWY4TjeZK5YYjNYKyaN55TIyCL766ZdMye68dMrXLKv/7kgTogAMUT847DxnyYafJw2HjPkq1G1OEMHJ5WCKrdGYOT/v9e+QAaAABcBoCnGXbTEcIVa09oAKHDEufhPKESpuVI/ItFzu0Sko61FNUlOAZgZ1tll6rge8eAk4RkObUXHXjYpMKstmqA2DHCAxvNVDphkM1YVgIEtygIEaWhVIeWQqFyxsJVCojN8HCD///0gAgGSUkrQWNAnCqqB9lwmstZJlL2EFCKxID8foj76QdTFV3krzATObPFYWRLuczFpHRxlSBSWVKfXI6dWbbg2qtLnDb6rYyqJeCijZE/nrAR/1qR9p0WfZr/Kl3/BFPzk0Ai6lLpJG1IA8WtvFoxQZOStSg204VbAYYgkPTJippsG84Z1qvBGQyk5t4tI6GMpQKSgqlOK5Nm6wm3rA1aXOG21UVkqiXgooxkX/g2SPDJqNDC3oscyKuczojMPcgwaH+hgr6BUUSltjjbkCSq8ooxIKMyYP2GWNRiHBpk7xr2cELQiyXKwj+/sI9zqg01qdKuVXZu3ItIhJHshKW1z0Fzcj7scyuQTBH9oiU0Hv/+5IE74ADKD1NUw8aYmrmqYdhI4QMKTk3TDBnyY+nqvSXjoet1v2HzGIv6L+2gAQWUQGB7D5CcyqJbjjnVod/TEY+iUShl5w68Q0LJcrkf39hGTmqhy1qVyrlV2btyLSIRI/GVS2uaoLBYYNuN9TQhy3cmu5LftlOuDHlnTxyoPzPAAgBOAx5QeyWlBzrZSA2CIAVov9l7HLc6zBTVBdXEBWJ9GS9SvURrAUQx/zYRMlN3NkCALqXAQqJIfFnmVOM4xj2RDZrpkK1BkZHANgEyEvtpFPaMx8nwv4+RZf9/zIipHgU5eoaIv//7wUSJcAS8TSmKHBg0kDMShqHftvZfsvA11dbiTVi/aPhu0nYCG5/DNPJeucgQBdVJgTRJH8p5ltGMYz5ENm/ybWkZeBigGDCRcmkU+QUxtz4Xn/lciuU2zLLJyAqk/PeCaw6AAFADYSRJcRDNJJ2W6vAU08JZ1yKVPEzRW/MREFCM6KDiP+s4k85RkqBEOCdXa0hjmSDlJIRxzjC1OFw7KDI0jUO3in/cZzoAgMPWFlFmBxxiLgh//uSBOQAAqI9VukmGf5URqoZPYMNzTE7MGykcIGGqubNgw4QA4QHAMXIKgFpcWT//9QAAgAJCJJUrHaQ4phqisnYCRkYRdzZKexGOy4u164iQcQVIUHEfys4zZxxkqAkM1ydnpDHMkMpJCOWbDqOxYXMhAKg9A5ZYLhMAZ/JqLOQ4MWuTUfMkw4qAUkW9vt9fqAADACjbSLlhSCAj+V6rRKqfiLAy3gzi1yMgarAiAYgwhShaAUMcvDZoUNlLu659RDRYxwLphW/l+rGLWYEoRhpSsELDY6Ggs8qtTkLPhHNkwAkHQ6kO8ERZVOp4uyzV8kAA6kppLY7/iIDEdl48SlZDcNXMgbi76GQNVgRA5GizXSty8ONCzZS6QdYfUQ4sYyCmlCt/L9KMWswKhDGlWmIKkbZZE4Q+qgV4Ta7voFLHzY0NG0WFyEAiEmUp1Soz/RRADA9IpFSsEIuGQxFpZJ6C7iRX/BqGN4OzTc3UcTTZui6I81hFyThtkDOzpNlqwBPtDotZ14ZSRCo9IrE3JlzWpuffgXXwflTDEGqSdD5IP/7kgTpAAMPN89rCRjoX6U5vWWDKgwU20GniGchgyLpNMMMXEAAZOBAq4uGBxIwN0f3/bUSASpWMabo1xCxizURLeDn+jL27w5LhYxNA3rm6jgLmwrxdGuawl0gI4bdBnfpT+sIn9Bxa1vG5IhY9IrE3JvNdDp9+BUWoCbBcnuNCjo5SgAyZEMw3rZZp/2ABI7DNfifAsQUY4kYxdKisdG8g2bAbRB0FNvfETV4ushX8+l3ySwzFIYs2emzGxTNicmFA8Yc5QIG6ECUxbxxY4KKJaDjUq83UoWaFzlweWUDatZM0ueEVhKIHzKPS/666AAHwGnAB0EwgZJWBrWS3RU5qlPzg8nJ+DfKONYxZ+a1PWX4n7dBgfMVL2mRJYbikMWbXNmMy82RyYUDxhzlYHU5mZzFnIRGhjO5+ZLVnJGB8wdwLoP/Pb6/tZWyl1EAADCUG4ElNABkaXaraCUbkPasubMgKBtjlJ6o39s26dDkRZzQQxoBvfFXRlYGy+PsZv2kbwuOaK2KdZaeoxBEDjtKICQtihi3GDRzjoEi4qRQwWb/+5IE5oDC+UHP0eYZaFpHiaNhgyoMNQk9J5hn4XcbpgmWDPkQGD2mxIfCTiUj//vlwBkggqUGWp4cIYiTyYseJEIt95sv1XsOE4lXY3H/ijf6zRmK1HoMl8+xIaC3uY76GrA2Us9jO9vnC4borUU6pc6o2EjjtMQC/SwmzUIUYC8uCpYU0sQpII734mmTTf2AEAAIBKtYIKDeiIYmdmrh1XmJ2JJfT9DRKv8bUyjjfgyGv1UD+DIGxyNgvbj5NUqFmrEpRZSMsw2/f4YranhgVFjYxf8vnxNEzJjOnigGsTtaWEqwKYN6ScmFGE+zq7+rUAndHJZJJHJJPlaNuOC0KKuPBFD2OTBCmw5DUztR/BkDYeQNgsm4+jVKREysqkCCmHGcq4ErqFYzBOKjBx4GCUWNjOWLNSo4CDw2BA2bFXJUsTFUCUInjZdJ2G2Hkp//1akBeGyywQowgAKIesQLSGuasiej2aFhhvHR8ExWwcusREOgIKRxE/0hBjwdGSZiwhSWRx7T2dZFSgQoue0pUZgY/a/Zfd2X0qr/I2iHEDQk//uSBOkAAwo7zksGGkhe5vmXYYMuTDEHNUwkZ0GEmmt0YQ1mJwK8USFixizX//XsACeqlssrb3wSAsJYfStfWOSwtlQsMI1HR8NCtQdMOxLjpApHETP08M5PxYRkuEVMmMfI3MjvLOBCfPTVWIgY9CaGtYBEwoAQ214AcfDoYC4q8FxYULpPOLHNpuum/9dQBVbck/7vweRkJZyywlK1c4/NUIVrX02h+o0gMQo5xCHeGhR318yLuCc0Fwk3yS4qb2mR22EOEGtJYEyCo72fA2VhFWTjuzfl1/ONur8T6jGnH18qLq/3fwiJZUa9zWf73u+WSoAHuySx2STbDKT5fT+iQpGVrpm9dzbgMML544XNG4DEKP8I3zIt31dnUi8EZo803pSoKle3U+0lNCuSyZUc8ioGXYOtUqr3UWyqY7noVDWQrKdVTEn2hgC1mGust0aX3K3OLJUqAMAAAgAqEtMbikgSQq3VlpgpxDmIgKkLAp1Ao8BklxiY8jIRU2GoZgr6QefFzW2vxLOOhZ+XBU4zl5nPe8dnosolzd4o7W++hP/7kgTmgALYSU9R4xL4X6aaPTBjXQyVN1GEjNOxj6To9PGWPNEGHtNDQbITdo3//9QAFTKTWlGBKA6XRPGi2WQ3YKMvNstFFSoItEbeRklThUNAryQ/MIpmtruaJZgnCLR814KnI65mcye8rPi9bcK/30Ov/T/94oX+HpGrrGBaXebHCYAABAkEGRiO8YMLxjU7yQdRyjt/HM9FayBb1qMdxLLvC7qfG7RH082DNj+Kt8Qzb2uWvvrtkRH/OWmxNfvmOVNsQgIVAvBe8WDALkkjASGAgFxAhYlEZA3K3qCU6pjr245rNr9QAB6DaogQGHBiVNfjmC4gmbi2XJTZoUHU5SJUGcFW21F3Smi7oj6mbBoz7jX8IJ6dM1Oz0fdsb0tiJr6zUzvqWJG9j7+rx9roQUU49GC7j5MES4kDbgUcQaFDoOlRVD7Gvnex1VnZ+qoAf17S2623fgqFw+n0kRLrqlU/4zWSlnT3JXbaasW6Zg9J1jMlXU+NNc1vSyUwkI8x4CC7KSjLZHPv/9YJBcKg8JGhgHh44UVIngWrQT2kiNz/+5IE44ACrzTM0w8ZAFNGmjwwwxnMkNczTJhrwZ6bZYmkmLAugwfKWnrGxz1TN+M6gOSZKULwGUG2JyVcU9nzLuJa0l+yvZMTmFPclNtpBgJFDiPB6Q6zmSrqfGzu5Vq1ixjMjEsBBqoLEvHNN3Mw2OcHRPUfsKo58M4l6PU9/6K3xwu9t0N9hPp//O3Kj9f/w/d8AlFFIqaCIyGCwJcAIE6jC2kuQ0BO5w4WCPMmIJMSUV2ZHVSzVWrefyGq7XYG6T2O9VFpWESPSMhuBTnaCEEFgpbAtFSoCQslKWlx5WAMoLhx4kFkhQNMtqkG6ttNKmAAkBxBTAIIX1MCGoQ+IGCtkjKhrFanrWZnKauMyBEITJXZkdVLNVlbz9oZLhlVgbpPZ7KqLSBwiR7mQ3Kc7gkoWCj52f0xJTEvJE1KPg28PRQDSh08bACJ4Ztps7ttNNkAuuRuOSNpyB51TdaU08t0tCtfOrxypA+mRGvTzHrSrIyNyEx6dM3Nsjp1VaHLXtYq7WiBW4KrCVHE6qRGXEfyUucy38pZ+t+HESR3/GDI//uSBOeAAvI00ukjM/hjBpnJPGVvS8zdNuwkYoGBH6YNgwywKD6HG62DR80y3Ed2wYAFpa3JdpJtxoJQUDhnKs/Ot4P02r51eP0r0WRGvTzHw0qyMjYbwx0FnNzbzp2q05214VdujCtwSrBlCGJNVGQf4gdxhzFjT5QyQgq9ijicM3vlHJgM4gePeetZZdceUAAkEhkU2XdVNisTKm9c2GIlPEq0h+vF6kz+/yhBCtSPADb0KebZXO+6JcEZjpTJBRJEBdefzI+f5bGZcUHkC0aua+WzIeVGYqMF3umBQkcSSSGA/KqrIKuzztOEnNYupMoAACqaKkx6xu/E4NjpSiYOWS2zP08XqXmz/wIQgsck2ARvRPpsgwcjBxXES4IzHRzJBSpMFvZ5yiT5MkLYzLZQeQKo1c1/IxSPpY0DXe7mJD2NoNqD4Abzv27i7723uv/5fz7N/z6sFQHNZL9NdZNuJ2mxWhurmHnPfOGubOXMNsWFdJnLRBortdYhYxl0dyyKkZgyI4ZVSTk850gdXfBnRc4CyQodYZFTCBw9MefHr//7kgTmgALjTtXpIRoMX0daTTzDDQww9zmsGGeBnJ1ovMMMvXiDoDShT2Ii5opZ73279QAaMqIkNNPXN8J2mxWhurmHnMnzbXOPlDBYYsLUm8WiDQr3WEWR9o7jGhVDMKpNmVBkhz85/b79Lpp082ReZfSLJ4ZbzannfvcyiZ5l5F6ECmlqWM/9/7KgKwBJBJTrAl0lVCS5I7A+00xds+ajR01QYZoFIJ4Iek4z2CT0Rc6ub84sDuZZVcr9flcW3F2bhG1xSm1NgdkMiJkRNIXX0JHpzhbSZkaecKzDwoYugwgcF2C0sYk/sujlbW1AQIAACAXGYPkAUWSyLrMzOJRtpbIxiceYUokUaQT4KvS7qeSFnVzebRcOLMiKllbr8ri2zXZuEbeOp+bA60MsjREIoXX0IQXHFnHjhoGEvGoAwANhIgC4jD4YRIOSO7lUUnvbYgFNYkU2kinKFjO1Dq7rCCXFr1A9kciMk+2/N7RmYIj5vSJj/nm9hEfsjRisrhg/dLDkadexSl/ZA4gPkFLGI0PEQ5kFB5nw/y/uSUvOZmX/+5IE4wACuS/S6ewYKFhp2l89gwUMXTMzTDBiwZEepimDDLCX6XbZqTAqeuWsR0iZhFIBQrttUBPvbXbJI3NgkieenVdkV2JZ9xL0i+bW5oGu28hOdGZgiPnaRBjPk83qER8YuMQMnMMH3JOGzNOu8VWvuxDjB7K0ZpKkSayrd9f6tr5mvo6yOpkoymTWUmuju2pp/aX39fwQA90llslje3AMCqA7pQJRWdLdv1K7gtyOwtUIwxLFpun/5VSpzZYj2t3+bFJVtKJNsk4akZd1lpxMZAxKKBQUeYFTSCUqEyQXd7nuj3VJOGnn2UOZRWpGRAAKTboCbLiFVMjWUCadnS1IfiRoe4PJ5p8TsmEbEUWmBp5P5UBJ3myxHtbv8NikttVkkbIJEdSFl3Ey04AYlAAShbxEg8oVAj8wVafMb1oHuj7lXa/9dQARa0mUmiXKFZr8T2dYXTYnPzIMOtqI2KuYbIXqTZvtWQg0hGDcyQZO6oLHMstGN5miqT+Rs3ueUeovT1NEnBaqYxzQcoZcRb3BdXb9ivKPwhb2Iyu3X9Oc//uSBOaAAxVSz+sGGMhiDRotPGKPCwTXSaSYY6FamuXJgwy4V8WyZbibt9NsP6waQQ7ACwEAElOQyfxYQKnJrNXjoRZP2QwDJqmxY5htR9SFZvtWQoULr5QZCvUFnlE0ZnmaKqP5Gze51Ueopr6miJIB0KYxzCDlKrIzm0Yu37l+f4m9mIxidFqkq8r4t27W4Zc1ur/IOtiyEgAB/MVBQtEgwkVXG6IJkaJ0YwIBxG+P299MluPieZlDeHIReVVKFWdO5qf8HUZ3CcXPmXFx5UGwIHA7wVAFpZY06qPGsorFAlU2AbRq1tdILTNawa/FrUBO/X6622WW4LihI9Aw4EMqxBAGOJwvRScMrRR2RZUaIwhKYzsL5OK+amzUDQSOVrsDIKzjoMG6d4Ko69+kzf49mnHU30Mfr3u10Fr7d2cmP29f0IV7v2v9Wp0EkguQ2WWCFHscR+S8ZyopLWvwxK5h6mt3IvJwb60loM3uihQGgKDNLMgfcXJ0Wzw13PxNyKXcVH6x0SsjPvm2mpjv6meyLNPCrQ+1Fqxc2KLUEFtuzf/7kATrgAM6P89rCRjKZqc5mmTDJksYiTLNJGWBaJistDCN/5+Na0XSLNJB0yfVAh2hVk0AiZWew+AONwbbbuwEE8SOsIcZrEOvRSu++kbqR6G7kvowH5iNgr/4dBAhH7oMu216jauq3XbxNSZu1mdmmvhcuV/rXRJ8es2v3LyzOiFBUXD4SRaVF3ilQQW27SSWEWrLnxYWaDbj9ggUOWMkKjvzCQAODVYDC4iMKEoxQEjGEGMIlQwQD3OMSi0FFQyoLDYBIMCBBcpgMQmCgUYNHIcGw4wbIiZVA1p0dRXSD0h0mgdcQkGbAJOjRPl8vjuLxOogHNwxQG2DZKhMGpLGhVMCcMzM2MCsZsoxWTZgapoHVoupJJAqmyRFDxRmKRZppmhgbsYlwkzxPHTzupIv1qQLjpn0q5VLh4yWYJKQZFamRoOlZSjylOybsivqpOtH31dldq1f9/6v1Od//q//mzY4HBosMNFgxoGDHkuMMmIwcFWhGLRyAjQZkGxt4rGCAsnKYHFJg4IGEyOGCcL2C2h1yqBuUo+iubBjYZ0nA//7kgTpgANbMsydYQAAa6ZZ2qwYABehnSo5ygADCrOlAzlAAEcQEFlgApRouT5fHEXidWANHC0ALqBgkQKBOkiVCdJgnC+ZmxDCJE/UYsUzhrQOmJifMDJIwKpeMiGGhRSMUiVRTTJwwN50uEmWiJImqnQSJtloGBcTdNJaMkS4iZIoKQoo1V0HVQ9Cim88t9J3rapl1fQ9kV+u973Uu2/0HOs//V//SgQGDFlQTXoXTIYCjMZVjOUIzK4iTGCwzK4TTgpZjttWTdEszetgDIsojbcZjFhgjbQrDHwwTkZxTTSB/TpTMiKDhhMwkDqs4MKI3SXkHHKiyIBl9gFA56kbnPBAiVAFJxvTkwZMYts2kWqZ0HYTMSitDlizbtSi1jhZ5Ww79r+2IpdtZWrGWFfOQXPy7vdBS42P3IK1uM495V5jlzK7KO7yz3uzlFec33Vj/5rf7/985+tfvX591YEaYnT5d6czDSKhgjpV2s/+pgABBiyoJr0LZkMBRmMqxnKEZlcRJjBYZlcJpwUsx22rJuiWZvWwBkWURtuMhiwwRtr/+5IEfQAGSUrKjndgAMmH+VHO7AANGO9c+YQAEZ+eLOswsgIVRj4YJyI4pppA/p0pmRFBwwmYOB3WcGEELpLyDjlVJEAy+wCgdBCNz5ggRKgCk48pyYMoMXKbSLXLcx2Q1JRcil6zbtSjLHCzjly1u1+fJzO/3V7+Z9kGvyzx+gjNWk7nFJqnlPLW6uGOWF+7KMscu/nZyiu9f3V7wwVvaGUAW0oAw2NFy4y8il6hVKZ3jATUl+sPf///6FAAQSPW7a3bYALsXi1p4plM6IpZiI7IEtSLrVlxxpOpQkIGHrBZhAdRhNMM8G4R6YjN4LFGWt3UntdKn+HggqGF965iz3doxFpw9iTatbhJaoDoTUIR9/fP6Px/AgFrhM9taBrXaGDjUoAAAAEM5d9f5vfwAg4ghlU1t5oBKJrxiDoq0qMEw6Z3nyYOCaeX1NqiPEch7BPhCjzvBw0wfPdTf1supw8ECaiMcpz1plLPve6IWYmdiTtPbcMa6oNlmJH7/5/h//whAdQk2/3b3jKaqgBAO678aM5Wm8uzoEgIKAvJIuJm//uSBAoIgtE01789AARfaDrn7CAAzAzjXOwZDhF7G6udg5nqww00mC/bEcKSTj5/+R4SicHWdvuYf5mUtf9lh394uYcfLNykpVkcxC/420ZWWH6+IGusF6o5BtqiSC6RRZZzqLhfFVLFhoAgHbd+MWjP60qPuIm4uUGgeprrRadHOCx48w8weSTBy//I8JRODrO1dzD/Kylr/ssO/vFyz3NtzEx2lTEL/jbMtSlh+uUgbXe3M/x8w2198Y7DOX84Tlf17q8XyH99kwU2v/FR05Yr1228TnYCHZj3Yi+KkIBTyoKMPS6Zv/kTAIyt8XDiBYfQ3qJDr/z6VByC5tNRMzHEoalRy0zz/q3+Ve5QgqFgnEA8xIGnqLwgkeJsuCSXqsQw8sKhNZc91GCm234qOnLFeu23ic7AQ7Me7EXxUhAKeVBRjJxc45PqMAJU16Oh4oiUhzzRMS+nMwqCaObhr43Z4RQ7O9O7ntmObWv2FJ96LxIsy8jnjz5DFwPMFDxGQbUlDRdosm1NAEE7Ldg+HpLI6TpQ4piCpNgS7W9I5XGspv/7kgQMiMLlN1U55Ru2Ysm6yjFja8qdD1unmGn5ciHpTYQN44akHKKr9A8GjmMSJJaSw4eEH/LoTDFhs1jTc2M0TlR0D1wLhPRObUSJM1GaqZIYhNRvPDonBT5ub+cz5seHdm/f1fzYAAEBONuQNAREwmk0yGQgl9YbwuhsfDIqTmKktb/+kQ6yzkMtpGWti4ZBBRXLqbe3rGiO8Y0AL1Hgd6BFO0vZVUc6MKquSGIYMClNnyzzaEhLSgYj+jzboxAzBb4Ok+77CrEAKIORtyCORh8TfbnVgXW6LhM1frNaidWZ/+QlGtfhKVXlR9JOyyFLq/s1XPIu5W9XhkMq734pdY/mEFLlmy/xGMGTNSgZQevjaOvI5r17wC9tVmtjQm25aHHJKMqEvxdi8ZZQsq3Dz0xh53he/F/quGF2cZ/ihoVFmLw9OaVZMygLsvCSxX9mq55L3K3q8PGVeqfFLrH5YQUuWbFziMdjHeBqvZjaKTyO90a8DP/jm+vxAEBt7q8KEL9QkYhtKq0n3lF+E84dqzHfbWtOdL0BfB1ATI13hLD/+5IEEwAC+0nUSewa3mRJOs0xgz/MdTdXp5h2eYUqazT1jX5qNCs6tziko9Q6JyTQjV/I83FxirHMqhER/R2yuDp0fIyXQzzOqd6/DMy8tzUcQ2JNPHP3H34577qgEIAJxSRtyAugmeic2gvSS1lIdc4+6Y77a1pzpegL4OoDSNd4KwRiIQWGHVYYJVo4VDVSJNCNX9fYWOjErGdKoREZdHaZEDzUWhGS1DPo/WfqnGeIVIjHzRkEuu5tNnHP2854OACAA5E5G04HoiqrO20SPZ5vwZ4WGSL3Gr6tN0xTVlwepUl4SEaNpHWv17/KIjM0upyBhz1RiEQtrdEhiBZOhQGgguo5HwAxYmvk2Nfh/38oSFedN33v5nwqZ3kW0QggBqn/kUAEkB2JyNqQQRys6JxEj2Vlu22lBY3Waeay2KMqCAEUKQ0IA8epa4fu3+w1LKtVzfoc9c4IhLDuhQyfqFAaCPqUj4m4mnlCK/n//hISEsa5u9PR2M6CKm3oxKcSd63mcYTVAAJAUJkjakEhUQieMCAeUZaR8aanBgrq9Jrq//uSBA2AArZRVunjLH5VRmrdPGaPywDPXaegq/FYGeu09BV39f+D0RcvRBocOIf5Hg0i1pY3l5+VZAbGGKHyr8HROHSQFZ6I7/98zI7OZXQ5Poiqys3ZUZGzakHtJRNwAAEBQmSNqQSFRCJ4wIB5SL4+NNTgwV1ek169f+D0Rcskg2bHEPuR0GmtZKjeX5UrIDYwyw+KXwdE4akw3pcl5+FvXyIHSmhZllinWXehFu6/vtyFMAAAwFWna25BY/kqEPZj/tPH1I1k4UJSBfaxwdUv7ByDw5Xcwzi5i8aO3HWcU0JKMPpEGa6uykMtEdv9IkvmERoYYWLAdYfNSrSwsJs4tECRiluETCJvrY8AAIAxpyNuQWP5KhD2Y/7Tx9YlJwoSkP903P7ByDw5XLMMSLmL1a7TDHRLIfyWdzmZSGVJnav6RIvoNGhgkLZJ+j0v4zcLEteF6/2t3/F0tvX+22RGlwBBsFlu34ayrZQnUEOLbueJTCIqSeJLsWPkX85FO0XCAmdGK/nOqbSOZ5X7lrqp36kzBtZ/sJrVrrUwriwdFv/7kgQfgBKHMVdR5TP0UCYa6jymfonwi1+sMKVRQpgr9YYUqyatCXDCZEZWcU19AISlixMXGACDYLNdvw1lWyhOoIcW3c8SmERUk8SXYsfIv5yKdouAgmcjFfznmq0jizqVyVctdZ36kzBtY261rQ3rUe4NPFT/SfsFBIECaIqTpQmQLIaaAAACkASuu/FdR2HhGacUFsadNJM3QOKRAPGN50iMf1HHHhIcIDlO/+7+ynZ/8QYbB02tM3sdCbWzZo6tsNtkDbWAI64BkFkkNcGrAwacmaAAACkASurqOw8IzTigtjTppJm6BxSIB4xvOkRl9Rxx4SHCA5Tv/u/sVb0+ggM8ru6tocfvRWZ4qT/810P1603jGRF+CoP+WIl3dw77CDev/qoAYAUluFhWxUYVpWqAZBmD37O+VgfroGcGQkcVc2KOfHxyXYCgSCKJ0Nb/94FA8mE6ebIV+vcGxgVJAR2GK3HISC4lcErSlzGJ+npHi0NIQ1DFAAA0CXbvxg+j3jJ35EZCIJ8v2oHMQLCxLjPcAOdNKq4AEZyCTfqgAAL/+5IEPYiCkR5VOy9BRE8jKvphgiiKPHde7CTO0UkPK92EmdqOANoIv+5owCkgI7DFbjj1BcSuCVpRxgOMS1pOgjMmaxNMXNQwY0QAVt/FAo9J0eXXfUSIIAKwQBVn9tBjjGWtw+VblJDGv/+7vEZkmkVH/73maKGQ+5twwODHS1TmShsQ1mzq3ECj1VJ9THqgwpA8cRHNH0bD9UoIAK2/iaUek6PLrvqJEEAFYIAqz+2gxxjLW4fKtykhjX//d3iMyTSKj/97zNFDIfc24YH0532KnDI47JgzJKcCehj/CGosQEBZrAMctoOlgXgdFQABpwPbBGFEmpMxews8RZbrB8czbK7RdZ+UdBne9OX3mZr/mBAIoshtzMf9+v4vuxjlNbiOdBJG///zI9lDjh85kkVXg4q4gJ/lWhEILrOJDRWYAEACNeB74ShTpzm4wgusTNeGD4518X2ROflMgj3vSi9/pmv+sLhqLIbVN3XNw7kQYkvRbjBZKn//////iL6kyRd2uYmbwuHpzGDW/KtMMcsXpaaoEAC2xxm2SBFZJPHb//uSBFmIwpgzVxsMK9RUBqr3YYh2ieTTXuww69FDGivNhh16EAlCZlzpJ7I/lcbD0XD0AA4q4/85Sq+EkFYDjSyPZ2uwnaFN3kNYxC/g8eTljDv/+e/1P/0UuEAPGDQSCoWmf///zQALbHGbZIEVkk8dsQCUJmXOknsj+VxsPRcPQADirj/zlKr4SQVgONLI9na7CdoU3eQ1jEL+Dx5OWMO//57/U//RS4QA8YNBIKhac///9NLnVAAI0AMncmhFibwTUNACif5cGo48n4qESSIP1XhVk0LTjKufaiHkPyGIgNh8fPGl+5K+Fl7zkdHejLR+LJUru3/+qEZtZ/1QKEFlARgYCOwABGgBk7k0IsTeCahoAUT/Lg1HHk/FQiSRB+q8KsmhacZVz7UQ8h+QxEBsPj540v3JXwsvecjo70ZaPxZKiXdv/9UIzaz/qgUILKAjAwEdgAwA01SlzIlzzOXaDtwJAfYRaictpovCG/hsowsCkuS6iakdxC+J8XXT5bsyWj123oYzsURfVzSyQWeQDjSTjSE//qpK+3J6jEETjv/7kgR1DMJ5NFcZ7BRCT2aK4z2CiEnoz1pMPLEJPJnrSYSWyRcXHEQDADTVKXMiXPM5doO3AkB9hFqJy2mi8Ib+GyjEgVhno9Y5co7DgQ2snlNI5Emtx0uCBORMB8VC5IqTpAcaScaQn/9VJX25PUYgiccLi44jAARQ466MdrLpMXJ6XhGq2dTb0sUT3QiDAMQmSrizYvVZjHMsp1LsKzrd3BIIS6IKTEqdziRLM/6pstKkIdu/MbcVEB5xU4AIByp///+p3qAARQ466MdrLpMXJ6XhGq2dTb0sUT3QiDAMQmSrizYvVZjHMsp1LsKzrd3BIIS6IKTEqdziRLM/6pstKkIdu/MbcVEB5xU4AIBzr///6P+6VgDjtdV0eTF2QBlxIann5dpq9qP0zY77cNMpQJSFPqy/LKEJwfwA10ChbOY7fvvCWiR11YU2vvgSYxFIXff6zdCMuiUDpsacVFjDnUwiUTADPDIc3D08SF8DsfqtRYPuzfCSc5uYOUVjiSaK/xSDwnSBGwEJ1X173RW58bgdrdoSYxFIX72a6ualCN7/+5IElYRyfDRYGeYtlFBGewM8xbKJ1NFcrDCy2TiaLCD0llqUDpsacVMg51DwCiIZ////HoUAmArwa7lMkNaUweYVMpzVgTjTM4dvyWKRKjgpHO+jvG2W2VbhaqBJktD/KOcqYqyQTC6WPcqoytTBxhyDpc8e79Or1pMdHq+riw+DqIKMLAEwFeDXcpkhrSmDzCplOasCcaZnDt+SxSJUcFI530d42y2yfuFqoEmS0P8o9ypirJBMLpY9yqjK1MHGHIOlzx7v06vWkx0er6uLD4OogowsUOK0ImILbgRZUHcbbxlwt8msXXNQl4OKhEDNh/HMDlWyLEpGmk5TCOdDHh5eaRZf6qdhjIP+rlRvmKQU7PoVeUeUQFDAQSU87///tLzBQ4rQiYgtuBFlQdxtvGXC3yaxdc1CXg4qEQM2H8cwOVbIsSkaaTlMI50MeHl5pFl/qp2GMg/6uVG+YpBTs+hV5R5RAUEgIJCZH///H3o+pQAEUOJEkacAWai0Uq6VmkFdcx2zUrnlDZRPFA0le/gUk7CY9yJXDrcnOqEzoqJj//uSBLYO4n8z1xMMLLJQBnriYYWWSbjPYGewq9E7mawM9hV6Buwpo92VHR9ui2U5/VlK/P1KvUaYY4gER4DBN3//6vcioABFDiRJGnAFmotFKulZpBXXMds1K55Q2UTxQNJXv4FJOwmPciVw63JzqhM6KiYwbsKaPdlR0fbotlOf1ZSvz9Sr1GmGOIBEeAwTd///85TfrhL4ZT8Ke4g5HQQ7LJToieFHlXGjCilkJRDUVdX6YZlejnhb6qaJ/3baULlsXnVWH0WDrqfUcx6LVWWc/gblZW/frILAiDksD////+znHwl8Mp+FPcQcjoIdlkp0RPCjyrjRhRSyEohqKur9MMyvRzwt9VNE/7ttKFy2LzqrD6LB11PqOY9FrZZz+BuVlb9+sgsCIOSwP///9KU6u5V/2QgKB/Qog8xUnQtEYYhBd5iUsHRSieW/EiUMiVmTggAXLHgwMENZgeLpQJklBa6CFC94IO7F6r6ytKlC9pRiS05IWNu69k7Z4MNGniUYOFPqtGj/9xV4HJS/xQWr4PKD6g5gkcgACCHFPEUcKf/7kgTWj/KEM9gZ7C2UUUZ7Az2FsonE0V4HiNqROZorwPEbUlLSeLB29ZGyOmh6hRUsl4RVxj2PBsDdkH61MLnHNQ7SJyXUx2x14+Xty+mOpgvVUaMdldV14qbmKT/tCofm+XFTyK6n+KH/xcMOB1iRMZ///5h6cxHFBSZBGFC9CE2+rp7SrtflKJS2vnNqkOyt/JxfELaogScMHfftr1PqRSt3bqezWHLIl1YHvV6fKXPGpCaFTQqlee7MzdbZyKEULb2WyonuGZBmfo1vUvNzRKBz//+nIrVqAAAIwoXoQm31dPaVdr8pRKW185tUh2Vv5OL4hbVECThg779tep9SKVu7dT2aw5ZEurA96vT5S541ITQqaFUrz3ZmbrecihFC29lsqJ7hmQZn6Et6l5uaJQj///vGalNrAiMRsOiccZXd8h7DmEnBpidcmRtUbe88NlEKDEdBYFMs7FLrAKrpNpDiWkoepbqtoCIsqRppbF8LWkjoHGQBp+uxzO9A0NUxqJyFJ6T6bO1e0eOOr5tS+FKfHagZeZ2/cghiQLCgAFH/+5IE9g7DLzRUAzhC8GBmmtNh6F6LuNFWbAjakYAaas2BG1Jjikp1/W1U7ZVOzSCGgWYdnanPS5qipEQeKTAKMsGW5wiIikiypSEaRtqx/voqOdxD8uvqNayhc09nqvW7J4aOrp7EA/Fqmp2Kuvj8ODDA+NNj//+jRdQUMK0QB2G8MDFwhRZdGVWZFVRxuI5XtTGZdQ2lENEATqx0yIs4V0oZjjQ5Gg/S9uC9qI/3ljsxF+cj+nmfT1NDk2V5r64v+avfhIsVS/0FSp+TnKCAQRMHLv//8vnYRUid7YgJ/iEwBoLAQxKiektOnXteSPNwXP6ipcQQA2InE57At4/XKQhN0GdPy9sHQ8Orprk0VNlea/4b4VLz6GGFWTV/0S0/nSUEBI4V/7+RE23ET3ucmpwAAl7CXtgC76YikTNXyzZi8DFn6qdH62R3p8LQbIcCmOVrN1qYl86S8ISugJd6A4GwT0a10KoFZOGGxfERMOvfFf//JpBI6KT/4av4G0PdxUBgaFw0z6ByWKQaIjBUmAwIrLvsRrjQACVcI2xgE9pi//uSBPMOIyMzVAsrNpJaxpr3YehejDzNVGw9C9FTmavc9iD6NROFMlX0NbmNZU79SBwY5T0rZHmIBl1ICZTH2YySJQSzlfjTYKBz6UHicK6rbfxy4PuvQLc+URBqHLehdPVRUQCI5CE+hm0MIkDTnEQEDDt//33T+jtMquV3qkXcBleZrep16oIART2EEqJmucuccxgFusDQHXjWoyngYU0t4sBRG6W2dGOB8NcI+dXc20ZjO8ZFqs81v/uol428ChRMkg6Oxsfv2+flHU8+Ff+2Nf/viFElqCQZJlfsCQQIDjjnGTI8EySlkUIkEiyFIQAEEWnPxMtlAwn3IIzMdnpvMyyzdMa2/UXHJJ9RyFuFOffZGD5gIguZRoDXf7qJeNvAoUTJIOjsbH79vn5R1PPhX/tjX/74hRIOoJBkmV+kWCEcYYYUcC4rAC00DkJFjTEIACSewm5YV2RgDEIxuC8jkd3YUrll9ENO3RbGJSWh8vE4Ch8TEpUMxwEpQTg1pBWvjxrXFzAqNBuwwwfAx/vlr8adcNA7/XJr+WtzzSxYWP/7kgT3BEM0NFSbD0H0balKc2GFss0o01TsvMuRhpprqYYZOtO/LgYLD3C58Vjlk1DCAfe8V2sLAApPYSuHCvaMGQAUDtJbyko7xZW2JZXwGtvh0YZzSbaK5+mB8KI6FypFebi/CXat1RW60xpnR1QRGh8yEeneUz40GcQMgZ5UEW6mcTFwKJgYPLO/r//+lCx+qx+dmsz+Gae2tT6tdKJ/AAwAU3sJYsEbZLgGPY9klmIInzeeL6ixHSFSNIcGrJWY8/PVwisA8AY2FIH31y1c7M1u11KYjlMJEcjluf/YqOVYsX3QIClYwEYwOZw+k+jP/0xo7gagQmgxgRbr9M0v43k8nrgAAgJTk/Fh4gUeIA10gu1KC+P/ru0/BTVFDNrVrbMiWA0Gj5UNtokKJn+N2upTEcphIjkctz/6MjlWLF90CApWMBJgczh9L6M//RlERTDFSY6gQoSt7a5/79uLz6YFAALl/FA8YNBDAxFgkmciMRcEAGWEKQukCRGJkFW2d7ZPYjCBMMM7jFrwkx/6zqMEbuRjR4ohrP73P//TaCP/+5IE5oRDGTPVGyxB9GkmepNl5U7MlPlTTLCp2XOfa3WElTvxvZo57/0lJ+Ca1mJkglXcKfT3H//mygPMcskSD/NmmUMm3xbmk36u0ACnPhBiyzDOdgQTpWPksR3HnYOImyxFS+LEIcx+OGoXxPx8n6HYYE4UKY4jSE7o8s74rFBQBMTKAOBwaBwGoyvm6/+DxRej7d3/xhD6SVJjmCKWOFeJ/X//+fobKXBw6xg9FfLmKhc7vt2ccr7QACU3FPIBKMODsW5rAE6IFjkov0bm/byrZEhqJ6dhY4KviqRhUKXUh4uAfLpob3BQXYZY2/fZ7NC4Oe6TYDZYV////4W1iPlv93LF7sUg8/DkGogCOGjjzDnfHBUYJkLshu1JAexQooe3cwVAASmwjb2BlL8DL680ZCMhvXoqX6Saj/ZRjHKSVu9KWeLXmX/nYAhlxXvgBvZAQByaDonIHcziM3PW99gsOBYA7eL3CjLjWP///4bbYUrtF//Ndcn3RQsbR0IQRKD4cNIyRpj8REjwdc0WiryojU4BVMj8rQkAApscStwL//uSBN6Iwz00VrsGSiZuCLqjZYhOzXTXWmw9C9HNmyrNlacS9aJIVwYgXMynnY3cLTz8nHQxYuNmzxhPjJbebgPSxoyjP5Wx/NfshGOCFViCQQkSTP/P//2l2OgVU6/JOFAcU82RGFCywsUDXIPu///pvN5cJAACTHE3NDR4UrxjyOiBs5E78rdwfnl/nHQxYfGzZ4cF/GR/ebgLI+QhFGZ8tj/q/ZCJuBNVjJETGf/9f/7pdjoWmv1tCFs3OIcsEhCBrkHo///uB5NS1mz1oABJGGEBgQVAIfZFVzQ8o9TSu9Q2bVjeX1LsB341MTuLDIGdyGoyq5sYXJR1I1L7WTuX61FcitPBUvmF62JE4TDEEU//1KNVknL6DSqWooFGMIFAkK///6l1KaZAAJQ41Ey/lwVhkVSPPlNWq+pZo0TNvSVd7YmyLk0FpQMT0rkmCyngPn+9qye00jjHXT+AXmjk4+Wjqf/6mrZJ/6mq1TChw0EQoCwwspP//+EFE7N7rmLVBAAA0bxqIIAYyCSwhD6NXZbNGmRcWu65k7k9PG0eWv/7kgTDAALGNFe57EL0Wqaa52GIXot80VZsFNqRXBprTYedel2YR54DkPwgKsfHUlHU9oN2ZSS1iKNR84fLNb/vbVRk0ONHf7oTOHGKx6NhaPz3/934ckjhqHHQ2hZIABEjDToGQL+me7UB1jvTcO3rUUu7jFj1hydLK0eNmdreXUQB8rAAgkAS00GIeoxEq7hNsdRZjV+dJ1Usb4VkWt/9tVLmjBpv/JnGMseiMGRWAp7//9LaGMt9aUAgCk0OJisNbnjAK80aW0uFJVziP6wq6/Vb5fT9s9gKkimpmDokyGVDhjJQCc09EQuudVORMaX//1zgRNEp6ewlGudnjZQucKyLDP///MPBQInDyhqJpIAQARRHFzIXPIjMiiY1FaXB423RPkpBx8Quzv7xZ0UrFRAXKaUw5WJacMOVFJ3omKjHNNSKiA6P//+4YUWf9hZZs4ehcaKsP///wIlDhIOLkGuAV7kAAgAEklsOrZQ7RE5jk0/J6k5aNk3KPqtcfVIjGTg/GeFDjOSfHbNKtCPJdbZFM+bjrjuYiAtGBIDBJfX/+5IEzoRCzjNWuw86dFtGWqNh516K5NNbTBj2UVKaKymHlTo1jf/+iOaVEV//8dHA2NUKjn+AgamlgFCVP22CF3fI2gAgm0dHA46WZCcE+5r3tTjD2U0urpaWKbG7F8KkCO2gETkdeVQHD0DMPFAc5JWpA0Qxdv3caVDT7J1xd+xAOjg4CG+oqxf/+mVQ6/9fxEID6FJNT/MiJ6IBsKLDFs8Vc+x9KAC3DsI/AA2K6gLUhSqbJo/jLrJxuMaj1vrhvXKhfKVihK5lSoeFcwJ4fKeS0aHAUaiicoHAwo5uRSKUTG//8hRQDCDP/54gPDwgpBX//50RUqqi4dH68b/JLie1b+PYffA/aAC3DsI/AA2K6gLUhSqbJo/jLrJxuMaj1vrhvXKhfKVihK5lSoeFcwJ4fKeS0aHAUaiicoHAwpNyKRSiY3//kKKAYQ/unPEB4sIKQV/1igCi4fBsWE5Zo9TAJWxi2rNRGpUEAATSuFCVQFU2GB67rOw1eM1qWK7MiJBnpCgszlSKiFuHGVjIbwjbAc0RBzwW7wF6M+dwFBBV//uSBN0Igt8z1dMPOnRihnpjaKnGjGEDVOy8qdmDmaqdl5U69qa/wAgiU5b//7jUGPb/5nESgMDiQIx/+GPkLOtDyUisGpVOsAFI3CbJRBKBUACQbJmUKcv7TWZbshDg2u6QoKicqPTcR7e5IYrCfAr04W5wKed6pvATEZmdth0MKjtSmvgQESuX//41Bm3/qJOHREPA6AjH/4YLuUWWHFs4u0DhqRFxRZUbaVAaILyKQ3aCTHECzkaTffiOXlwsxIHYLyvmCU9FCr7KYnOQBYQob5JR8jOVysjvIiXVKfVtnSTcmp/p/ucVERev//yiA49m/6oVyBEpQUTHmP//FWBZ//W4hmG94slcIgvIpDdoJMcQLORpN9+I5eXCzEgdgvK+YJT0UKvspic5AFhChvklHyM5XKyO8iJdUp9W2dJNyan+n+44qIi9f//lEBx7N/1QrkCJSgomPMf/+KsAg9ur+4hYgS0QqLJVACkX4hFWKUkgEPQjbokJHL1F60imjmnycboYN3R7/jdCDNYIqIdDY7Zu7CthyF2hZrlqZOiJaP/7kgTaheLcM9U7Lyr0ZAZ6g2nlXouc81RMvKvRg55qjZeVenf9bbW1CyAWSqf7BwGGHcYKKRv/8BxA6p1gvsa9b0KFlQEUPjVgBRr8QirFKSQCHoRt0SEjl6i9aRTRzT5ON0MG7o9/xuhBmsEVEOhsds3dhWw5C7Qs1y1MnREtDv+tdrJULIBbVT/kFkO4wUUjf/4DoYHykkA7JhcnlbESNYBUj3GbPVePGmNikxMChKsbopJYjlFNU9x7IbYpWdi/nnUl+l/IA7guSmLiurYy5WaRuspbUsEzGzf/v2ElEm/9lFzCYqIhogA7Hb/7YoHievt9xkmXGmmk0StgBUj3GbPVePGmNikxMChKsbopJYjlFNU9x7IbYpWdi/nnUl+l/IA7guSmLiurYy5WaRuspbUsEzGzf/vyCSiTf/UXMJioiGiADsdv/tijjX/+SePPyc08zqUAu5/iHoSp8g/T8WieMqXYZozikmE/mJOo164kB+nZQsQEZB1lj7XlVJoVthZbqj4slL5Hf/z/7v8Takpf99SrHaoaRoocHf6nser/+5IE2ozC4zxXGewrZFjHmuM9hWyLcPFcbCS0kWOea42ElpIUYKQhWcdZROdDRCAXs/xD0JU+Qfp+LRPGVLsM0ZxSTCfzEnUa9cYDPT7hPoCrCCjDjmvVUKQCbASmRC4YVD6N//TO1nf4DMBCpPn9sFHUEkaKHU/+IhlIowUYjKO3zhTtCagFLZsLyUgdC0wXpnUV4bZfEr9+gswww2VUsMN/adW7R2Xiyp6rpq7js99xlL8HiTTnembz6GscbVou6KovP3W+R3C7pddu7qiPSJB4Vc1/oNueBguIhKZNARKYKRtZKUBBV2v44p0NBfgfrZYfTv3Ko44HTgQWqnBHsWdjqXrXZK4VpXpwknh2cspt22JLMOQxTHFVUVHEO3W6RBzhZneZUt70npCQeFTbt1nBMGGiYGwsBSSRU4BHmEbBcxZGqgBZZcIcKpJDmOhDim3+ShhSlMzZv25Co8X6e59HQi2UYbnG4zVymW3Y2mdORWf93JODaAUjKFDxZZ+2YzcEwfcxlNgcLumu1FpDXJtUBA+NWzbi1i19twmi/oG9//uQBOSMQrMzV5noNHRXZnrzPGaqi/jLVGyYtpF6mStdhhU6m17WzyhaueDJ1AcadaFhIBQpAILDgBZZaIcKpJDmOhDim3+ShhSlMzZv25Co8X6e6AHQi2UYdONxmrlMtuxtM6cis/7uScG0ApGUKHiyz9sxm4Jh2eZFGsOLdNdqF02dTUoIL0ppq4iomqttwsi/oG/qvWZWEWAZgqrSMHzPXU5UwkGABl1/FuDB5a8yzVdgeVKMclcx50mv1yGvN/8JbhCCz08m4wtlu29kIfOABqu36Ii/I7INHC5hRkeqvkUqmEkVCpklT+SpLucXVMHx3V0n+AOW3Z9TrT/MAEgAJl2/FuDB5a8yzVdgeVKFyiVzHnSa/XIa83/wluEILPTybg8DEcCx7b2Qh84AGq7fdEq/kdkGjhcwcS8+lLDRIBhhQoqJIQRGd3sxpCHuJrGD8797+sHaae3e1M8AJAAFrr+KrYUSqcjTogtLWmVVJ1x6NwVJsZhYg0umictR/vt/Hh2+09gEokduK7u/3tjxuY9zlqAZoMqHJr/jH1v3//uSBOyAA4o005tIZcRtBqpzaQy4ixUDWuwwp9l8IGtphhT7KUiLavCYneJWlUGChwjMJipvOsjsBgBIAAttfwiLU8q5F3RBytpE0y246Nw6ZbMLMkumh/eH++38eH/2nsJYkduK7u8b4h42ZdOZPQBzSWY5+9sx9/7lKlWrxcuFyrSqDCjhE0w2pZsdOisdgMEgBkc2FklFDAH2O0eLDnVbPE1Hyybe1QMeMyOeDsaoMJ6qcDuGCEuZIKohbV0W7oYKCHrYJXHcfSQmj2tLWrPdmUtLJp7gE0n/4YwziH5mf/+85nI9FkmhtS6xqOFBLHQTjP7hAkAMjmwskooYA+x2jxYc6rZ4mo+WTb2qBjxmRzwdjVBhPVTgdwwQlzJBVELaui3dDBQQ9bBK47j6Rk6e1VV1Z7sylpXFT3AJpP/oxico/ukf/9vV9Tt63sPhXc//TAOwUwuzb3cgqggAHaX8XLBFiGQITwZNwzI06bfDSycJKvnXjdRsTbhB9rvZDXzEfw1sMzMtWKSCtjnToxD7VSX+/eXn2zuulUAEz5M/+v/7kgTggALHM9dTCTFEV2Z6+i8GAoyc0VLsvQXZmCHqXZegu6vWardKb//4IUHg+pTLO2Y5Duizk4H/fYuGUgEggAdpvxqwRMiIMB8Vn4lSLKtPhQMXBzrtvwFhI24Iex7pCvPH8VMJnmjhBEKUZulRnsaU337nj1na8aQADcz/0q7HGiDNMaTd//U1ODwJbYOzMUJ4UON7D//YnaAEbdwmiqBLV9hm7lKKPwZhQoiTgfRizA5DsfzBs/OJifoNa8VAYdhV3971Ke/We+4nTutenv3xUM///9jbnVd+wbCxMEQNylP3qKj5SBWHt9pjne7tIUbfetrDOH2ACNu4TRVAlq+wzdylFH4MwoURJwPoxZgch2P5g2fnExP0GteKgMOwq7+96lPfrPfcTp3WvT374rZ///7Jv1XfsGwsTBEDcpT96io+UgVh7D4fZ6K+5rq9lttz49EBbegDLUIlsIxcB+il1QPcIMLDn1LhvMkgoe5kZFiWPlOm4o2FDKNOYGrUlrA36SMd4m4VLCYOT7//3kSCkzMtJEN8zIqlXkL5cYH/+5IE4YQC/zTWuwlZZl2m2upgyDzLvNdWbDFn0XIaKs2GLPrQKxeTHXuAMcY4y1DW2HJcKKY3T67hBhYc+ucN50ltGfMjItLJlOn4r2FQUacwNWpLXG5iCOeWdDEFYTkGPa7//9Egp0zLRcQ3zMfVJbxDfKjAeAbCMmOvdd///1J9IIACIGEyz9HLACjowaN1ZgrBHGoxSlqsZYJkmrGUDAjBN3tHaLTZPgebGC5V6paLuHaKvMQoCnxMtUadw6BRLf99fU1Qg6i/E0UcjDyKijDhg8YAnf//zZv2V6ggjqO4KtSG/ciM91qwwUojjUYyiTSvJi+k4wtrjumoi/C08M0kHr7Y8kcTjfcRTa+2r5Pj5UYsiQhJjgF2FAuWDOMvrArEBjf//pUgLqT8jRSyMXMVFFw/DQdEYwmUBBRPA6REYig+H4EyeZgCs2q+t6U0zY4RXrwZNA70fj7MEAHHkgZMD5Clk/28UuSMKsWZ+HUEum0xtGpdEobKDhSZ5ebKpWLDRLJA1jjH/9XlNzoJsUlqgAUTwPelMyyKAlYwVvzA//uSBOSEwoo12BnlZpBYJrsTPOzEi2zPWOw8q9F5GmoFhirJDs2hfS+lNM2OEVWvA+aBro/EdtbJC4OSkIqAcEq5/us0kINQcUuVmCfurrZXwzjtkcN0ChSGf6+3Vipyxo/mw1rHmX///zepf3uUhnbNq4CAEiuB1wSXAf+xuFcui0IZOue/iJpAqgwNKiqRmvgSyPTg1Dd9blYg3jsSsCaCQc8Y+hM01j2LxnPx17DNw/Fiea+W/oehkh2NBq4F2hN//9c6uEDd7x1YZnlAAEDAdbAhTgIT3y3CUTxIwQXD1zp3IssRqRTXamJLG6RTdO3InJMmQZQrWdDexZXMfDrr9ineJh1DxtpjntdD0rpbMMnA4eEatqbsPIKKA6h04W6F/9ZXAi9Rmsk8WIIANEwCpEhZdMDBSQqajD1rdstWgEaiN7RtK7HhGIFINlgakIYDCVhd0AdwPOGU0VT/LTWuj2cANpxopZmo8ZROEzl1g5QhPen3VmQOQ4uGY90+F8xykFyyBOQAckPGHx6wsaADRMAqRIWXTAwUkKmow9a3bP/7kgTyBMK6PFeZ7Dy0XieK42HrPstg8WDnsQmRfR5rDYeVerVoBGoje0bSux4RiBSDZYGpCGAwlYXdAHcDzhlNFU/y01ro9nADacaKWZqPGUThM5dYOUIT3p91ZkDkOLhmPdPhfMcpBcsgTkAHJDxh8esLGgASRwLL5CoIR94FqEod7C1I/ofFldZc8I+Kkmg7Ssfts7+OZ6aaz+shvixvG+7t7gUJrxib7h7kkN/FFDz41JYu5TjHKGy3U+PIE408NhBhIkv/8Cnu9jKTYAIAwFp8VBqpbOkFRIWM/hmYdfBb3FvaJVuQvbiZyaMEVD9czsccjaAXJPoxOe9jdyvu7eqBISLjC17Y9wiiOfkixQur9UtJzTyjml5t67oVlXjMKGDYhN//UanvZtWuAAAGAm6UmhUQskT9k0YJUnTM4yLKztqUo3ZULs/zETgOmIaGxksahDw2BBXo7rQ3PTzD6hmwROR8NY0HCsdqJsJb46QQmdtm3UoiE5NSztS2x46XYRDRIMHAww6//+NDu9z0Kj7AAABgJulJoVELJE/ZNGD/+5IE+QzDFDVWGw9C8GKGqsNh6F4LLPFeZ4y6EX2eK02HnXqVJ0zOMiys7alKN2VC5n+YicB0xDQ2MljUIeGwIK9HdaG56eYfUM2CJyPhrGg4VjtRNhLfHSCEzts26lEQnJqWdqW2PHS7CIaJBg4GGHX//xxn0oA8YxInKAAEBcXWxMbmi4UgJRoEqBg1yG9ZPuAy4SOz4W0yRyQDLlUSoRLSpCYyBnIY6WZXmINIMyJFgVoBKS+OgHj7IdUS/4qivc3WIa1RAfsEjCtRzzCfpRLIaHweiezGf3f/WNvccCYQ0z4ABAXF1sTC5ouFICUaBKgYNchvWT7gMuEjs+FtMkckAy5VEqES0qQmMgZyGOlmV5iDSDMiRYFaASkvjoB4+yHVEv+Kor83WIa1RAfsEjCtRzzCfpRLIaHweiezGf3f/QuPjBdiqYPsAAIA4pcQKGIPBB6hObI6ilc4Cbwq8pyy4W8H+2IByetD8miYJBeIRC87zD+dSMtS/qFbD2rGHEtUDCE6Epb9xaaA2YXhi/kYszD4U4BuGLnf//6iNilg//uSBPkMwyw8VhsPOvRnZ5rDYedejODPWmw9C9GcmetNh6F6Su/LAAEAcUuIFDEHgg9QnNkdRSucBN4VeU5tcLeD/bEA5PWh+TRMEgvEIhed5h/OpGWpf1Cth7VjDiWqBhCdCUt+4tNAbMLwxfyMWZh8KcA3DFzv//9XUVAil36YlSiVLBfBziRpGiIoD4CaRP5LjuvBhD0geJt42up0EvpZXhMyyt/nGV40pWiZjFiU52cbLlykYCudDEefJnClu4qwITUTcpJM9fVS7rqlEosc5I6oUhaD9h5MKtkO06AABEjhiei0aJY0imhOQSxwlJPhL5bO+iNy2/UTAc1Lxaqd8zIxTH1l5St650zwShT5fSHzMVfFMGWq8lfXNDhpZ4rmkKSlRzja4UhaD9gsNBSeTZD///rpmvqqlb0DoNwCjRgkPK4H0TImIenYNUZrNZ4tuvHlXvQPMyhd8mWRjQIyPqShu6o0zWYScZsnrVvOigcAL4HGejOPS3MFAOI4lmYvi+RS7Uv+SafYd22SsVL9eeY6RNTT7CKJTSUsregdBv/7kgTqjULRMteZ5R6EWWZa8zyj0IvY1VYMITqBbBosHPKjQuAUaMEh5SC+iZExD07BqjNZrPFt148q96B5mULvkyyMaBGR9SUN3VGmazCTjNk9at50UDgBfA4z0Zx6W5goBxHEszb4vkUu1L/kmn2HdtkrFS/XnmOkTU0+wiiU0lKDgKtcvlXT9ircScGSwNZjfJl+vf69KZHTtCsiS82l24k/el83HRi1Z16TLur/1MI9C2BBh27pqvnTT0xDEAyMjI1q7YyE1sCdyFZVO/9lakSBEHEFQ3/8efxpedhgAAhbjGuhtXVlnXhJqZLxrNj02qLp6ZhW4ZjRQI9y/x2JC8ExkN1ahIW8tvG/Ao3LZPgJc7xSU3CjNisIEhzChHvRmEg5nZhhh4oUiqd/7K1IdAIg8EKgt//H39b23eCuq0b42gACSOKm0mcV8wO3AaTZVkyZ+51KSq7mhvpSSqcBtLV7Gh5fnaxSnjGhuGJ//hwucljoCLUhrxdXsx17zP/44etXjL6N+eVWUkt/vqcNSbWSPUbiVXKsd3//+aT99wv/+5IE8o7DFTNVAy9NkmKGaqBl6bJLtPNabBhaEY8ea42HlXpHNj1aqMrbp6CbZCSEyTvheKcFmC6Kbl1VLl9wAlQuU2pvcbYTEptes9NyDK/+v1IKdclZiAtN0FxyG9XyGaTeVb56OKc3idZP3jN2av97a2lJ0Upk5NPNhFqPBE4stBb5YMDlvEIR10O73aWH68NPZT4jJhFDsiSiZ9I5S+1ZnrmTqpoZeN+cIphrdBK7bzxRuA+J+lqe2CtWXwd2nZlq5TuZp9MMk/5Ln8y6zlfQ5SuIljMisDZWo8ETiy0FvlgwOW8QhHXQ7vdpYfr009lPiMmEUOyJKJn0jlL7VmeuZOqmhl435wimGt0ErtvPFG4D4n6Wp7YK1ZfB3admWrlO5mn0wyT/kufzLrOV9DlK4iWMyKwNlQBAAEAcVqVOTBJ6klxOK2j5mo69916tRifehRh9AgoUHby+0mSEIgQoQDX3lEDs3mCxwicB4KmCypJS/w7DqfG//1bTzVNsq1qz7H/5/jWX17obVrAqP//6JSGzxdX3gCABJI4ypVyY//uSBO4M8vE01xsPSvRZBoqwYwZeC8jRVAwk2gl5GiqBhJtBJ3WJcTWtp0ZsZhe4K1LLcEKeiBdEdDt5f8mUBMFqEB195zHZvOWWInBUH1FKklN+PUPSvFo7/J/j45WxdtSz6psp8j8Gkj+vfotM+98Uf//1KFjjnpUAEgRw10IDpLoAgYRVx/xTtPt4SXB7YHsUpnAesn+/m+G+i8gIBNoSSg6PeO3OI88WtFZHTgSLSKNpmJ7uBmMNxnjhi38V3NWzwMoa2FYHOGk///0Z+dYBm1oKgjhroJzpHmoJGByfixxTBNt4OnB7WUaYq0YiVt06e9+pvCDHMmF7VoKq6btj11kPXLn2XkOljV1Xdm+P3fXSj0v8Sz56y9vNlOksOy47TIOnH///zIdBtxUDTWtwCQAQAOK8BlrrpYPOlQcSnWgfEXPlS//d/SHkmpnXoYpP0k/jRTa8ITmmxOsii9CP3xwjg+H4wfRSw1KzG8Jv+TO+jzKrK0JTD6wiYQByAMyt//////7MrLs9lxrnsOKABAA4rwGWuulg86VBxKdaB//7kgTzCKL5M1a7DDREXwaK52GJiItgzVxnmRZRdplrTPwl2sRc+VL/939IeSamdehik/ST+NFNrwhOabE6yKL0I/fHCOD4fjB9FLDUrMbwm/5M76PMqsrQlMPrCJhAHIAzHb//////5Sse7K6zoosVVuM2AMJJLB8CKF1VakdRMyyhdOTirnxcKhR5ccUaZPHI1DzhVopIobalLRgycgIoLzVOvYB48gcstBjgKA+Knck4mapHLMU1T/twdftER9/uslci1AjVaspAuOdl///9Y1xYS4qpRVlAAwkksHwIoXVVqR1EzLKF05OKufFwqFHlxxRpk8cjUPOFWikihtqUtGDJyAigvNU69gHjyByy0GOAoD4qdyTiZqkcsxTVP+3B1+0RH3+6yXEWoEarVlIFxzsz///w0iFReUilwuoAIgjDTSjaGVDkosMAH+4iSY0shvrKLFkZBRjMocuPc/Pe9IkxWKQlxWHOS5lCpUhvzyZz/k0gAMILZ/4ufbWtrdx/8f96Ldf6yREnTQwgVHHYdGud///+LrkHbYukANFDjUT/+5IE9o7i8lBWGwgtpGCqCsNhBbTMuM9SbKE20ZMaKk2UJtqLf1iAUuWAIq4qRpm6QrFjmKyr46KWakWcfzw5yDqj9QiNu9SsBlAP3Ez8qQDYlt/w7F7Y9bW7j/4+rvRbz/+XMh0i8KMIqCmAQ20v//8hTaoxPkVrABIAwvsxGURNLpZpV0XplgZaVEpIg0RNW4KJmk+1+wRFLDYIvT7pmLZEA3tel9OMKd1mrJ1tZ/XyGNJqvL8c0HscTte/7MpaottPfMHjI406igoCDhT///dYMDK3B6zGAAkAYX2YjKIml0s0q6L0ywMtKiUkQaImrcFEzSfa/YIilhsEXp90zFsiAb2vS+nGFO6zVk62s/d8hjSary/HNB7HE7Xv+zKWqKjae+YPGRxp1FBQEHCn//+yRclaShNQ2MoANADinmgzNkgPJSSzn1CIEyw57W5NvXCoJosNp6Rm+GFmA7d29KWEtnwYphftOEEDa/VAiYHMbSjSYDi2RLGD/xNfm3/76M45gQsgdf///WEwSUHxwdFi7iKiAgABIAoht4joBiop//uSBPCMwu401RsPRDRaRorTYQa2jATRUmwwtlGFmipNhhbKC+I5wlHEB6iAS/yrU9k+4+Yq0BFimH4YRrsYTrM5HQyF9SAMVbAB4eOTk/MiE26/o4xDifEw010gt5UEHMl0fWK13zb/8zuJDh4aJBQOKB44////07GfWAIgRxyAha8aQkS4gooy8cf1HZx4FvyQMDZAKGXFtOZtlTGiay0NxWZwL42ttOzs5rY8HvU1uVpN7LWtf4N/eX0//LKwYUUqCkFIf///thkQkSAWOkb4jAERI45AQteNISJcQUUZeOP6js48C35IGBsgFDLi2nM2ypjRNZaG4rM4F8bW2nZ2c1seD3qa3K0m9lrW/4N/eb6f/U08asEQuHSJQkhJD////Ec8cEzWMNPXAAJNoZkVEQYwyMJvZeaNRWWyta1BD0OCiCSSbQKKqpLMuiaZ1VOibd61DLWKcoEW8uFtuqDV5L2pTd5+5U6TZWiQXXiN2IzSljv/tdqMjaub9TxEEgwCqTSgPmmHL//3yL9z/lOmCU6mRGjUX9JYMFVOdii6AP/7kgTyjMLkMtWbCBWkYiaac2XlpIrky1hsME+RZJlrDYYd8gibQzIqIgxhkYTey80aistla1qCHocFEEkk2gUVVSWZdE0gqqdE271qGWsU5QIM4apetiHXX9wH1tfLEbxslQk4allUsJSx3/yzntQsULG1c36niIJA4BVJpQHxkEiX8/Pi4cPMYLNABiKCdbAcBwNNtekERp7Cs3APbIQUlz5JR/Z1csKNSaUNa8RLnaYyL4eXaUoV1ZLL7IpXBIUr36VKyIVnQlUNCGHpaG09/VzGS2sT//45qdDh450oSiM1v9uEzinQibBLKKuGqVe+kFAIjb/GUACVZ0vi99Bf/HUxYapRcZQyHJrrExcGJyZSm2Ky6ohaCBiu+SpVEndCMygVDsVhs/1SI7Iu/xQPMcQAw8cLkjRZa//8pWtKclkZjuQTHpO7uAQcX/aTYH+6VQQAhxTYR9PYBrcYG6pHTqX8NS6iX05s272mFtmzjS8kE0SOaPy4JWjb0/1iQ3H/757/O+erWeF8xeZsj3UPMZy1Ov+9nNFxIcKAcFFUMPr/+5IE+gwDp0rSmycetnQmOlNl56SLcMtWbDENEX8iq12ElaN//5xzsjnUNvBzC03Pt/b7eTvyQAKKbCA0ZjdDacCYpY0upGph86FmK1dmVgTstIaHM5+IacmnyjXCHZPrbfPWhqP/07B3Ryr0f7T//o0XItTWsy5f/+19W2eUaIgRhCIsMfx/x//2/bma0+uMh7wXDIWDyrIXuHWvIADAnV38Yt3QBWx0dlsfIEsw/hGOF2cZM/OPEJukjySBQ8hZKcofbf9yUNlpa8Wxx8mh3RMmn1Fy1/9m////jDHeSiTiTQVhIYrL///3v82TbREYDIOGpy48LOuKaljSAAwJ1d/FTvDCjgu4p76pFuv6fuB6OIo/OOIpsUoogYGIyI5y9r//XOLenred1JuiTwvopkTM/uX/9CHOokImERUAwgKBI79dIbUwwMQgIsMueLOuVSitABANoghnIuYaiYhdBYjMo5xhf89AkMjo2dnFhIchbNJyB26lgO3UujbmW5NKZS0RsbrQXC6yrS6H//zZGUZbFZCsHc7f////dkOJiTI4//uSBOaEQxQ/VTsMLEZmaGqTZeg+i/D9XUwlB9FZmivo8xW65QEDoOZ39A4dGz4qHhWTSxDmlZoDXIoACZVwoH9H1ISBFqEZJZO8POdwegy4sQtjI5oyItpENdEiw3gopPH6kEmql254KPUv1KIi4yqB8Dudvz0dn1f0/3ZBMBxJkccoAB0HFmX//bT2XumT76U3fEbho/GoAGFGQADBK0Qk9iIjxIrpULBu03OMFvJaDTVBVFAce7l+VS1tW/gq7xudi05bwoiWF5xirDBvudLacN2QERF5UKBuNkKmO/////QyVJFTEBcO2PF7f//6AOFCZ860US4OLgAEMBDC+thJV0RMb+jNyZ1Mr+NQHSKaWS+7XW6Ibw52mswLC5Jv4DwyjcdZJg8dIqooP3We1w7EBMLNDAuDcRCWPHe3///+ZUmUxBWKmEhGG5KM///y7FKqQtBDUgRAEOMKYrDseLaaalFirXNtnza36qr3tkc+7asvLI0EVhRlJfYgq5AZo9qmZg3JIhzPKSFTUNQz1kc///+1j1VnAQMV///y7wqlOv/7kgTmDsMqM9MbSS2UXcnKk2XlPoxAz05ssPZRbpnqjYSqyiyLoADAI4wjRXGuJltKWkUrJQuJrjp1M8y+rH1HFvyWXTqsrLyZVoSIsQt9aCqJxTiL6dPD2uQh5iipZ4SEcBQjbK3///2VgODCjOAgYr//////85zD/sbt6r9nnxAAAAIHzICEGjDEDhxYgABiMEhoEj4GbioClrINMQIH9CzUoogPDj5YNgEIHYbxwFHDXI7FCHwFiwxFOF4WEuiE0VfN+RdrYMFkUg6lEh7960kaDY5DLf///0G4CTwATCw2GBGOBaaGf//8wi58i9MkAAABBB5IZCpJrh8yCSgx2FhwVLgq0VAKCklMYYjIgtLIRzEly+2kSAmBvIQZ8NfKBWimCpYYjGJIZJuKnSr5/4XbmHCyMBJlUo4b12RoNjkMt////QbgtPAaYWHiwjHA9CT///+2lQQe97VLJCCWwr4DYj+lJCRCOsmGAXGnGmwElq+CE2UyE+jMk5NXMCSqbcOA0JAFBL44WHFrRjmxhyOjLLOOr////4mQCmCZA+H/+5IE5IzCbjPXGwkUVFsJWrNhgojOBM1EbbzxEbCZaM2nniLTsGf8k9JuaN0jpQeYStq2g2sUj5UkIJbCvgNiP6UkJEI6yYYBcacabASWr4ITZTIT6MyTk1cwJKptw4DQkAUEupwsOLWjHNjDkdGWWo6v////iZAKYJkD4dOwZ/2tSpAEWXhaihK1CqBUmzegENqaioOpGM6maYOcPEgTTl8w7NM3iCBSuktKeA1BYPjIckOiTKGC+QIK2r3egi2dv043G2gdH28Hnba/lPtVrK5v6tQSDDi4iBIqYrIjc2RvyliwcHiMEigDW9cW1vFVpymgIyPfjSTwWC3ohbaYxFh5W4bsBoOwcXT0csuDyyYgeADmO5hjLU7UUecMZ6sZ22v5X2fsrm/2oJDji4iBIFMVjIx8x0Iz6NZZwQq3ZIju/vS19a1T20K2CvQSADBlomFNgfvYMY6NGmhpsNEaBmx+gEYtpQgZBH7oiGJlP1iVjTWRVqRShCW1sbazioYbXQowYADmUqihXREK44yF///9TAQaMSJMLSPKK+BwJ9D7//uSBOEMwsgw1JssK7RXxhqTZYV2jGD5Tmywr1FoHysNhhTjqZr4+/I6dVZG385/fKn8lM7/AAACAMBcg1Lh98uEBsp+TUE59Hpu9aL0mfKLLlXLDuNyLxCIsxu1N49KO/QowYADmUqihXR0LKZC///+cwEGjEiTCjQ8hKHKyU0KneZGZ4VDtzkMlc/Ny5zzV9gYabKZ3DggFWXIMk/UypsGn60e9Qb3FLrPuFjIKTKDNCGGq+9GhEL3HHuc3/97h7eFlEzlax5c2LYOQkXHrzV7XSP+s//9ICEp1UL8gkNqJCTDnTTt4TnOWQNNEGcrw6iSl53ueoCrFthTFQoqaPmO+Sf87JMbr8XVFsCw5hBNLGzGctjm63i4rs5e1q7MzP3sd3G0DdOmJ59c7LmY9b8p6uRD9v+RBgqSpSTXYQEnMoKHCKA40MdV491NB2gDV2NkKPed/6mqAWt34m6UnrFAQSkbHI8r3I7tjOHZ36W9vDEvUOwdYdHpL1F//40cMFVb8pNZSDKoSOxRNS3wOR2bdajvKEYRwuAsEBjSPCpAwv/7kgTpCIMyNNKbTytGYana2mCjucvxFVrsLG35g5yqjYYVu5vxqAVU/ypXN5w7radJUUYqied481UAgJa3/ibpScsUAJKHy52m7bkqaTrPFR2mN6h2ELEI9Jeij3//GjhgqrflVy6QZVDJhl5v4HI8XyvEXsJxHDYLggMbH8vRIrcyQcNFKJRrh61m/RWfmlm3r6HCUgm4P+igwb1ejuAfbMCfQENN5zli7a50hzg29oc+Xgq6FKdL6vf//okiIBqE6XBq0CJJzQyD/KlYr6qI36X/3LCJyQ4EU2rqfrSTtWVef666+M2b9xhNF32D+vdoza+r8ZBEjuwf0ZEZ8v0cSqsia44duE1hxH9cTNTIvzHNbdTniY7Ph6UpS1Lu/6lQ1ayuxTKUcT1YY2zESfX6nA0ogKlZ2K1pIs5RIRLVXtZyZo6hJxjKqIuKmPxKd5nG3YfQb68AwZiSkFA+JtBXCx1d13ObfgHzOGsacbZy+i6HY44tZA6SWhRC56uuo4oZW9UVY4w8RdzTNJk6fravs4CC8kaQLHEHEs/Rf/8+ZkL/+5IE44mC+UdWmwgcdGCo6udh6BqLeQtc55kN2YGj6k2GFPurvv32y5gWWTo2r9ga5ZqtOAQMyObCPKdnGdEwIuXTm1xT8n42GYHCbER4TmcvrdC9Vg8D1kDtS0Yp8737ykiL98xM5CFG9zXEQkFWevOO5/+wcH7KOHMZYu9f+cJFmgZuPC7mKLmUNQIEahoAaCtC/lKT4GwYCMW9NOACrw8CRchahrAXVkSZgcpxdmVtHu3sqWpLOTXw/ixpC5obVVQVnln/6BsFUaNj0tCeK6hdUt1tWoSvf/ChpBzDXZkMLf9JJOYUKhi0UGpkYwGpKoyVFiBEFaGbK2neJluDDswM+C7A6BxlCCR2BCjIjTS5TVDX5nyzWewGTULs9SqZZnI721teY/tr/95HRL+PE/fPI+ns6hmkfWnbK233/wSoMwM0MBr/hsPXLIFnjKhQbsigGeSdtQDAmgzYRFuQK3c4EGERRI0CyXkbjLx0NXd1KCACIA/mp033z5BIoO7KJbVvD0l/6XagP26Hj6aNR241Tv///BEuwIDWFfl+/c6m//uSBOSIgt0+1rsJOf5bRlqnZShcjKTLSm0ktNGLF+mNp4m6cqZYoWgnqviwIgE2ERZkAx2hABIiSJIgO62jMYfHR067pMBg6FIdz3U/s8IJDAR2US2iUkgzoceQHUcSXMLM6lS3///oTZA+sRdmX47MjOMqsFFFK3twq2o0k5Q1n/G4NQEYliEVJVAccqfSjYWt7rX6WapBDTPcZlViEgkEWVyBPixhU3P8xQxBi4C5Qimh65iJjoi2h/3hITt//8WYq+IqDoGanEo6YHsO9HkeJlmxaWqQo7jmPGjn7HH8n3+nPfcC7kAI1rRH0/zEIodKlRXozGOujYcgIc4ZJjSrpaN2W5NcINBx8+UaVY9GghD7J3Hgj6TEp55emQBYLDQQjXD25ZpNk5VPWiLiH/vhkMnH//xZ9n0ZEMIawLCVbHGG/YFYzsEqixiWslNRxBBkw6PTLjb+6mMKKgA0VgL7pCiNuBhSwk4SLPeKaGxZihKTQOrqkGR0Oji+xhaqArpqiGNNfLMCzbxYqmb69Uvr0xEsfuCYeMap1qv//X/3Kv/7kgTkCMKVMlU7KRN0UeZKk2Uldo084VrsIe/Zzx1pjaW9+qICgkKVdOv/+YizLzGqmrOailFkAMCI0/h2VDhMQCoA8YzsF1QuFQhF9k23INLEjCOQolEWNw7F6tdVRi5uo1/O9inNI9JvDi0Ie6+bcv/6/+5VRAUEhSrsn/1B4QjRBJkqwCFQqSorkwAUDwG5IdTQU7hAYgqYHA88RmJgVJ1CEQD5/FNRBwSjEbeVsEQrPnG5W8cfmbdNAUdbrFbc/1pzHdzurFuv1tGsuHNsHbsr+YZC4otJ2RtPv06v/6WNOJA+DIcMn/+jT/QACgZBASqJlZtwkHwxoIAU5BIShWypEUETKAB7UNi0fo0+GyE0v4anb4Mey6bUSy3j84jVzF9Nw9ow5T/hmWiibwDdQkFF7I2n3qpmyuj/+0wsBgUMDT//6aqejp/augA2TsJ+YNZHQyWTQKpBXfrdWz3C7tO/oQMrqMUpNGV35pdrdy01YPf+ZmRSaVXIak3Skii4fTlZ0EXfk7+0iI/eb/5BQLMNCAoz/qxzGBKALqh3USf/+5IE4YyCyEdTm0w7ZlUGSrdlJ26MJMdEbeTrwXmY6Q23lbK8JLc5AAKAoEXgA3S/QhSj2AhEGD42k3VC5oxAmNsNDIYVE8UjyJuIuVduFKUMV+qH2qsBwxt0/qLqeEFRLtGnmpSUQFcCiJGRmf9pER+83/yCgWYaEBRn/E1URRjQQsWICh9c39uawFmsfFFBzLcByyEPJiBF4C0OIlIRQw8uF6ZY/FWMv1FZF2nsMLgSWue/tajrtK5W/XbKnbWYeT9fNrYcc/yCN8pcLA8I2KLZJ3v+mWlf5h4Vg0EQSBbGAQJEofXN/bmsBZrHxRQcy3AcshESYgReAtDiJSEUMPLhemWPxVjL9RWRdp7DC4Elrnv7Wo67SuVv12yp21mHk/Xza2HHP8gjfKXCwPCNiJbJO9/0y0r/MPCsGgiCQLYwDRMAEACCDY8cyTgMsSNgQAwTth1Q2YbsFjc2woHshqqZVqIxNy7gw9RHJ+YjUv+q60k9U32vp0i7OD9EnguRdX8hVBS5Qvzi3V/0q9n/SocNgJiMCwOT///+v1iA2WAj//uQBOmM0ssx1JssK3RbxjpTaeVsjATHQi3lS9F+mOhFvKl6amewI0ki+BwNMDl35QQyhfQMK4+m8UBrXX6VqWiKZJIttb6xGZ+QxsTOIKSmO+tN9fqfoicJYV5uk0SIQljaiqDFyhdM4t1f9KvZ/qimh8PgHxBAsB9IACQBRa6cmQ6oqtJ1tw3V6wsGPAaEApKMcAAiVc2m4nsUY2Fji5tXBwTtjhBkjKDH35O1mNcypzZIiopk+DIROUL3o/X7x11Op+swISw6IwgA8YT///0CooMHNQESiG1LAEFrps7M00RoJF4zBqkFYRnGhGSmDm0nGAgiEX+jo+D+xAT6lU7Ll9XBNHa4VDDAZjoxjfasL5Dn5Lpx7iIQQcDlYkhROUG15A/X7yGqUf6nGAujgxEEHgPign///1niilGkUZdiTLTrqF6QZCg9BUqN13WrEAqhE2AzRVlAyHBrG4LqSmgipWbtSQ3MfHMnIEiN3bNGc4jNLv7lR/E7iOpEdsLFYqIopsRoyBkb7FqzQ8cfq9be821/6zqYX8sEECQDsAni//uSBO2M4s0yUhtPO2RbRjohbep6TATHSG087ZGUGOiNt6nqMZssAEACiHodOZHmRRuLjX2NflkyUrNqAxh/BRYFYuFEbDVizdR/jwNqwmi7aHuYMiH/G/DwYJFtaVihsm1CENnhDqmMUf/t7zv/qqiHYSAvCAFRYl///8uJDBEaA2njzlpctFA5uOWsQNoeOIemTZjUR0NIjFqhy7VioULMypnM3ENdhl6K9nUN1H0TggBrTnXZZfZ5+r/1NLTTijq37BVBSxg4EjQuMDSL2TztNveS0f/UeFQQRXA0EseBCNYvAAAAOH/U0NxZmnEiaLZiqJi0s9pCCkwy+BipFWKgANAtM5MNsx2FIXi9jgJweC4OZLSvJzs+J/AwT8hzWY8wg41WgBec3hJK0/9vyWj/6kihekYTgoyQNSUl////20rVAAALga21s3hzjIIRw0QYaYzf2iUDYNowYZ8qAAHhhVTK+1tFPnFxwqdLgmrAu4HYHjPN5/DyuRrUOFjPUJxnAPUEzkNkH/1/8y/+tZgVEA+DbJISooGF/9IpNlP6Ev/7kgTvj8MeMs+DeGtgX4Y6Q2nqbIvAx0AN5UvJfBjojbep6IqJiJSHJWACA5RDcNm8c0oiPjYh/TB58lH360DAfsYYpVtMy/LScz2cOAyoXrqQbz77s6LZh4mY+igbgppCNyGyD///pf/WtRw84wZkPIZB40c6hyRm3cxSSn67o9J1Th4DcgiQEIA7C7SGu9RKKk6lMZGUqglGGsm92OPbqqxLbid7uWooOUPqVGu/lIqHxODkmKgnIBtkdP////zmlnFA3NO/4TJjKUhJ1Knh+lOTJ2kHLFQAABIJLDBx6j2Cg0juOUar45TGSZbEhUhfZgq2pMrLfron8acQsIAYVgP5ZQy7v4zONQ4xnCcjzE2CzJQtZT/9/9f/5OPR0NxJBhz545/1p0lnPYmroycgAAAX4cJJc0aXTHABkAzFEQ3UfEmAQAg8UtgWFTMXcTA9ubRtpJH6dfQKzr5gCLKmEjt2bHP/fzhXq1Evy7ORcBzE9L8OAbHrRtvf///QVRuIwqIBgDQ+NJHd/oFKceXRtXj/f0gAAH8OEkuaNLpjgAz/+5IE7gjDIzHQm29rZF3GelNpLXiKFMNQ7KTukWCY6M2mNdqAZiiIbqPiTAIAQeKWwLCpmLuJge3No20kB+nX0Cs6+YAiyphI7dmxz/384V6tRL8uzkXAcxPS/DgGx67be///+gxG4jCogGAND5SRH/UQOttSLe3+M5sAIgyC6n4QREJMBIjGzUueYBTl740YOAQYbwoDWJmn06X/4dS7cs+jpLKI64W82mt//8odCYSXRhlPWJNQMjA8N2rsVJ17af/zAKIEMBRJx3/hoyKBmFCbjnEKzJFSXm2O2JADRdo22AkiToXEGDJsDC1dldMilQRJzJFAdPSj3wtO6eli42He6+Kd/+bORHVYuakZMkYHhu1aMVLr20//mAUQIYCiQuGX4/5gNMwa2HMVFU3se+M2EQAiBIGdtoau4jRSsk50cLPmYMIdJpSgwjQlp7IPw4hRDzLZIuPrEKN29mSxZK1Rz5zJrX/+qqpSi6oicy4CvMurhNSJE1qtrfN1Pbc2t//KuPjWKw2TF//uUUExgDuLvYobJb3jFXB95EANAWh///uSBPeMwyUy0JtvU9BjZkoTbep6DFDLRG28q9FaGWmNpZXq4oaduRAXhOiEWHMgtGn7RREJZ26TE5YThmTzjY+HXpni4QjU3v4r//PIiiUWT4Nx10WLMOGlNa66m6nbctb/+rnLBi3F///5PbUjg1BNlQTrG73C29BGIqnNhpHoZIOAeyEmPQjpCAB4YJR35UomJtlU60zs5JQb+EgOh1son9u//70zYMBFYUNgqEhlU9s2n5PbvLGjzjm1tf/opcTCQeChAeF7O/1DM/MrcmBcxQOWggukWSSq1szD0iNkgAJGTpgRpCAmE4Bo/FWkVFwsy0h1vag3+wMi71L+3f//1niQzSBshNKqntmibye3eWHHmM1l7//HjBRQoiD3f8OCJo1yFApew7uJTLbhj331ABATgchNowqoDFoxRCNpEDAANJ024DXWxcHXa2iqDPC8CO7/N1s4wvRGneXzwQExmBm+fv//w36oP4JWwMojDIXkk6oH7FRhsbNsjZNp/v5hgskgnGQvCQJoVjwReY91evYK0cAUBsadQAZPSACAnP/7kgT2D7NKMdCbbzt0W4g6U2libovUyUQNJO+RYBjpVaSVug5CbRhVQGLRiiEbSIGAAaTptwGuti4Ou1tFgGeF4Ed3+brZxheiNO8vnggJ5MDN8/f//lfqg/glbAyiMKgvJJ1QP2KjDY2bo2Taf7+YYLJIJxkLwkCaFY8A0e6vW5yf1UE1tNGAiA0UhwAGABhWphTYvwIQAJ2murYRJxprIoY1mCnHXMScoZMfk9qn0kVDO2///7d5JGwtKjE+iZEQ9JOT/655j///OBgwVBRcwsQd///////yVV5SHslkoOf4LwgOAAgAKJqNCCcGEAqKMJVDgaPA8nf5hoiMEFM4hbtfVUjREv1XWTfmhLor9vcf/zTx8IQBBaEQhF4ihwmOkh//zj2//54+DoOIi0mSFw4MEP//+pbRq6cB2PQqAABogMADCfkBhsC7iexUaFx31BhK3WGGFis6Nr4MHoG9zCqLUNMlF5IVCTK4/q4pG5wXOGwaIITNjUo6PStnHEVGr/8fGwPBeGhWeTFxgsf///4ggqCQvWFjokKg0RAOAGD/+5IE9wiDZTJQm29T1GuGShNt6nqLHUdRTKyt2WiY6amnnWoBRPvIYtQHiBGAJFQdraSACqUKmBjJKTT/qKAViAGu2c2TPZJMgoNM/7K4VG48B5wjAWEgoLzVEUTOjqjqhh4iFD0av/xuJQDAnAsFyZMNEAw////xmGaGvuYIlhQAIYbf9JET8ZUFbAfXATsjxrPghMOvkcDMduUIgJIN3HXKVYcBIVhCZ//xJTVkKByHRsNovuVv8VaVUSLNJZ7z1///i+KIQmKg2ZMKXE1X///SDYYVBpBhI1wogGABhDb/pIifjKgrYD64CdkeNZ8EJh18jgZjtyhKHT3HXKVYcBIVhCZ/+aJIuURRoWGrj4vOu31qYg8cn///4viiEJioNmTClxNM///54H2kCb5kcJRQgxsAAGEgwAMKCaC5opArwhkbXNu2Jbb0oITBBJdDD/1dU3f5TzF/X/q42FX0UfV+OZsXb0UaGD2GiJw6nKivr86zzdX7ij2///qOIhBdcMtHkyR+sLp///y6BpEFmgM4HhQVBhzwAAAFC2aQhxrI//uSBPAJovQx09MrO0RfZjpXaMdmi5TLT0ydLxFhmSnpk6X6ZAV0iFUj9PtyTbS6iEWeP84fb+n6kcsf+QzR1MidVGrsX+ijRzsNETgCnKRTVG2mHi6LUou6/niITUVMOUUdwQTN///62rk1th4sLmiRsQAYAFEBU4EwOpNJYq6jtjksCQDQCss26BYF2mkTE3Xz5h2gyzw78w+q9Jnsh3gn//ypASFwpjU/VXEX3P/zDNvG1jOCPnsl//jKsncRgO1yYZqOR6IPCIKef///5cQxGqNJsCo0gSIgBgAQPtTmAeG0MofFWCMtmcq3FtnRUXNGyEhLWmQTE3N272H3Mu4d3MOCrBM35DvAz/KmigKD6ERYdPduiKcv/zDLXG0pnArZ7Jf71bKsnWRHGlckzKxaOwx8HYM+c////i172FNiI1UAAbgzUSOKCqSkFl07qkXYYVyNq3jBtpROrhwyRRSTzZAqENNXkr//86D2o3EVnWrsyK7/hQ+yCcHxCpo/5ZXAdNiCbjDrs7///5hjUSeeYWFUgDCDAAoaM7Iq2NqDf//7kgT1iIMcMtPTRU2kWAZazWHlPoz8z0bsofaRn5mojaO+0sSAi6FExdSXMZZ+CCcasGUsv2C2MahUznNmxhHTM+cN4KW87f+dBc2Ok2TJz6pz2RV/8NNtBuPZVUR/cmx0+BZVwfS3KCc5xDf//9asN3jzJZD5MCAxAAKHSX4OpgmWGOQUDcEBaGH5ExEGifN/BQ8mthe2cND0n1xgfyaCKQLKum386nQVWPOLTmR0mu//9CSmAsKkMbH8qiABTGOmhh0lWd///1sYMFDUOuFw0bAigIDEDpL8HUwPlhjkFA3BAWhh+RMRBonzfwUPRWwvbOGrR/XGB/JoIpB0rad/zqdBVY84uonERR13//QkpgLCpDGx/KogAVGOmhh0lAX///kjI6bAQie4kh4RegAwBsH5UBMThc6+FBMxn8bMgjUOE33rWOljVQ0TYlLKJ2Y8Sus03ueysHYCXhEAUtu+m///+M7f66vebgX+t4pS2qf/+utwozEds0KBTf/+LmmUzannGFJLrcab/ngy8tHzIEem5jd2toAgBABuH5WFMPj/+5IE7oiSkTNWUw9BtGBmWldp62iLmMtJTWEDUWmZKSmsIGpE5iDhbRhwZogykOE04LYnCntf1dzWZao+89jFloJO6Q9A6AV0g6EilNm/qXL7SXNHKk96CDsg3szmRsXhinjIwQf6Yl4cRNHcUTJR1nNm/5AIngWj6XhXc4+WnEHjC2KLoAMAG4mEjAtZnxjxKAmIEQQ9r1CwE8zOFJhTtK4DBrkdyCe58PXs1WnAZivQ9CkJjxK//+IlVr3tuncais2zf9aHVnHKjspzf9NeSSAoQZ0mHwWD3/pFhkdcQSdEbGTrTJO52WjgEwFaLjSBi+fsMxsACBZnAqqk9DjztVv1Gu8+Ap7Hf3edy1/V5Qunl07nyv/9YhRqlut7cZQ2bMp/+PuuRTj/5uSQcLox1ZxlT/9/0OQiSGR2QyLs6KCOcfAFUz2cyFIdyJoAALBHCE4JiHiRotalWAZttuE9cnpV3Kk70WHdStjP1QWDxpMyThQjLWr3kbbih0zT3JIhmsrDv7iq7YMiK3/wQpjjoyC21djGbWhOuuhDvxT06H5I//uSBPiMw0kyzxuYeLBrpln3cw0cDJi/Pm29a0GJI+jNpArjdzPIVzpY5XFuhEXA4jqE/CZAQCC38+YxcoAXBDqIgwoq5jCvkep+NMMtT8uvfz8e6y12Kuw7TBJdVhzZqUR7zs1uXtkEnf3rmi1VlK+Lc1rbcLya5v/88FB5Y3XinH24CDjmiDqEw0/fKUQikUS4LUJcbXGk4C43dhHrD5E17AgBqJj0iYQefSG4er/nTf7UWF7JNTnf+lmdwyXnKQGkzWu/uIQOKev5DDCwQzlPEEqxmMERQgMhtvlhXQz+qSyd1PpXMXdTXP/f+uTALjd2EesPkTXsCAGomPSJhB59Ibh6v/X39qLC/JNTnf+mzuGS85SAyXa132uIQOKf/I4wsEM5TxBHZjDCgiKEBpfVzOgILhNpMidLHEDyCY0lMfpqAAIv4miAEMMnB6WTrBwchCBBQMEWiqHxRdzc1go1UjNDlzX73MWdS2AVK3Ua5C12JXsPcV5pS7+WHP/kkKLcIIJ//lkHZ/1PMCcaug0NG4vDhu/LBqJ3B8c0XScB8//7kgTpiIMLUFVTCBP+aAZKA2jLtArA4VNMGEfZU56qaYMI+oILfYPpelF4ABF3EeHAgxC2HrJDcFDQsCgAcCB1jKM78KmZGntDVJGY7lzXN3Y5ZuRl9Uq3EZY96pEN1h2mttEXHtYc/3ySFD7iAIT//KpB7P///c4A2S6kQKkY+LE7e+3CvD2R7RbFWwfyzyf/7zq75fbXtOd1sAQtAUwp+M3ArKqRKrZGHN4o0Va6+JED3T+xXl+0jJoFBCSD4MoDCLzv/pQ4SSRgWBAd////oyjYHhQcFZcQk1Eo9/+zeuP5AofBD5IKQg7xsXyLyGyBaH/iykATBe4xpxCiBrj2wOrI9AgRFWtXxRR+T+xXlXaIxUoIAFEgTE0eDQfT+/pQ4Qo2MCcGgxt///9GUmHiEBG4vLyYP/14MhtqxxlLmJyZQsIGAQRpHKArytUAAAXCpDBfWD4lxTcW09j1JRrO59Lvyaj3hxlIfTuQ/lwiYh+Hu2i6KNsRKlhT3gU/9DkIlpxj///b/yo2G54gYUi6Gv+WLABWwgLyaXGSofAKhKH/+5IE7gCDLTTQG2I+IG/mmfNsqcRLOPFbR6Tp2V+Y6umEqPrGC6QwCJkAEAXCpDBfWD4lxTcW09j1JRrO6NLvyaj3hxlIfTuQ/lwiYh+Hu2i6KNgRKlhT3gU/9DkEkOPGxAv//9v/KjYbniBhSLirv+XDjGTRYa0VKUDxpI6sBKKiR4AWaBABAMobozs0ynOAqaxSqSNkvWmS4rSS6pl8n30WK3n+jxPwJaHuZUEuXjfyJOrR3h1HAgV5DFMwbZNKn2GopGoZUw45////88Jh4UC8eNNEk4wim/9anml7fl2LAMy0oQCimIBBlDdGdmmU5wFTWKVSRsl60yXFaSXVIvk++ixW8/0eJ+BLQ9zKgly8b+RJ1aO8Oo4ECvIYpmDbJpU+w1FI1DKmHHP////nhMPCgXjxpoknGEU3/lVB40Kpt+sdG0g5YKWB5QCgDcICdkm7EgjDGQrcSivNtmMrmRg7jsv+yRlcH+SY/z7Z1O3IYYyfUU6IPgsCMTiwyMbGl0WzAd/mElUVK3////yCIeFBIOGUec7f//1o7PZk2fb///uSBOkEYtAxU5sPOnRbxhpzYedOjHjJSuy865GIGSldl51yvRKNjr0UMmKycAKAOwgJ2SbsSCMMZCtxKK822YyuZGDuOy/7JGVwf5Jj/PtnU7chhjJ9RTog+CwIxOLDIxsaXRbMB3+YSqKlb////5BEPCgkHDKPOdv//6yKOVZBvZpCziaHg3/kmKHdEZyYB3CbScStZ0ZxzRFB5k5akUtyuQVJ+6+UTfSODQRYUfMFRIIwLiVAJNx+RGZ6QCAWS0CY+yBiV9cv////8YiFDgSnHgEK3Pv+bbHIopCvBUc17xehlzwEwZuIZGCWocBDhBOLRKaOF37qkfLQ3sDYL0cF9QPIDQLkVEMzTkaEYKGTQJkvB0ZX1zX/////KIho0FSnl2HQqky9Rr5s6Teiik8ouWWk7eA5RkwhAcAewjqHRa65xPSxoZTEbaceL5mzR2qstg1oDDlsEaxouSk5OPg9OnpkMhBXmLj1sllagDRaSVAUMjPVWb///oVRII3kpp/9u6MUTVXVDEv6EdaVBL5TDLf/G9tQwHAHsI6h0Wuucf/7kgTqDsL+TdQbDyp2Ymfag2HlTsq0w1JsMLDRVBjrDPSeGj0saGUxG2nHi+Zs0dqrLYNaAw5bBGsaLkpOTj4PTp6ZDIQV5i49bJZWwHWklQFDIz1Vv///QqiQRvSmn/255ChVDS9vsrYjySoflNJb/KfTvagZYr+NqHJNInk6n+azIQthZ6nOm6PqXQdJxHU8uk8GGle1YUhZA8dkER3UiK0cLFxzrbul/nJ/Uxzo52UwDNogu2yCzGO/lYriimjYeONBxHT+h+pnofvXNdpeCpINsd/Hr6W8pmTSjTszsq5NXymnW6fD0LicB4NwuB3CjUfK0vH41PKIT1fzYq2jOXZ1t33+qf1MBh0FHIoYMR5DdqXh//pUDlHdPd7+3kT8m7i4kbN+3kUBQi7hbA8HA9HMZkTcjgCigdljIKEFkDKosPa5gIAaAH3U8vJolePoLSZ/4zTKrthRXQNAgCUAYXEKShyljqPrQw5Hrtgw7kHMk7u6VaIM1OpEtT18RCAGeDGKilIUmYk6IN0kzbQj8oUSafz03tyK7SNzbPciwtb/+5IE8oiC6knUmwwUNlrn2pNhgobLkQNW7CSn2WCY62mEiXsCiN/DGCIGCRcxN9NgLgaHCVWMgIYQQMo61t3MBEBQBAbVmZNkrzaYVFG4zWXm2FMdLkuCjGIQqkocpY/EA3Ycj12wYbkI2d1d6tCdOpERqWRzfHQoDp4uONRW2RMi6kHPIXv0PXRkQeSncmsy6zOFB1pda2TEDbHdht0RZOUj6j2q3Q8NpSihp8E8adhVWZpOvbkILMBWLOSy+F06ltvfGufWq30+rUtj2vq/mW1fs6+fih/AlPWfpM///Bu5lumJ+f+jWXawZYTgFjgClmsBhF20kbEawJEpaM14hzN9Rswx1AU7z1zVNVPcVLE7i6KIqs3e2mwoloLLAlWqe2nznt9V2bt58ccWey3R0Pb9V9vXRheASScnHz+2g3dHd/X0Iq5hZXSvwbMofsr+szHTwb35KgZGrcMx1YsYYQVGw8oC5IGRRK3MRF44fDYan9MITFB7Bpnkmy1eqPWpqeWdfb0jnjh+4uLhef/+LJHtSrIytWmswSwpgnvevem2//uSBPsIw79STxtlHjJ1aDnzbEfETCD7UuylZ9F1n2mNph1782l2N7/iOFkk0Gf9NQmo2mABkYCus/45Ay9EAGjGQ17LFeyb1rWdY5BRAZr/DQ8aHQvVct1LK9Z74aQoSoC4bHDhxUut/8WKidkVZGVzd1mBlJQy4nfd5v2gfcLDE1bTOv/J8k1hnfeqiz20FY1LRTiiEzwxtTuilAyzXGAaoayFVCPy+A/B0qSzaFXp+2ifqXi0YnStWWn+iRMXlZalWLVRV1HHdz1QPvNI1lMZ3bRu8DMNDti3forw6ZA+MqdnFq6LNk4Bk5IhFSM6l/WX67S4myFY3LhbHCZkAUCnBAMPQe9wNXayWJE5cC6DESM0m3ZOuenhYmISxts033OUn2JOTOb2L/r/f3D3+lfLNd/wl+zAuHCGHtKvd/xvq0PHPU81E/NdVCT1aylTHFDnirinRlV32lKbTNcjQCQAKaS/imsJMWAdB7HVzmeHS7W9rjEEZIQQZXipJecFc++J0uIhxQgYmzHqgpaQOFGrExc6nLXROxFzp6mHAKLIKf/7kgTkDALWNtObBkHmWykK2jxofszw30htMKvZnippjaShe0OUGF06VfI39TiZzgALg6yA2iys7EcDt3FYIBSyb8U1hJiwDoPY6uZXLtre1xiCMkIENLUhi3Qmu9RKIYPBQgODHqniYM69Jn3ue55+I+kSfmO/mBwcnIZw6hYP05kQKl9jDEmoiOXdyMCIfX1rrMmDxX7uFvfbVcwAcAAOxzcWHwdV3z6nbWsN2ZBZjMiy5rvc5bTT8O0F6G5+Js3tGW2/fjXzvjT30sGOXq4CoIt+/KE6nW1113qjfoLihGZBEwGQ44YIlb8v8QEKTI/ps3audSFlK+4+L/xauoQB8ABlsv45Bj8w+Ax+/3Ktmcsk0ZW7P+7tnz2Djt87F9nb/OyznRSjdpHHiYKJT01wWCp/8I8fGlXNVNd0sV3P1B5hFVAglB8MFzBISanP+v/+KEYdigEQRNAiLLa5CBlc0tlyBgCVHNx2oTLGUijojDM611j9q02z5qwlooLTQ8vgVXlYkn7lGrjJxys3iYmQXOSNb//bQr6Jbo5YiBRIWVD/+5IE4IAC5D3WUwkqdF9nysdhKDjMXTtZTCS02Y+eq6mGIXpmdA6ybG3/x/xcmqP51ltgbfUmxUtjX3LPRFM0AeACSjv47KEo0OYwSZIWUskjESLrLN+NKwYNLCGYCrkQCh8hDQqcMGs3QTEyA4uKRrf/7aM/S3uWIiow1EqLjABZzHKnexH5RVx5TT/eC1xrG3x45/m1fV135gDIgCJbvxce61lygKs4JjyG3J29ZmkJFSfFnRA6E4ss2jfrX9yIPF/Qj1H8fxzf/xykcTYyv5zLYqAswrKM6lnczEpfHzDjKNE8V1FTMPA9dFbuuNXu2X+zLyKLb3WAMqAImu/GbVWDMpBTZyithJuF2PAbSQKAhfFOkAhwJaZDcv1EaPsDlT/ld1dWEImICVFdC9Yo6GQGcOyGOom6VqQ+uijCFH2YqSqioPJ48wz0vy+teI+flQA25aIJJWg6IEsTHfBTNTLnwWb6x70iRZlQ2MkelBXSHHYJQ0DIcAmHwi3Lz/9SUx1+1qntfUWiIiXENpL3TOLtokQR+0z8g3F8w+2Yd/3f//uSBN4AAswz1bsMKfZbZ7raYSUuy8lFWUfhAFlhIitpgZT724+6UCttksIaONuvZVrzvR2/oAbctEEkrQdECWJjvhEqBlz4LN9Y94kSLMqGxkj0oK6Q47GtoTbAl2YFdTr9lEjCz5pSXM9kchCEI6IaRRd2Q4fNIQiE6KucAxeKC7mFit977o9WdUfSKDZhDRUnfcF2Qs7bNHMQBeQCdk2wskoXFSeKxy+5Vp/3KRHljZewpo8kkeeYItXk2UuPUPXf/MwiGUHnegg6UalU/5X1u+41azeEivnpdWGy/n+NMkT7aHw+2k/UiQdR0Vgzwa17jkZdniP/Mt+N1wAUoiAE425BoaahI0K2PJBZ/lSi+WNl9I0edDnqNbeTkLjzA9d/8zBSAs4868jKZro2yO6OU5UDzokgsM0MPMcMY6N6sVfqeU5/UhBiuUlmVyGcxmWhrHE1Zccf+HNWJgK/EDekcgXBKNx9OrNwKuSn4/KPykdnlY0cSxs46KX/aP/T2pavzcSOOBiWOTWCY/7PM97v1MzJ9e5HJfy8Y1xtUdjoRv/7kgTnAAMTN9KbD0F2ZSiaU2HlPsww51NMLMvZf6YrNPWVfz+GVn/x8x9ffnR7FVizdHrsO7cXAWjnJqwFuACVbTYfCuleWE/rHsOw0mH3fK/rIkdnlYUQ4SwSqKa38V+0vQ8d2SonKEIWKSnHiicwkqvLzspK5bcmiNfr0kT7MMqIZcg75jehL/I6JS1em9q/f+fDx9gqsAmQCvqyCjlMf5U4bnJ5D0+l1v5tyjik1LeLWEqFmXrL3tzGlaCiOhEkEMjci7kEnigvYpSTCh88tqddpZ3ikEXoyhikOFdnRlQ0/Kn+e3Mptbv6MgIgZ7WndnAO4QJx1NjorjTjTx43y5L39rXeLIF47fpaJi43z7/b9miWghFwQkwyiB3cHsw1NBPmYzxgVbl7uHtV1N3IVEz51rHCsj/7ZoeTGxGeZvkh7XRL5l/CgYzsdXUAACbikEY3RVnAqm8Q4kiLfXkY809YOaQjjLnRCE9lTkwMdCRFNBw0ej6f/y8XAf4oeL/OVfjIQgODH//LflBRK70kGg4XPv4MOGnn2pj8l/6y/yf/+5IE4YAS7kJWUYIyrlvGqpo8yC/LYTtRJ6BP+WenKqjDDf7XxVhTVbNp2fsHXpEeSH+UBJAm4pEEb6qPg0jyE4DBDXbAv2k1Z2jQ/iPQmFbTsHBeWRFNBw20L0//l4OQG+KHi/C4hX4yDBgoY//736Ild9SHA4XPv4MYaeeQQEyzuCyCrU8yKqa158yKroQVGvHKgCAAJA6Si7kqrC6aVWH5tB0j+seIXMaaCwuQBcdXTSOEKgykdOA2QWtCcP+uiUQSpG2jNoMUVkwkiDBG0haAyb8XqejqOCr1gwZXlARgbmCJmshbTDEkCZ8P/sAEWQoYketTFlDA8aJAkg3FiiwAYACAJNsYjvVpvAcazs8enkd7ifk1askHMJqt2KNg1iyh4I4qLj/vTR2vbTjIdoaiB+oWgMm/HVPR1HBV6wYMrygRgbmCJmshbTDEkC58P/41qLjMVKEUiQaBWCQzB61Vu+ujd69C1QBsABAAI05sWl5V8LKisES1qs+aKrIGlOIKIbb1ucEuj3yBTaxv+tVwqUuhDCJOP87FGnk4YJKh//uSBOkIAyA0TxsPQXJgBnnzYYguDbjPQuwxJBGeGelo8yW6mFjUCfiiTsDAO8uRuocZCIMhvT8Yd5lJ0uZ6qJfN9bul9P1SZZyMJppBQncIgjUeSt2F39H/+to0lM2C42TmP//1gDYACAARpzYtLyr4WVFYInWunzRVZA0pxBRDbetzgl0e+QKXWN/1quFSl0IYRJx/nYo08nDBJUMwsagT8USdgYB3lyN1DjIRBkN6fjDvMpOlzPVRL5vrd0vp+qTLORhNNIKE7hEEajyVuwu/o//0PAIBUZspemj//9YAIxKImFFwiQlKst7+SyOs8yYREQPDLsCO+hPuoKe00tsJUL4oGkrEUEh6FQxWVMM4SMvgVIfxsDxNwNhCCUKEWcel+pj1NxHINGqtctyoL0wo1KJY/4ahMYYBRktHQVxlHUTWVN37KgEfU0BrZ4fISlWW53clkdZ5kwiIhiaAvcYeaGxDbRpywN6IXLEsnMpl0WF61Pxcz8G6TJMGKfhAFAcjKTMuMduTKkVSXWWdraUDsyamiCV2z0fgKDWFIMBkTv/7kgTbACPRM9TRg3ukecZ6mjBvdIz4z1SEMfSJgZnrEIezEScLYUG/ZSCU4CQAAAR9ZPOhMwJzzUagu3SRXI+oKJRc8RLfezn//6qFCJUk40sTE0iuESJUzfRFltRRu/Lc1amq7K1Zc6pNCMH4dQAtLP///DMdIly7SAcT+wFvFAAACOLbnQaYE55qNztRJFcj1QUMoueCy33Wc///VIUIZSTjSxMTSKuIiIqZn0Q0tqJm78tdqKTVWytWJzhR0lGDUOpkb/Wf//+cqg2sufIHidoAMAADCEixFECQMsZ7nHqBOWTu01mIKGeh7l5jUd+k6MitTZ/9FFDEGUODw8oF55RRph6o09TF3UE+zrIx1dNRgrN0yB0rFHHrP//0COBgg4ClEByAiJsAGAABhCRYiiBIGWM9zj1AnLJ3aazEFDPQ9y8xqO/SdGRWps/+iihiDKHB4eUC88oo0w9Uaepi7qCfZ5kY6umowVm6ZA6Vijj1n//6B5Q2KTVaESJtAD0AIFJ/jGWFFX0TMh5mj3p0RJA/tFjeX9tVYqNHD2ntK7z/+5AEvAIiojJX6SNJdFSmquokaS6K5MtTR6UH0VMZamj0oPqEvCI/3u9d3UFDEAQodY6c0Q8KZufVUnOQj+QS8SMYkBn9n////kekLhxNs+eQPHDpu21bfvhW30APAAgEnuLMOE7XAGgP0sxqcTkzsrruBIvU2y4mkChYUIpAMTbQ2TysQ3H9u6bk07kptw6tlrj/wQ2jV7e48zl6VWb/DIntDVo0AtGs/MBVTvKKWm6BA0AntLhaeHgqDBQtvQtGVFon4IXPwyuu/pMRNrhMqYNALUkxUy9ama2tya/ZSDlcUza7mT8QUvKpXuSUpWQjOkIUzDHO6uS///cpmvmBJM8/IyiGb+v70v0x2gQEgJLcZtgTRiQGM4g7R+Frbb05SqtsZpTwtBzZ5kydVNY7n4901+ykHEuKZtaOZPxBS8qhXuSUpWQIM6QhQzDHD//mmWilKWnnJQSeAAq0XaLRJSoCCIKT3Ha5EanECXdTuA23Juc59We+LOmWRzCyY1LBZjjtjNpLEQXMMpO9JJdSbZ7WukHMMV0/39+4u7l4mVT/+5IE0AEC2kdW0wwZ5l6mWrphJlaKRQVazCRH2VGYax2GCPq7jNd0JOHjz8IxBFhccb/+krpeZ5JgqsshhoN5ACCIKT3Ha5EanECXdTuA23Juc59We+LOmWRzCyY1LBZjjtjNpLEQXMMpO9JJdSbZ7WukHMMV0/39+4u7l4mVS7jNd0JOHjz8IxBFhccb/0vpRF2gXFUC7EwNc8mSS0vxXoR4EcoHeWDO4H6di6r58L2GNAnQFzBZpE+AttFs1NzI+NnaCCVBjoXD/5Gvbpbf72iuKflvjRW67Oyp5GEAM2AgETf/QFWqlm1TV7WE+5T71ggAkCQZ0KTExch5eYHE/YXGkHuWF7D9AqAWk0kUAwrbRbNTczPjZ2hBKg1Rsf9Rickzwqr/e0VxT8t8bK3v3ZU82FFzGNEpb/0LVlmklIe22T0uVx8Ekk7Dayk/ZlANTjscWcARc6N3Eu3HhDHiixCgahgK8yAyW8Qia8UayP3SW1SHwenjVCJY0mjRtysETHVrW3f8T/p1Z9CADcqaEBRtkN/6CQiBVQjRKMsuclQE//uSBN6IQtwz1rsPQeRbpnrXYeg8iri/Wmw8x1FUGWudh5jm5KuKgAAC0bGQhIJgEw4iNiOe60gH3z69cocp4CWZExorMwKIbAmQ76dAlmCwICirxUSixAuh9JbVIfB6eNUIljSaNG3KwRMdWtbd/xPxp1Z9CQG5U0ICjbIb/0CwiBUcI0JHLQe84+9KKwYAAkCQUnFFQOnIrplrxXN6357Q+*********************************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\" type=\"audio/mpeg\" />\n", "                    Your browser does not support the audio element.\n", "                </audio>\n", "              "], "text/plain": ["<IPython.lib.display.Audio object>"]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}], "source": ["from IPython.display import Audio, display\n", "Audio(\"https://www.nasa.gov/wp-content/uploads/2015/01/591240main_JFKmoonspeech.mp3\")"]}, {"cell_type": "markdown", "id": "a5c299fa", "metadata": {"papermill": {"duration": 0.041702, "end_time": "2025-07-31T07:45:25.270013", "exception": false, "start_time": "2025-07-31T07:45:25.228311", "status": "completed"}, "tags": []}, "source": ["Let's download the sound."]}, {"cell_type": "code", "execution_count": 16, "id": "9be74a2a", "metadata": {"execution": {"iopub.execute_input": "2025-07-31T07:45:25.358947Z", "iopub.status.busy": "2025-07-31T07:45:25.358558Z", "iopub.status.idle": "2025-07-31T07:45:25.868999Z", "shell.execute_reply": "2025-07-31T07:45:25.867890Z"}, "id": "k3vrdoa0Z01X", "papermill": {"duration": 0.556344, "end_time": "2025-07-31T07:45:25.870578", "exception": false, "start_time": "2025-07-31T07:45:25.314234", "status": "completed"}, "tags": []}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["huggingface/tokenizers: The current process just got forked, after parallelism has already been used. Disabling parallelism to avoid deadlocks...\n", "To disable this warning, you can either:\n", "\t- Avoid using `tokenizers` before the fork if possible\n", "\t- Explicitly set the environment variable TOKENIZERS_PARALLELISM=(true | false)\n"]}], "source": ["!wget -qqq https://www.nasa.gov/wp-content/uploads/2015/01/591240main_JFKmoonspeech.mp3 -O audio.mp3"]}, {"cell_type": "code", "execution_count": 17, "id": "114b244e", "metadata": {"execution": {"iopub.execute_input": "2025-07-31T07:45:25.958307Z", "iopub.status.busy": "2025-07-31T07:45:25.957982Z", "iopub.status.idle": "2025-07-31T07:46:12.036915Z", "shell.execute_reply": "2025-07-31T07:46:12.036036Z"}, "id": "BJr_D4O9Z2Zh", "outputId": "b08643d9-6c1b-4dda-af99-8589940e7dcf", "papermill": {"duration": 46.166112, "end_time": "2025-07-31T07:46:12.081398", "exception": false, "start_time": "2025-07-31T07:45:25.915286", "status": "completed"}, "tags": []}, "outputs": [{"data": {"text/markdown": ["\n", "\n", "**<font color='blue'>Question:</font>** What is this audio about?\n", "\n", "\n", "**<font color='red'>Answer:</font>** The audio is a quote from President <PERSON>'s inaugural address in 1961. In this famous speech, he calls upon the nation\n", "\n", "\n", "**<font color='green'>Execution time:</font>** 46.07 sec."], "text/plain": ["<IPython.core.display.Markdown object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["audio_file = \"audio.mp3\"\n", "user_input = \"What is this audio about?\"\n", "_start = time()\n", "messages = [{\n", "    \"role\" : \"user\",\n", "    \"content\": [\n", "        { \"type\": \"audio\", \"audio\" : audio_file },\n", "        { \"type\": \"text\",  \"text\" :  user_input}\n", "    ]\n", "}]\n", "output = do_gemma_3n_inference(model, tokenizer, messages, max_new_tokens = 32)\n", "_end = time()\n", "formated_output = f\"Question: {user_input}\\nAnswer: {output}\\nExecution time: {np.round(_end-_start, 2)} sec.\"\n", "display(Markdown(colorize_text(formated_output)))"]}, {"cell_type": "markdown", "id": "968227cb", "metadata": {"id": "L15JuAmmaOkB", "papermill": {"duration": 0.048237, "end_time": "2025-07-31T07:46:12.176130", "exception": false, "start_time": "2025-07-31T07:46:12.127893", "status": "completed"}, "tags": []}, "source": ["# Text-image-sound multimodal input\n", "\n", "Let's combine now text, image, sound in a multimodal input.\n", "\n", "We are also adding a new image."]}, {"cell_type": "code", "execution_count": 18, "id": "47aced32", "metadata": {"execution": {"iopub.execute_input": "2025-07-31T07:46:12.273592Z", "iopub.status.busy": "2025-07-31T07:46:12.272617Z", "iopub.status.idle": "2025-07-31T07:46:12.277521Z", "shell.execute_reply": "2025-07-31T07:46:12.276566Z"}, "papermill": {"duration": 0.053522, "end_time": "2025-07-31T07:46:12.278855", "exception": false, "start_time": "2025-07-31T07:46:12.225333", "status": "completed"}, "tags": []}, "outputs": [], "source": ["moon_link = \"https://ichef.bbci.co.uk/ace/standard/976/cpsprodpb/F574/production/_107563826_astronautedwine.aldrin-onthe-moon.jpg\""]}, {"cell_type": "markdown", "id": "062bb597", "metadata": {"papermill": {"duration": 0.04556, "end_time": "2025-07-31T07:46:12.372423", "exception": false, "start_time": "2025-07-31T07:46:12.326863", "status": "completed"}, "tags": []}, "source": ["![](https://ichef.bbci.co.uk/ace/standard/976/cpsprodpb/F574/production/_107563826_astronautedwine.aldrin-onthe-moon.jpg)"]}, {"cell_type": "markdown", "id": "da3b2d77", "metadata": {"papermill": {"duration": 0.043031, "end_time": "2025-07-31T07:46:12.458119", "exception": false, "start_time": "2025-07-31T07:46:12.415088", "status": "completed"}, "tags": []}, "source": ["And now we are composing the messages, by adding:\n", "* An audio file with the speech of JFK\n", "* An image of JFK\n", "* An image of <PERSON> astronaut walking on the Moon"]}, {"cell_type": "code", "execution_count": 19, "id": "def36ff8", "metadata": {"execution": {"iopub.execute_input": "2025-07-31T07:46:12.552375Z", "iopub.status.busy": "2025-07-31T07:46:12.551800Z", "iopub.status.idle": "2025-07-31T07:47:55.807003Z", "shell.execute_reply": "2025-07-31T07:47:55.806215Z"}, "papermill": {"duration": 103.349553, "end_time": "2025-07-31T07:47:55.854172", "exception": false, "start_time": "2025-07-31T07:46:12.504619", "status": "completed"}, "tags": []}, "outputs": [{"data": {"text/markdown": ["\n", "\n", "**<font color='blue'>Question:</font>** What is this audio and images about? How are they related?\n", "\n", "\n", "**<font color='red'>Answer:</font>** The audio and images are related to **President <PERSON>'s historic commitment to landing a man on the moon and returning him safely to Earth before the end of the decade.**\n", "\n", "Here's a breakdown:\n", "\n", "* **Audio:** The audio clip features President <PERSON>'s famous speech delivered on May 25, 1961, where he boldly declared this ambitious goal to the nation.\n", "* **Image 1:** The first image is a portrait of President <PERSON>.\n", "* **Image 2:** The second image shows an astronaut on the surface of the moon, representing the successful achievement of <PERSON>'s goal.\n", "\n", "**How they are related:** The audio provides the historical context – <PERSON>'s inspiring declaration of a national objective. The images visually represent the outcome of that commitment – the successful lunar landing. Together, they tell the story of a pivotal moment in history and the power of presidential vision and national ambition.\n", "\n", "\n", "**<font color='green'>Execution time:</font>** 103.25 sec."], "text/plain": ["<IPython.core.display.Markdown object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["user_input = \"What is this audio and images about? How are they related?\" \n", "_start = time()\n", "messages = [{\n", "    \"role\" : \"user\",\n", "    \"content\": [\n", "        { \"type\": \"audio\", \"audio\" : audio_file },\n", "        { \"type\": \"image\", \"image\" : jfk_link },\n", "        {\"type\": \"image\", \"image\" : moon_link},\n", "        { \"type\": \"text\",  \"text\" : user_input}\n", "    ]\n", "}]\n", "output = do_gemma_3n_inference(model, tokenizer, messages, max_new_tokens = 256)\n", "_end = time()\n", "formated_output = f\"Question: {user_input}\\nAnswer: {output}\\nExecution time: {np.round(_end-_start, 2)} sec.\"\n", "display(Markdown(colorize_text(formated_output)))"]}, {"cell_type": "markdown", "id": "94d06f26", "metadata": {"papermill": {"duration": 0.042573, "end_time": "2025-07-31T07:47:55.939413", "exception": false, "start_time": "2025-07-31T07:47:55.896840", "status": "completed"}, "tags": []}, "source": ["# Partial conclusions\n", "\n", "Gemma 3n 4B with Unsloth shows amazing multi-modal capabilities.\n", "\n", "We tested the model with a series of history-related questions.  \n", "\n", "Then we experimented with sounds, images, and with text, images, sounds combined.\n", "\n", "More tests will follow.\n", "\n", "Stay tuned..."]}], "metadata": {"accelerator": "GPU", "colab": {"gpuType": "T4", "provenance": []}, "kaggle": {"accelerator": "nvidiaTeslaT4", "dataSources": [{"databundleVersionId": 12693789, "sourceId": 105267, "sourceType": "competition"}], "dockerImageVersionId": 31041, "isGpuEnabled": true, "isInternetEnabled": true, "language": "python", "sourceType": "notebook"}, "kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.13"}, "papermill": {"default_parameters": {}, "duration": 651.356768, "end_time": "2025-07-31T07:47:59.106929", "environment_variables": {}, "exception": null, "input_path": "__notebook__.ipynb", "output_path": "__notebook__.ipynb", "parameters": {}, "start_time": "2025-07-31T07:37:07.750161", "version": "2.6.0"}, "widgets": {"application/vnd.jupyter.widget-state+json": {"state": {"024c774955bf4c9ca2165e63614a9fbe": {"model_module": "@jupyter-widgets/controls", "model_module_version": "2.0.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "2.0.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_allow_html": false, "layout": "IPY_MODEL_e8f8aaa13b234110a9c70067bc66d91c", "max": 3, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_28f186f5447d46afa5fe5ca7c2921337", "tabbable": null, "tooltip": null, "value": 3}}, "02d8547498ba40ee91ae276655c48838": {"model_module": "@jupyter-widgets/controls", "model_module_version": "2.0.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "2.0.0", "_view_name": "HTMLView", "description": "", "description_allow_html": false, "layout": "IPY_MODEL_b391dbb9e9164fb69b10e8f8ce819803", "placeholder": "​", "style": "IPY_MODEL_331a9de63f464e718c7c0a28e0d3a036", "tabbable": null, "tooltip": null, "value": "model-00001-of-00003.safetensors: 100%"}}, "06b8c4caccaf4118a6f45bbe98f30c83": {"model_module": "@jupyter-widgets/base", "model_module_version": "2.0.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "2.0.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border_bottom": null, "border_left": null, "border_right": null, "border_top": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "072cb533e704492b8e8a35b3020b02cf": {"model_module": "@jupyter-widgets/controls", "model_module_version": "2.0.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "2.0.0", "_view_name": "HTMLView", "description": "", "description_allow_html": false, "layout": "IPY_MODEL_68d050c46edb4713aafdcd8c5a3ab010", "placeholder": "​", "style": "IPY_MODEL_a5e1089a8d4a4da9888e69ddbb685a9a", "tabbable": null, "tooltip": null, "value": "processor_config.json: 100%"}}, "106d74413bea4c5a9ab048c15f9bd316": {"model_module": "@jupyter-widgets/controls", "model_module_version": "2.0.0", "model_name": "HTMLStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "HTMLStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "StyleView", "background": null, "description_width": "", "font_size": null, "text_color": null}}, "10f52f49238148f48afa0db9c586c875": {"model_module": "@jupyter-widgets/controls", "model_module_version": "2.0.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "2.0.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_837a83eb5259496480323f649b412e05", "IPY_MODEL_51a91a87be014ffa87c23ca8ccec3215", "IPY_MODEL_4e8e3bae91414c2dbf8ab9a772d41339"], "layout": "IPY_MODEL_dafeeb4a3a614aef91bc617da78e9d59", "tabbable": null, "tooltip": null}}, "119b5a96cf4b49ea9428a7b5b154d798": {"model_module": "@jupyter-widgets/controls", "model_module_version": "2.0.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "2.0.0", "_view_name": "HTMLView", "description": "", "description_allow_html": false, "layout": "IPY_MODEL_8d772ccecdb94c3199fd851b12a798e8", "placeholder": "​", "style": "IPY_MODEL_68634e97ea3741469d9149ef0f828c1a", "tabbable": null, "tooltip": null, "value": " 777/777 [00:00&lt;00:00, 98.7kB/s]"}}, "12b3096efccc4f099c868c176fbb7f2e": {"model_module": "@jupyter-widgets/base", "model_module_version": "2.0.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "2.0.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border_bottom": null, "border_left": null, "border_right": null, "border_top": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "padding": null, "right": null, "top": null, "visibility": null, "width": "20px"}}, "13fe6d6ac55a40c19ad27a81e7fdb578": {"model_module": "@jupyter-widgets/controls", "model_module_version": "2.0.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "2.0.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_28a419c3357345418ff6a539c6fc73fe", "IPY_MODEL_378a02f2a1e8414ea59c00c6526c6c28", "IPY_MODEL_777d1ccf453643f38c7430ad523abe4c"], "layout": "IPY_MODEL_8f19d042041646c185e3bb1bd93632b7", "tabbable": null, "tooltip": null}}, "192a7de01fa8429e99ccb10ad8f14cf3": {"model_module": "@jupyter-widgets/base", "model_module_version": "2.0.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "2.0.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border_bottom": null, "border_left": null, "border_right": null, "border_top": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "1bc92ad044c54b26b4a080351c60f619": {"model_module": "@jupyter-widgets/controls", "model_module_version": "2.0.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "1cf3ec27ef7547438b285d6581cd5ddb": {"model_module": "@jupyter-widgets/base", "model_module_version": "2.0.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "2.0.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border_bottom": null, "border_left": null, "border_right": null, "border_top": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "1edc107309474a098e63e1be54eb23cb": {"model_module": "@jupyter-widgets/controls", "model_module_version": "2.0.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "2.0.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_2f0be47b0be5403b951232fd1ce766f2", "IPY_MODEL_fcb4411d77b944999ff61b7693f0e95a", "IPY_MODEL_c70767ef3d4b4aeb80ec3832c0a8d366"], "layout": "IPY_MODEL_61c093555b8b4dd29157b91e6f75ff12", "tabbable": null, "tooltip": null}}, "2048a278308445ba863458da4be1de02": {"model_module": "@jupyter-widgets/controls", "model_module_version": "2.0.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "20a5ab7f379249f991ab9aca86d8e410": {"model_module": "@jupyter-widgets/base", "model_module_version": "2.0.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "2.0.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border_bottom": null, "border_left": null, "border_right": null, "border_top": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "20c96cc19fc744a19478176de9a1b91b": {"model_module": "@jupyter-widgets/controls", "model_module_version": "2.0.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "274ec42fe8e84aaeac1484593a8c0d32": {"model_module": "@jupyter-widgets/controls", "model_module_version": "2.0.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "28a419c3357345418ff6a539c6fc73fe": {"model_module": "@jupyter-widgets/controls", "model_module_version": "2.0.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "2.0.0", "_view_name": "HTMLView", "description": "", "description_allow_html": false, "layout": "IPY_MODEL_a1d28e1283f642889dc1c13a01d8ace7", "placeholder": "​", "style": "IPY_MODEL_642fed1545f64f6691e1252e7118db69", "tabbable": null, "tooltip": null, "value": "chat_template.jinja: "}}, "28f186f5447d46afa5fe5ca7c2921337": {"model_module": "@jupyter-widgets/controls", "model_module_version": "2.0.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "295f15f9405f48bfb0ee5e45c2a1faeb": {"model_module": "@jupyter-widgets/base", "model_module_version": "2.0.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "2.0.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border_bottom": null, "border_left": null, "border_right": null, "border_top": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "2c65ee457d794fa89c79e1bce13dee1d": {"model_module": "@jupyter-widgets/controls", "model_module_version": "2.0.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "2d389a97ab1048c09ec38473ba132409": {"model_module": "@jupyter-widgets/controls", "model_module_version": "2.0.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "2.0.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_allow_html": false, "layout": "IPY_MODEL_719d5a977b8e42bdab652c1759643039", "max": 3723417614, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_2c65ee457d794fa89c79e1bce13dee1d", "tabbable": null, "tooltip": null, "value": 3723417614}}, "2f0be47b0be5403b951232fd1ce766f2": {"model_module": "@jupyter-widgets/controls", "model_module_version": "2.0.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "2.0.0", "_view_name": "HTMLView", "description": "", "description_allow_html": false, "layout": "IPY_MODEL_3854d2d9d57a40f88f8a28d30d56a642", "placeholder": "​", "style": "IPY_MODEL_d48a07afca4e43e9af75c743df8f98fe", "tabbable": null, "tooltip": null, "value": "model-00003-of-00003.safetensors: 100%"}}, "2f4469314cac4b75b27ed448f5022292": {"model_module": "@jupyter-widgets/base", "model_module_version": "2.0.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "2.0.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border_bottom": null, "border_left": null, "border_right": null, "border_top": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "padding": null, "right": null, "top": null, "visibility": null, "width": "20px"}}, "2ff2f2ab97e54fef8508fb3444f86be1": {"model_module": "@jupyter-widgets/controls", "model_module_version": "2.0.0", "model_name": "HTMLStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "HTMLStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "StyleView", "background": null, "description_width": "", "font_size": null, "text_color": null}}, "316184ab32d443d9ab3e8dea4a738459": {"model_module": "@jupyter-widgets/base", "model_module_version": "2.0.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "2.0.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border_bottom": null, "border_left": null, "border_right": null, "border_top": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "331a9de63f464e718c7c0a28e0d3a036": {"model_module": "@jupyter-widgets/controls", "model_module_version": "2.0.0", "model_name": "HTMLStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "HTMLStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "StyleView", "background": null, "description_width": "", "font_size": null, "text_color": null}}, "34f5ad69fed94276b2649ff63aff9a34": {"model_module": "@jupyter-widgets/controls", "model_module_version": "2.0.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "2.0.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_allow_html": false, "layout": "IPY_MODEL_879e9814642e4930a62ffe9f781e5420", "max": 4987233092, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_274ec42fe8e84aaeac1484593a8c0d32", "tabbable": null, "tooltip": null, "value": 4987233092}}, "35c3c806497949e4996e28d4c2bd3076": {"model_module": "@jupyter-widgets/controls", "model_module_version": "2.0.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "2.0.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_f0870fd17827436fa40f69c5262264fc", "IPY_MODEL_34f5ad69fed94276b2649ff63aff9a34", "IPY_MODEL_da87d78565854d8680b6d85182e8a65a"], "layout": "IPY_MODEL_8a27858a8db147e7be69d96c721f919b", "tabbable": null, "tooltip": null}}, "378a02f2a1e8414ea59c00c6526c6c28": {"model_module": "@jupyter-widgets/controls", "model_module_version": "2.0.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "2.0.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_allow_html": false, "layout": "IPY_MODEL_688ea010c56d4eb39ef0cbac43e43257", "max": 1, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_f5c317103de44ec4ae75df643c4a58b9", "tabbable": null, "tooltip": null, "value": 1}}, "37ceb7aa475341078f6f3aa0a18baf0c": {"model_module": "@jupyter-widgets/base", "model_module_version": "2.0.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "2.0.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border_bottom": null, "border_left": null, "border_right": null, "border_top": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "3854d2d9d57a40f88f8a28d30d56a642": {"model_module": "@jupyter-widgets/base", "model_module_version": "2.0.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "2.0.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border_bottom": null, "border_left": null, "border_right": null, "border_top": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "3e3f32d8505f462db28e8b2ec602aaa3": {"model_module": "@jupyter-widgets/base", "model_module_version": "2.0.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "2.0.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border_bottom": null, "border_left": null, "border_right": null, "border_top": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "41436d7f818c49199ba350de0826ec48": {"model_module": "@jupyter-widgets/controls", "model_module_version": "2.0.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "2.0.0", "_view_name": "HTMLView", "description": "", "description_allow_html": false, "layout": "IPY_MODEL_6770677339924e468dfd8eb5228ef795", "placeholder": "​", "style": "IPY_MODEL_5826c124c3c7490e872a6cc82e4fd9c6", "tabbable": null, "tooltip": null, "value": "generation_config.json: 100%"}}, "444148e86238497083a3e4f73bf144f6": {"model_module": "@jupyter-widgets/controls", "model_module_version": "2.0.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "2.0.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_6fd480efb7de4ddcb4fd011b576a2cba", "IPY_MODEL_024c774955bf4c9ca2165e63614a9fbe", "IPY_MODEL_849678d038484037abe626649b547d43"], "layout": "IPY_MODEL_c73df37cace54c34b221732639a729f5", "tabbable": null, "tooltip": null}}, "4639eae5695e4989a7bf1569334c192f": {"model_module": "@jupyter-widgets/base", "model_module_version": "2.0.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "2.0.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border_bottom": null, "border_left": null, "border_right": null, "border_top": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "48c3e10f02f54ad8804d4b4fbb9c5d04": {"model_module": "@jupyter-widgets/base", "model_module_version": "2.0.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "2.0.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border_bottom": null, "border_left": null, "border_right": null, "border_top": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "4bfbbb77576846b28c9404c6257d7d26": {"model_module": "@jupyter-widgets/controls", "model_module_version": "2.0.0", "model_name": "HTMLStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "HTMLStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "StyleView", "background": null, "description_width": "", "font_size": null, "text_color": null}}, "4e8e3bae91414c2dbf8ab9a772d41339": {"model_module": "@jupyter-widgets/controls", "model_module_version": "2.0.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "2.0.0", "_view_name": "HTMLView", "description": "", "description_allow_html": false, "layout": "IPY_MODEL_1cf3ec27ef7547438b285d6581cd5ddb", "placeholder": "​", "style": "IPY_MODEL_b25aa7bd4a67407bbe8f1936021012cc", "tabbable": null, "tooltip": null, "value": " 4.70M/4.70M [00:00&lt;00:00, 396kB/s]"}}, "4eacf11ccfb0422d8756eb910388c3d1": {"model_module": "@jupyter-widgets/base", "model_module_version": "2.0.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "2.0.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border_bottom": null, "border_left": null, "border_right": null, "border_top": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "51a91a87be014ffa87c23ca8ccec3215": {"model_module": "@jupyter-widgets/controls", "model_module_version": "2.0.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "2.0.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_allow_html": false, "layout": "IPY_MODEL_aeded2e3ed80421fb6ae12ec095c7dea", "max": 4696020, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_f1b83744b141443cb1a9ee4b8b6d8e48", "tabbable": null, "tooltip": null, "value": 4696020}}, "5438d5132e894224baa011ca8b38f388": {"model_module": "@jupyter-widgets/base", "model_module_version": "2.0.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "2.0.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border_bottom": null, "border_left": null, "border_right": null, "border_top": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "5826c124c3c7490e872a6cc82e4fd9c6": {"model_module": "@jupyter-widgets/controls", "model_module_version": "2.0.0", "model_name": "HTMLStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "HTMLStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "StyleView", "background": null, "description_width": "", "font_size": null, "text_color": null}}, "590861bdcd614c8f9c199788bccb2176": {"model_module": "@jupyter-widgets/controls", "model_module_version": "2.0.0", "model_name": "HTMLStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "HTMLStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "StyleView", "background": null, "description_width": "", "font_size": null, "text_color": null}}, "5e1c6cd412f3489dad863966a1494e84": {"model_module": "@jupyter-widgets/controls", "model_module_version": "2.0.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "2.0.0", "_view_name": "HTMLView", "description": "", "description_allow_html": false, "layout": "IPY_MODEL_be6189d5a460449caf226ce8a272e570", "placeholder": "​", "style": "IPY_MODEL_77bffa1e59774a2596eb3bd35f62b2d4", "tabbable": null, "tooltip": null, "value": " 3.72G/3.72G [00:13&lt;00:00, 182MB/s]"}}, "5f433e41fce241dc97623f2031cc02c2": {"model_module": "@jupyter-widgets/controls", "model_module_version": "2.0.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "2.0.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_allow_html": false, "layout": "IPY_MODEL_f73318cb764c462490375b7668ceee4d", "max": 1, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_fd55191309624a9dad4bf9ca79d5c3f3", "tabbable": null, "tooltip": null, "value": 1}}, "615b153e48b0410f94d16607e7fa6022": {"model_module": "@jupyter-widgets/base", "model_module_version": "2.0.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "2.0.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border_bottom": null, "border_left": null, "border_right": null, "border_top": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "61c093555b8b4dd29157b91e6f75ff12": {"model_module": "@jupyter-widgets/base", "model_module_version": "2.0.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "2.0.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border_bottom": null, "border_left": null, "border_right": null, "border_top": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "6215f563c84a47ce9f211f8624f46e15": {"model_module": "@jupyter-widgets/controls", "model_module_version": "2.0.0", "model_name": "HTMLStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "HTMLStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "StyleView", "background": null, "description_width": "", "font_size": null, "text_color": null}}, "63a5045048b24835ae589bd55a0c3b13": {"model_module": "@jupyter-widgets/controls", "model_module_version": "2.0.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "2.0.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_02d8547498ba40ee91ae276655c48838", "IPY_MODEL_2d389a97ab1048c09ec38473ba132409", "IPY_MODEL_5e1c6cd412f3489dad863966a1494e84"], "layout": "IPY_MODEL_192a7de01fa8429e99ccb10ad8f14cf3", "tabbable": null, "tooltip": null}}, "642fed1545f64f6691e1252e7118db69": {"model_module": "@jupyter-widgets/controls", "model_module_version": "2.0.0", "model_name": "HTMLStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "HTMLStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "StyleView", "background": null, "description_width": "", "font_size": null, "text_color": null}}, "64a9d36e402c480b8a06ab2bfa73fa72": {"model_module": "@jupyter-widgets/controls", "model_module_version": "2.0.0", "model_name": "HTMLStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "HTMLStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "StyleView", "background": null, "description_width": "", "font_size": null, "text_color": null}}, "65a23b4f042f40f7ac2d4b9c2377e201": {"model_module": "@jupyter-widgets/controls", "model_module_version": "2.0.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "66a5124aa7f24a3eb77770d762b726e1": {"model_module": "@jupyter-widgets/controls", "model_module_version": "2.0.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "2.0.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_c6af9bf2f2ea4d8fa266558915b4f11c", "IPY_MODEL_98bbabc8d83444528dad3dd44ad3a1cd", "IPY_MODEL_bbb9bc990f9a469286d1c5711bcf17da"], "layout": "IPY_MODEL_7aa2597f5aaf4ab8a5036a722d32a983", "tabbable": null, "tooltip": null}}, "6770677339924e468dfd8eb5228ef795": {"model_module": "@jupyter-widgets/base", "model_module_version": "2.0.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "2.0.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border_bottom": null, "border_left": null, "border_right": null, "border_top": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "678a6aa20a7548d69338ec6941a38e60": {"model_module": "@jupyter-widgets/base", "model_module_version": "2.0.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "2.0.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border_bottom": null, "border_left": null, "border_right": null, "border_top": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "68634e97ea3741469d9149ef0f828c1a": {"model_module": "@jupyter-widgets/controls", "model_module_version": "2.0.0", "model_name": "HTMLStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "HTMLStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "StyleView", "background": null, "description_width": "", "font_size": null, "text_color": null}}, "688ea010c56d4eb39ef0cbac43e43257": {"model_module": "@jupyter-widgets/base", "model_module_version": "2.0.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "2.0.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border_bottom": null, "border_left": null, "border_right": null, "border_top": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "padding": null, "right": null, "top": null, "visibility": null, "width": "20px"}}, "68d050c46edb4713aafdcd8c5a3ab010": {"model_module": "@jupyter-widgets/base", "model_module_version": "2.0.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "2.0.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border_bottom": null, "border_left": null, "border_right": null, "border_top": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "6dd49edea8d64cb0ac01bb11d9c0857b": {"model_module": "@jupyter-widgets/base", "model_module_version": "2.0.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "2.0.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border_bottom": null, "border_left": null, "border_right": null, "border_top": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "6fd480efb7de4ddcb4fd011b576a2cba": {"model_module": "@jupyter-widgets/controls", "model_module_version": "2.0.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "2.0.0", "_view_name": "HTMLView", "description": "", "description_allow_html": false, "layout": "IPY_MODEL_06b8c4caccaf4118a6f45bbe98f30c83", "placeholder": "​", "style": "IPY_MODEL_6215f563c84a47ce9f211f8624f46e15", "tabbable": null, "tooltip": null, "value": "Loading checkpoint shards: 100%"}}, "719d5a977b8e42bdab652c1759643039": {"model_module": "@jupyter-widgets/base", "model_module_version": "2.0.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "2.0.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border_bottom": null, "border_left": null, "border_right": null, "border_top": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "7534a69706294871818f51f31a22e564": {"model_module": "@jupyter-widgets/controls", "model_module_version": "2.0.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "2.0.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_allow_html": false, "layout": "IPY_MODEL_12b3096efccc4f099c868c176fbb7f2e", "max": 1, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_20c96cc19fc744a19478176de9a1b91b", "tabbable": null, "tooltip": null, "value": 1}}, "777d1ccf453643f38c7430ad523abe4c": {"model_module": "@jupyter-widgets/controls", "model_module_version": "2.0.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "2.0.0", "_view_name": "HTMLView", "description": "", "description_allow_html": false, "layout": "IPY_MODEL_4639eae5695e4989a7bf1569334c192f", "placeholder": "​", "style": "IPY_MODEL_a50a358f055945329528a066d5d9a546", "tabbable": null, "tooltip": null, "value": " 1.63k/? [00:00&lt;00:00, 201kB/s]"}}, "77bffa1e59774a2596eb3bd35f62b2d4": {"model_module": "@jupyter-widgets/controls", "model_module_version": "2.0.0", "model_name": "HTMLStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "HTMLStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "StyleView", "background": null, "description_width": "", "font_size": null, "text_color": null}}, "789f85cc80b8405d91c27a65dd7424db": {"model_module": "@jupyter-widgets/controls", "model_module_version": "2.0.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "2.0.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_allow_html": false, "layout": "IPY_MODEL_4eacf11ccfb0422d8756eb910388c3d1", "max": 98, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_89dcdbee333741e4ac9d980dd27e7279", "tabbable": null, "tooltip": null, "value": 98}}, "7a5d597dfab247499f6d4e7fa9680fc3": {"model_module": "@jupyter-widgets/controls", "model_module_version": "2.0.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "2.0.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_072cb533e704492b8e8a35b3020b02cf", "IPY_MODEL_789f85cc80b8405d91c27a65dd7424db", "IPY_MODEL_a4b4f17e7fc141d990e5794a42a8eb87"], "layout": "IPY_MODEL_48c3e10f02f54ad8804d4b4fbb9c5d04", "tabbable": null, "tooltip": null}}, "7aa2597f5aaf4ab8a5036a722d32a983": {"model_module": "@jupyter-widgets/base", "model_module_version": "2.0.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "2.0.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border_bottom": null, "border_left": null, "border_right": null, "border_top": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "7b5883c00b07414fb805d113ff52a370": {"model_module": "@jupyter-widgets/controls", "model_module_version": "2.0.0", "model_name": "HTMLStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "HTMLStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "StyleView", "background": null, "description_width": "", "font_size": null, "text_color": null}}, "7bf27db9e94f44d3b2eea7a71648d045": {"model_module": "@jupyter-widgets/controls", "model_module_version": "2.0.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "2.0.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_allow_html": false, "layout": "IPY_MODEL_2f4469314cac4b75b27ed448f5022292", "max": 1, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_2048a278308445ba863458da4be1de02", "tabbable": null, "tooltip": null, "value": 1}}, "7c6652df9c404a90b717068cbd136132": {"model_module": "@jupyter-widgets/controls", "model_module_version": "2.0.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "2.0.0", "_view_name": "HTMLView", "description": "", "description_allow_html": false, "layout": "IPY_MODEL_f5d0a7e7c9bf4bc9b10665735a9fbc86", "placeholder": "​", "style": "IPY_MODEL_eccdd3df7e2749da93c47ade885930da", "tabbable": null, "tooltip": null, "value": " 370k/? [00:00&lt;00:00, 31.0MB/s]"}}, "837a83eb5259496480323f649b412e05": {"model_module": "@jupyter-widgets/controls", "model_module_version": "2.0.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "2.0.0", "_view_name": "HTMLView", "description": "", "description_allow_html": false, "layout": "IPY_MODEL_bf27810bc22648f0b6d7922ea2b4d8b3", "placeholder": "​", "style": "IPY_MODEL_d832126c1ebf4e80ae35800d7d9ad1e0", "tabbable": null, "tooltip": null, "value": "tokenizer.model: 100%"}}, "849678d038484037abe626649b547d43": {"model_module": "@jupyter-widgets/controls", "model_module_version": "2.0.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "2.0.0", "_view_name": "HTMLView", "description": "", "description_allow_html": false, "layout": "IPY_MODEL_615b153e48b0410f94d16607e7fa6022", "placeholder": "​", "style": "IPY_MODEL_bbba28cc544d4c99ba326e1d617c2685", "tabbable": null, "tooltip": null, "value": " 3/3 [00:04&lt;00:00,  1.34s/it]"}}, "863b23d15e3b40c8a171f38735063e1d": {"model_module": "@jupyter-widgets/controls", "model_module_version": "2.0.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "2.0.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_b1b4d43648e349169ca1694df6ebd021", "IPY_MODEL_5f433e41fce241dc97623f2031cc02c2", "IPY_MODEL_7c6652df9c404a90b717068cbd136132"], "layout": "IPY_MODEL_888bd207dad84a66a391c127cd4f5d1f", "tabbable": null, "tooltip": null}}, "879e9814642e4930a62ffe9f781e5420": {"model_module": "@jupyter-widgets/base", "model_module_version": "2.0.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "2.0.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border_bottom": null, "border_left": null, "border_right": null, "border_top": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "888bd207dad84a66a391c127cd4f5d1f": {"model_module": "@jupyter-widgets/base", "model_module_version": "2.0.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "2.0.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border_bottom": null, "border_left": null, "border_right": null, "border_top": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "89dcdbee333741e4ac9d980dd27e7279": {"model_module": "@jupyter-widgets/controls", "model_module_version": "2.0.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "8a27858a8db147e7be69d96c721f919b": {"model_module": "@jupyter-widgets/base", "model_module_version": "2.0.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "2.0.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border_bottom": null, "border_left": null, "border_right": null, "border_top": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "8be475bea33d4572a3ed66d8ddae33cf": {"model_module": "@jupyter-widgets/base", "model_module_version": "2.0.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "2.0.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border_bottom": null, "border_left": null, "border_right": null, "border_top": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "8d772ccecdb94c3199fd851b12a798e8": {"model_module": "@jupyter-widgets/base", "model_module_version": "2.0.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "2.0.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border_bottom": null, "border_left": null, "border_right": null, "border_top": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "8f19d042041646c185e3bb1bd93632b7": {"model_module": "@jupyter-widgets/base", "model_module_version": "2.0.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "2.0.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border_bottom": null, "border_left": null, "border_right": null, "border_top": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "9462598000c24d72935eb7e99a92d8cc": {"model_module": "@jupyter-widgets/controls", "model_module_version": "2.0.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "2.0.0", "_view_name": "HTMLView", "description": "", "description_allow_html": false, "layout": "IPY_MODEL_6dd49edea8d64cb0ac01bb11d9c0857b", "placeholder": "​", "style": "IPY_MODEL_a6cf065358f445089580ade282c90f97", "tabbable": null, "tooltip": null, "value": " 210/210 [00:00&lt;00:00, 25.0kB/s]"}}, "96afa25609a44b4e8687c340736dcb8d": {"model_module": "@jupyter-widgets/base", "model_module_version": "2.0.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "2.0.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border_bottom": null, "border_left": null, "border_right": null, "border_top": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "98bbabc8d83444528dad3dd44ad3a1cd": {"model_module": "@jupyter-widgets/controls", "model_module_version": "2.0.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "2.0.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_allow_html": false, "layout": "IPY_MODEL_96afa25609a44b4e8687c340736dcb8d", "max": 33442553, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_b4cfae0992174dc78c818fceb30024e4", "tabbable": null, "tooltip": null, "value": 33442553}}, "a07d2e89a4924d189067a985bfa9b9d6": {"model_module": "@jupyter-widgets/controls", "model_module_version": "2.0.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "a118373ff4df4386a73e63c32eb0a2a3": {"model_module": "@jupyter-widgets/base", "model_module_version": "2.0.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "2.0.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border_bottom": null, "border_left": null, "border_right": null, "border_top": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "a1d28e1283f642889dc1c13a01d8ace7": {"model_module": "@jupyter-widgets/base", "model_module_version": "2.0.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "2.0.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border_bottom": null, "border_left": null, "border_right": null, "border_top": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "a2cd60e4ef1d49769bc0c25e4e915c24": {"model_module": "@jupyter-widgets/base", "model_module_version": "2.0.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "2.0.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border_bottom": null, "border_left": null, "border_right": null, "border_top": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "a31f3d10cdec497bb4bf7aaea25ff98b": {"model_module": "@jupyter-widgets/controls", "model_module_version": "2.0.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "2.0.0", "_view_name": "HTMLView", "description": "", "description_allow_html": false, "layout": "IPY_MODEL_8be475bea33d4572a3ed66d8ddae33cf", "placeholder": "​", "style": "IPY_MODEL_cd1ab7c1e42d4189bf78f3909597bbe4", "tabbable": null, "tooltip": null, "value": " 1.20M/? [00:00&lt;00:00, 76.5MB/s]"}}, "a41fb28ebaf84243be130de62607c53d": {"model_module": "@jupyter-widgets/controls", "model_module_version": "2.0.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "2.0.0", "_view_name": "HTMLView", "description": "", "description_allow_html": false, "layout": "IPY_MODEL_cf39b59044f44d4daf291262819fe8b5", "placeholder": "​", "style": "IPY_MODEL_106d74413bea4c5a9ab048c15f9bd316", "tabbable": null, "tooltip": null, "value": "special_tokens_map.json: 100%"}}, "a4b4f17e7fc141d990e5794a42a8eb87": {"model_module": "@jupyter-widgets/controls", "model_module_version": "2.0.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "2.0.0", "_view_name": "HTMLView", "description": "", "description_allow_html": false, "layout": "IPY_MODEL_ded79722c59d4047bf6c269c9fe70d43", "placeholder": "​", "style": "IPY_MODEL_e436fe71ad4a4b30b917695e90a7aef9", "tabbable": null, "tooltip": null, "value": " 98.0/98.0 [00:00&lt;00:00, 12.5kB/s]"}}, "a50a358f055945329528a066d5d9a546": {"model_module": "@jupyter-widgets/controls", "model_module_version": "2.0.0", "model_name": "HTMLStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "HTMLStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "StyleView", "background": null, "description_width": "", "font_size": null, "text_color": null}}, "a5e1089a8d4a4da9888e69ddbb685a9a": {"model_module": "@jupyter-widgets/controls", "model_module_version": "2.0.0", "model_name": "HTMLStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "HTMLStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "StyleView", "background": null, "description_width": "", "font_size": null, "text_color": null}}, "a6cf065358f445089580ade282c90f97": {"model_module": "@jupyter-widgets/controls", "model_module_version": "2.0.0", "model_name": "HTMLStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "HTMLStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "StyleView", "background": null, "description_width": "", "font_size": null, "text_color": null}}, "aa49f5a52219400dac05cd9627a5b304": {"model_module": "@jupyter-widgets/controls", "model_module_version": "2.0.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "2.0.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_b7f8115a32864162b39653424314c880", "IPY_MODEL_7bf27db9e94f44d3b2eea7a71648d045", "IPY_MODEL_b211f2fab5904fccb2952b5a3591df96"], "layout": "IPY_MODEL_aab327d5508040daa25ced9090d708cc", "tabbable": null, "tooltip": null}}, "aab327d5508040daa25ced9090d708cc": {"model_module": "@jupyter-widgets/base", "model_module_version": "2.0.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "2.0.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border_bottom": null, "border_left": null, "border_right": null, "border_top": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "aeded2e3ed80421fb6ae12ec095c7dea": {"model_module": "@jupyter-widgets/base", "model_module_version": "2.0.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "2.0.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border_bottom": null, "border_left": null, "border_right": null, "border_top": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "b153769d173f4d6cb97ab6124bb1e894": {"model_module": "@jupyter-widgets/base", "model_module_version": "2.0.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "2.0.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border_bottom": null, "border_left": null, "border_right": null, "border_top": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "b1b4d43648e349169ca1694df6ebd021": {"model_module": "@jupyter-widgets/controls", "model_module_version": "2.0.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "2.0.0", "_view_name": "HTMLView", "description": "", "description_allow_html": false, "layout": "IPY_MODEL_a118373ff4df4386a73e63c32eb0a2a3", "placeholder": "​", "style": "IPY_MODEL_bb3542f217464b719194ce50be3bf946", "tabbable": null, "tooltip": null, "value": "model.safetensors.index.json: "}}, "b211f2fab5904fccb2952b5a3591df96": {"model_module": "@jupyter-widgets/controls", "model_module_version": "2.0.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "2.0.0", "_view_name": "HTMLView", "description": "", "description_allow_html": false, "layout": "IPY_MODEL_678a6aa20a7548d69338ec6941a38e60", "placeholder": "​", "style": "IPY_MODEL_fcc2544529db42f8865af3059820ed44", "tabbable": null, "tooltip": null, "value": " 1.09k/? [00:00&lt;00:00, 92.9kB/s]"}}, "b25aa7bd4a67407bbe8f1936021012cc": {"model_module": "@jupyter-widgets/controls", "model_module_version": "2.0.0", "model_name": "HTMLStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "HTMLStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "StyleView", "background": null, "description_width": "", "font_size": null, "text_color": null}}, "b32a1f1e43704b7cac87eb54dafa3c82": {"model_module": "@jupyter-widgets/controls", "model_module_version": "2.0.0", "model_name": "HTMLStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "HTMLStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "StyleView", "background": null, "description_width": "", "font_size": null, "text_color": null}}, "b391dbb9e9164fb69b10e8f8ce819803": {"model_module": "@jupyter-widgets/base", "model_module_version": "2.0.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "2.0.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border_bottom": null, "border_left": null, "border_right": null, "border_top": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "b4cfae0992174dc78c818fceb30024e4": {"model_module": "@jupyter-widgets/controls", "model_module_version": "2.0.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "b7f8115a32864162b39653424314c880": {"model_module": "@jupyter-widgets/controls", "model_module_version": "2.0.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "2.0.0", "_view_name": "HTMLView", "description": "", "description_allow_html": false, "layout": "IPY_MODEL_b80b460d987c44239437c0189c0a50e2", "placeholder": "​", "style": "IPY_MODEL_590861bdcd614c8f9c199788bccb2176", "tabbable": null, "tooltip": null, "value": "preprocessor_config.json: "}}, "b80b460d987c44239437c0189c0a50e2": {"model_module": "@jupyter-widgets/base", "model_module_version": "2.0.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "2.0.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border_bottom": null, "border_left": null, "border_right": null, "border_top": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "b96ef3ff9d6c4973906d5a6a33c56b90": {"model_module": "@jupyter-widgets/controls", "model_module_version": "2.0.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "2.0.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_allow_html": false, "layout": "IPY_MODEL_5438d5132e894224baa011ca8b38f388", "max": 777, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_a07d2e89a4924d189067a985bfa9b9d6", "tabbable": null, "tooltip": null, "value": 777}}, "bb3542f217464b719194ce50be3bf946": {"model_module": "@jupyter-widgets/controls", "model_module_version": "2.0.0", "model_name": "HTMLStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "HTMLStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "StyleView", "background": null, "description_width": "", "font_size": null, "text_color": null}}, "bbb9bc990f9a469286d1c5711bcf17da": {"model_module": "@jupyter-widgets/controls", "model_module_version": "2.0.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "2.0.0", "_view_name": "HTMLView", "description": "", "description_allow_html": false, "layout": "IPY_MODEL_f09ee0cae38b497ea5a66e8ecc8e4f3f", "placeholder": "​", "style": "IPY_MODEL_7b5883c00b07414fb805d113ff52a370", "tabbable": null, "tooltip": null, "value": " 33.4M/33.4M [00:00&lt;00:00, 77.3MB/s]"}}, "bbba28cc544d4c99ba326e1d617c2685": {"model_module": "@jupyter-widgets/controls", "model_module_version": "2.0.0", "model_name": "HTMLStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "HTMLStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "StyleView", "background": null, "description_width": "", "font_size": null, "text_color": null}}, "bd22c7dc82194fb0ad3aa1ef582ca4d3": {"model_module": "@jupyter-widgets/controls", "model_module_version": "2.0.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "2.0.0", "_view_name": "HTMLView", "description": "", "description_allow_html": false, "layout": "IPY_MODEL_fcca86d115cd459b85e6bda25d1bd158", "placeholder": "​", "style": "IPY_MODEL_4bfbbb77576846b28c9404c6257d7d26", "tabbable": null, "tooltip": null, "value": "tokenizer_config.json: "}}, "be6189d5a460449caf226ce8a272e570": {"model_module": "@jupyter-widgets/base", "model_module_version": "2.0.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "2.0.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border_bottom": null, "border_left": null, "border_right": null, "border_top": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "bf27810bc22648f0b6d7922ea2b4d8b3": {"model_module": "@jupyter-widgets/base", "model_module_version": "2.0.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "2.0.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border_bottom": null, "border_left": null, "border_right": null, "border_top": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "c502039d4039426ca5b1e95221ebbfa4": {"model_module": "@jupyter-widgets/base", "model_module_version": "2.0.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "2.0.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border_bottom": null, "border_left": null, "border_right": null, "border_top": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "c6af9bf2f2ea4d8fa266558915b4f11c": {"model_module": "@jupyter-widgets/controls", "model_module_version": "2.0.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "2.0.0", "_view_name": "HTMLView", "description": "", "description_allow_html": false, "layout": "IPY_MODEL_3e3f32d8505f462db28e8b2ec602aaa3", "placeholder": "​", "style": "IPY_MODEL_2ff2f2ab97e54fef8508fb3444f86be1", "tabbable": null, "tooltip": null, "value": "tokenizer.json: 100%"}}, "c70767ef3d4b4aeb80ec3832c0a8d366": {"model_module": "@jupyter-widgets/controls", "model_module_version": "2.0.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "2.0.0", "_view_name": "HTMLView", "description": "", "description_allow_html": false, "layout": "IPY_MODEL_20a5ab7f379249f991ab9aca86d8e410", "placeholder": "​", "style": "IPY_MODEL_64a9d36e402c480b8a06ab2bfa73fa72", "tabbable": null, "tooltip": null, "value": " 1.15G/1.15G [00:04&lt;00:00, 684MB/s]"}}, "c73df37cace54c34b221732639a729f5": {"model_module": "@jupyter-widgets/base", "model_module_version": "2.0.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "2.0.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border_bottom": null, "border_left": null, "border_right": null, "border_top": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "c9d180b9ac4241a799e0dc609ff097fb": {"model_module": "@jupyter-widgets/controls", "model_module_version": "2.0.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "2.0.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_allow_html": false, "layout": "IPY_MODEL_b153769d173f4d6cb97ab6124bb1e894", "max": 210, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_65a23b4f042f40f7ac2d4b9c2377e201", "tabbable": null, "tooltip": null, "value": 210}}, "cb037d75ff17429ab7c2f8e82600ffa1": {"model_module": "@jupyter-widgets/controls", "model_module_version": "2.0.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "2.0.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_a41fb28ebaf84243be130de62607c53d", "IPY_MODEL_b96ef3ff9d6c4973906d5a6a33c56b90", "IPY_MODEL_119b5a96cf4b49ea9428a7b5b154d798"], "layout": "IPY_MODEL_e4683c81042e4bcd87ce529a69d3f0ba", "tabbable": null, "tooltip": null}}, "cd1ab7c1e42d4189bf78f3909597bbe4": {"model_module": "@jupyter-widgets/controls", "model_module_version": "2.0.0", "model_name": "HTMLStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "HTMLStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "StyleView", "background": null, "description_width": "", "font_size": null, "text_color": null}}, "cf39b59044f44d4daf291262819fe8b5": {"model_module": "@jupyter-widgets/base", "model_module_version": "2.0.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "2.0.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border_bottom": null, "border_left": null, "border_right": null, "border_top": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "d48a07afca4e43e9af75c743df8f98fe": {"model_module": "@jupyter-widgets/controls", "model_module_version": "2.0.0", "model_name": "HTMLStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "HTMLStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "StyleView", "background": null, "description_width": "", "font_size": null, "text_color": null}}, "d832126c1ebf4e80ae35800d7d9ad1e0": {"model_module": "@jupyter-widgets/controls", "model_module_version": "2.0.0", "model_name": "HTMLStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "HTMLStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "StyleView", "background": null, "description_width": "", "font_size": null, "text_color": null}}, "da87d78565854d8680b6d85182e8a65a": {"model_module": "@jupyter-widgets/controls", "model_module_version": "2.0.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "2.0.0", "_view_name": "HTMLView", "description": "", "description_allow_html": false, "layout": "IPY_MODEL_c502039d4039426ca5b1e95221ebbfa4", "placeholder": "​", "style": "IPY_MODEL_de44f7fd4b204f0da9c08cc2cb77cab8", "tabbable": null, "tooltip": null, "value": " 4.99G/4.99G [00:20&lt;00:00, 739MB/s]"}}, "dafeeb4a3a614aef91bc617da78e9d59": {"model_module": "@jupyter-widgets/base", "model_module_version": "2.0.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "2.0.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border_bottom": null, "border_left": null, "border_right": null, "border_top": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "de44f7fd4b204f0da9c08cc2cb77cab8": {"model_module": "@jupyter-widgets/controls", "model_module_version": "2.0.0", "model_name": "HTMLStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "HTMLStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "StyleView", "background": null, "description_width": "", "font_size": null, "text_color": null}}, "ded79722c59d4047bf6c269c9fe70d43": {"model_module": "@jupyter-widgets/base", "model_module_version": "2.0.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "2.0.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border_bottom": null, "border_left": null, "border_right": null, "border_top": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "e436fe71ad4a4b30b917695e90a7aef9": {"model_module": "@jupyter-widgets/controls", "model_module_version": "2.0.0", "model_name": "HTMLStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "HTMLStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "StyleView", "background": null, "description_width": "", "font_size": null, "text_color": null}}, "e4683c81042e4bcd87ce529a69d3f0ba": {"model_module": "@jupyter-widgets/base", "model_module_version": "2.0.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "2.0.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border_bottom": null, "border_left": null, "border_right": null, "border_top": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "e8f8aaa13b234110a9c70067bc66d91c": {"model_module": "@jupyter-widgets/base", "model_module_version": "2.0.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "2.0.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border_bottom": null, "border_left": null, "border_right": null, "border_top": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "eccdd3df7e2749da93c47ade885930da": {"model_module": "@jupyter-widgets/controls", "model_module_version": "2.0.0", "model_name": "HTMLStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "HTMLStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "StyleView", "background": null, "description_width": "", "font_size": null, "text_color": null}}, "f0870fd17827436fa40f69c5262264fc": {"model_module": "@jupyter-widgets/controls", "model_module_version": "2.0.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "2.0.0", "_view_name": "HTMLView", "description": "", "description_allow_html": false, "layout": "IPY_MODEL_295f15f9405f48bfb0ee5e45c2a1faeb", "placeholder": "​", "style": "IPY_MODEL_b32a1f1e43704b7cac87eb54dafa3c82", "tabbable": null, "tooltip": null, "value": "model-00002-of-00003.safetensors: 100%"}}, "f09ee0cae38b497ea5a66e8ecc8e4f3f": {"model_module": "@jupyter-widgets/base", "model_module_version": "2.0.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "2.0.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border_bottom": null, "border_left": null, "border_right": null, "border_top": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "f1609a45a3eb4efc88f3fbd19a66b11f": {"model_module": "@jupyter-widgets/controls", "model_module_version": "2.0.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "2.0.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_bd22c7dc82194fb0ad3aa1ef582ca4d3", "IPY_MODEL_7534a69706294871818f51f31a22e564", "IPY_MODEL_a31f3d10cdec497bb4bf7aaea25ff98b"], "layout": "IPY_MODEL_316184ab32d443d9ab3e8dea4a738459", "tabbable": null, "tooltip": null}}, "f1b83744b141443cb1a9ee4b8b6d8e48": {"model_module": "@jupyter-widgets/controls", "model_module_version": "2.0.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "f5c317103de44ec4ae75df643c4a58b9": {"model_module": "@jupyter-widgets/controls", "model_module_version": "2.0.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "f5d0a7e7c9bf4bc9b10665735a9fbc86": {"model_module": "@jupyter-widgets/base", "model_module_version": "2.0.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "2.0.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border_bottom": null, "border_left": null, "border_right": null, "border_top": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "f73318cb764c462490375b7668ceee4d": {"model_module": "@jupyter-widgets/base", "model_module_version": "2.0.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "2.0.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border_bottom": null, "border_left": null, "border_right": null, "border_top": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "padding": null, "right": null, "top": null, "visibility": null, "width": "20px"}}, "f8f7b40f13f14b0fbb56361588808dde": {"model_module": "@jupyter-widgets/controls", "model_module_version": "2.0.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "2.0.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_41436d7f818c49199ba350de0826ec48", "IPY_MODEL_c9d180b9ac4241a799e0dc609ff097fb", "IPY_MODEL_9462598000c24d72935eb7e99a92d8cc"], "layout": "IPY_MODEL_37ceb7aa475341078f6f3aa0a18baf0c", "tabbable": null, "tooltip": null}}, "fcb4411d77b944999ff61b7693f0e95a": {"model_module": "@jupyter-widgets/controls", "model_module_version": "2.0.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "2.0.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_allow_html": false, "layout": "IPY_MODEL_a2cd60e4ef1d49769bc0c25e4e915c24", "max": 1148535480, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_1bc92ad044c54b26b4a080351c60f619", "tabbable": null, "tooltip": null, "value": 1148535480}}, "fcc2544529db42f8865af3059820ed44": {"model_module": "@jupyter-widgets/controls", "model_module_version": "2.0.0", "model_name": "HTMLStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "HTMLStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "StyleView", "background": null, "description_width": "", "font_size": null, "text_color": null}}, "fcca86d115cd459b85e6bda25d1bd158": {"model_module": "@jupyter-widgets/base", "model_module_version": "2.0.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "2.0.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border_bottom": null, "border_left": null, "border_right": null, "border_top": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "fd55191309624a9dad4bf9ca79d5c3f3": {"model_module": "@jupyter-widgets/controls", "model_module_version": "2.0.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}}, "version_major": 2, "version_minor": 0}}}, "nbformat": 4, "nbformat_minor": 5}