"""
🤖 Gemma 3N Model Entegrasyonu
Kaggle ortamında Gemma 3N ile AI koçluk sistemi
"""

import os
import gc
import torch
import base64
import numpy as np
from PIL import Image
import io
from typing import List, Dict, Any, Optional

# Gemma 3N için gerekli import'lar (<PERSON><PERSON> ortamında)
try:
    from unsloth import FastModel
    GEMMA_AVAILABLE = True
    print("✅ Unsloth ve Gemma 3N kütüphaneleri yüklendi")
except ImportError:
    GEMMA_AVAILABLE = False
    print("⚠️ Gemma 3N kütüphaneleri bulunamadı. Simülasyon modunda çalışılacak.")

class GemmaCoach:
    """Gemma 3N tabanlı AI eğitim koçu"""
    
    def __init__(self):
        self.model = None
        self.tokenizer = None
        self.model_loaded = False
        self.device = "cuda" if torch.cuda.is_available() else "cpu"
        
        print(f"🔧 Cihaz: {self.device}")
        
        if GEMMA_AVAILABLE and torch.cuda.is_available():
            self.load_model()
        else:
            print("💡 Simülasyon modunda çalışılıyor")
    
    def load_model(self):
        """Gemma 3N modelini yükle"""
        try:
            print("🦥 Gemma 3N modeli yükleniyor...")
            
            self.model, self.tokenizer = FastModel.from_pretrained(
                model_name="unsloth/gemma-3n-E4B-it",
                dtype=None,
                max_seq_length=1024,
                load_in_4bit=True,
                full_finetuning=False,
            )
            
            self.model_loaded = True
            print("✅ Gemma 3N modeli başarıyla yüklendi!")
            
        except Exception as e:
            print(f"❌ Model yükleme hatası: {e}")
            print("💡 Simülasyon moduna geçiliyor...")
            self.model_loaded = False
    
    def create_coaching_prompt(self, situation: str, context: Dict[str, Any]) -> List[Dict[str, Any]]:
        """AI koç için özel prompt oluştur"""
        
        system_prompt = """Sen bir AI eğitim koçusun. Öğrencileri motive etmek, odaklanmalarına yardım etmek ve öğrenme süreçlerini iyileştirmek için tasarlandın.

Özellikler:
- Pozitif, destekleyici ve samimi ol
- Kısa ama etkili mesajlar ver (maksimum 2-3 cümle)
- Öğrencinin durumuna göre kişiselleştirilmiş öneriler sun
- Türkçe yanıt ver
- Emoji kullanarak mesajları daha canlı hale getir
- Motivasyonu artıracak, cesaretlendirecek bir ton kullan

Durumlar:
- Odaklanmış: Övgü ve teşvik
- Dikkatsiz: Nazik hatırlatma ve odaklanma önerisi  
- Yorgun: Mola önerisi ve motivasyon
- Başlangıç: Coşkulu karşılama
- Bitiş: Tebrik ve başarı vurgusu"""

        # Bağlam bilgilerini hazırla
        context_text = f"""
Durum: {situation}
Çalışma süresi: {context.get('study_duration', 0)} dakika
Dikkat skoru: {context.get('attention_score', 'Bilinmiyor')}
Yüz tespiti: {context.get('face_detected', False)}
Önceki geri bildirim: {context.get('last_feedback', 'Yok')}
"""

        messages = [
            {
                "role": "system", 
                "content": [{"type": "text", "text": system_prompt}]
            },
            {
                "role": "user", 
                "content": [{"type": "text", "text": context_text}]
            }
        ]
        
        return messages
    
    def generate_coaching_response(self, situation: str, context: Dict[str, Any]) -> str:
        """AI koç yanıtı oluştur"""
        
        if not self.model_loaded:
            return self._simulate_response(situation, context)
        
        try:
            # Prompt'u hazırla
            messages = self.create_coaching_prompt(situation, context)
            
            # Tokenize et
            inputs = self.tokenizer.apply_chat_template(
                messages,
                add_generation_prompt=True,
                tokenize=True,
                return_dict=True,
                return_tensors="pt"
            ).to(self.device)
            
            # Generate
            with torch.no_grad():
                output_ids = self.model.generate(
                    **inputs,
                    max_new_tokens=64,  # Kısa yanıtlar için
                    temperature=0.8,
                    top_p=0.9,
                    top_k=50,
                    do_sample=True,
                    pad_token_id=self.tokenizer.eos_token_id
                )
            
            # Decode
            generated_text = self.tokenizer.decode(
                output_ids[0][inputs['input_ids'].shape[-1]:], 
                skip_special_tokens=True
            )
            
            # Bellek temizliği
            del inputs, output_ids
            torch.cuda.empty_cache()
            gc.collect()
            
            return generated_text.strip()
            
        except Exception as e:
            print(f"⚠️ Model inference hatası: {e}")
            return self._simulate_response(situation, context)
    
    def _simulate_response(self, situation: str, context: Dict[str, Any]) -> str:
        """Model yokken simüle edilmiş yanıt"""
        import random
        
        responses = {
            'başlangıç': [
                "🎉 Harika! Yeni bir çalışma seansına başlıyoruz. Size eşlik etmekten mutluyum!",
                "💪 Motive olun! Bu seansı birlikte verimli geçirelim. Başarıya odaklanın!",
                "🚀 Hazırsınız! Hedeflerinize doğru ilerleyin, ben yanınızdayım."
            ],
            'odaklanmış': [
                "👍 Mükemmel! Konsantrasyonunuz harika. Bu tempoda devam edin!",
                "🎯 Odaklanmanız çok iyi. Performansınızdan gurur duyuyorum!",
                "⭐ Harika gidiyorsunuz! Bu enerjiyle başarı kaçınılmaz."
            ],
            'dikkatsiz': [
                "🔔 Dikkatinizi toplamaya çalışın. Derin bir nefes alıp odaklanın.",
                "🧘 Biraz konsantrasyon zamanı. Hedeflerinizi hatırlayın, yapabilirsiniz!",
                "💡 Odaklanmak için kısa bir mola verin. Sonra daha güçlü dönün!"
            ],
            'yorgun': [
                "☕ Yorgun görünüyorsunuz. 5 dakika mola verin, sonra devam edelim.",
                "🚶 Ayağa kalkıp biraz hareket edin. Enerji için iyi olacak!",
                "💧 Su içmeyi unutmayın. Dinlenin ve güçlü dönün!"
            ],
            'mola': [
                "⏰ Mola zamanı geldi! Biraz dinlenin, beyin için çok önemli.",
                "🌟 Harika çalışıyorsunuz! Şimdi kısa bir mola hak ettiniz.",
                "🔄 Mola vermek performansı artırır. Dinlenin ve güçlü dönün!"
            ],
            'bitiş': [
                "🎊 Tebrikler! Harika bir çalışma seansı tamamladınız!",
                "✨ Bugün gerçekten iyi çalıştınız. Kendinizle gurur duyun!",
                "🏆 Başarılı bir seans! Hedeflerinize bir adım daha yaklaştınız."
            ]
        }
        
        situation_responses = responses.get(situation, responses['odaklanmış'])
        base_response = random.choice(situation_responses)
        
        # Bağlama göre ek bilgi ekle
        duration = context.get('study_duration', 0)
        if duration > 0:
            if duration >= 25:
                base_response += f" {duration} dakikadır çalışıyorsunuz, mola zamanı!"
            elif duration >= 10:
                base_response += f" {duration} dakikadır harika gidiyorsunuz!"
        
        return base_response
    
    def analyze_multimodal_input(self, image_data: Optional[str] = None, 
                                audio_data: Optional[str] = None,
                                text_input: Optional[str] = None) -> Dict[str, Any]:
        """Multimodal veri analizi (gelecek özellik)"""
        
        analysis = {
            'attention_level': 'orta',
            'emotion': 'nötr',
            'engagement': 0.7,
            'recommendations': []
        }
        
        # Görüntü analizi simülasyonu
        if image_data:
            analysis['face_detected'] = True
            analysis['attention_level'] = 'yüksek'
            analysis['recommendations'].append('Görsel odaklanma iyi')
        
        # Ses analizi simülasyonu  
        if audio_data:
            analysis['voice_energy'] = 'orta'
            analysis['recommendations'].append('Ses tonu normal')
        
        # Metin analizi simülasyonu
        if text_input:
            analysis['text_sentiment'] = 'pozitif'
            analysis['recommendations'].append('Metin girişi aktif')
        
        return analysis
    
    def cleanup(self):
        """Bellek temizliği"""
        if self.model_loaded:
            del self.model, self.tokenizer
            torch.cuda.empty_cache()
            gc.collect()
            print("🧹 Model belleği temizlendi")

# Global coach instance
gemma_coach = GemmaCoach()

def get_ai_coaching_response(situation: str, context: Dict[str, Any]) -> str:
    """AI koç yanıtı al (dış kullanım için)"""
    return gemma_coach.generate_coaching_response(situation, context)

def analyze_student_state(image_data: Optional[str] = None) -> Dict[str, Any]:
    """Öğrenci durumu analizi (dış kullanım için)"""
    return gemma_coach.analyze_multimodal_input(image_data=image_data)

if __name__ == "__main__":
    # Test
    print("🧪 Gemma Coach Test:")
    
    test_context = {
        'study_duration': 15,
        'attention_score': 85,
        'face_detected': True
    }
    
    response = get_ai_coaching_response('odaklanmış', test_context)
    print(f"Test yanıt: {response}")
