# 🎓 Gemma 3N AI Eğitim Koçu

## 🎯 Vizyon
*"The future of education isn't AI replacing teachers—it's AI becoming omniscient student companions that know learners better than they know themselves. Through multimodal consciousness (LLM+VLM+audio), AI achieves 360° student awareness that no human teacher could match, making traditional classroom observation as primitive as reading by candlelight."*

## ✨ Gradio Web Arayüzü ile Kolay Erişim

Bu proje **Gradio web arayüzü** ile çalışır! <PERSON><PERSON><PERSON><PERSON><PERSON> kurulum yerine, do<PERSON><PERSON><PERSON> taray<PERSON>cınızdan erişebileceğiniz modern bir web uygulaması.

## 🚀 Özellikler

### 🤖 Multimodal AI Analiz
- **Görsel Analiz**: Web kamerası ile öğrencinin dikkat seviyesi
- **Gerçek Zamanlı İzleme**: Sürekli durum değerlendirmesi
- **Akıllı Geri Bildirim**: Gemma 3N ile kişiselleştirilmiş öneriler

### 📊 <PERSON><PERSON><PERSON><PERSON> İstatistik<PERSON>
- **Çalışma Süresi**: Otomatik zaman takibi
- **Dikkat Skoru**: Odaklanma seviyesi ölçümü
- **Motivasyon Seviyesi**: Duygusal durum analizi

### 💬 AI Koç Özellikleri
- **Pozitif Yaklaşım**: Destekleyici ve motive edici
- **Türkçe İletişim**: Doğal dil desteği
- **Kişiselleştirilmiş**: Her öğrenciye özel öneriler

## 🏗️ Teknik Detaylar

### Kullanılan Teknolojiler
- **Gemma 3N 4B**: Multimodal AI model
- **Unsloth**: Hızlı model inference
- **Gradio**: Modern web arayüzü
- **OpenCV**: Video işleme

### Sistem Gereksinimleri
- **Kaggle Notebook** (GPU enabled)
- **Web kamerası** erişimi
- **Python 3.8+**
- **Modern web tarayıcısı**

## 📁 Proje Yapısı

```
gemma3n_coach/
├── kaggle_gradio_coach.ipynb        # 🌐 Ana Gradio web arayüzü
├── gradio_coach_app.py              # 🐍 Python uygulaması
├── gemma_integration.py             # 🤖 Gemma 3N entegrasyonu
├── requirements.txt                 # 📦 Gerekli kütüphaneler
├── demo_simple_coach.ipynb          # 🧪 Basit test notebook'u
├── test-gemma-3n-4b-multimodal-with-unsloth.ipynb  # 📚 Referans
└── README.md                        # 📖 Bu dosya
```

## 🚀 Nasıl Kullanılır?

### 1. Kaggle'da Başlatın
1. `kaggle_gradio_coach.ipynb` dosyasını Kaggle'a yükleyin
2. GPU'yu etkinleştirin (Settings > Accelerator > GPU)
3. Internet'i açın (Settings > Internet > On)
4. Notebook'u çalıştırın

### 2. Web Arayüzüne Erişin
1. Notebook çalıştıktan sonra Gradio linkini açın
2. Tarayıcınızda kamera izni verin
3. Modern, kullanıcı dostu arayüzü görün

### 3. AI Koçunuzla Çalışın
1. "🚀 Başlat" butonuna tıklayın
2. Kamera karşısında normal şekilde çalışın
3. AI koçunuz sizi analiz eder ve motivasyon mesajları verir
4. Gerçek zamanlı istatistiklerinizi takip edin
5. Bitirdiğinizde "⏹️ Durdur" butonuna tıklayın

## 🎮 Kullanım Senaryoları

### 📚 Ders Çalışma
- Matematik problemleri çözerken odaklanma desteği
- Kitap okurken dikkat takibi
- Not tutarken motivasyon

### 💻 Kodlama
- Programlama yaparken konsantrasyon
- Debug sürecinde sabır desteği
- Öğrenme sürecinde rehberlik

### 🎨 Yaratıcı Çalışma
- Tasarım yaparken ilham
- Yazı yazarken motivasyon
- Proje geliştirirken destek

## 🌐 Gradio Web Arayüzü Avantajları

### ✨ Kullanıcı Dostu
- **Modern Tasarım**: Profesyonel ve şık arayüz
- **Responsive**: Mobil ve masaüstü uyumlu
- **Kolay Kullanım**: Tek tıkla başlatma

### 🔗 Erişilebilirlik
- **Paylaşılabilir Link**: Arkadaşlarınızla paylaşın
- **Her Yerden Erişim**: İnternet olan her yerden
- **Kurulum Gerektirmez**: Sadece tarayıcı yeterli

### 🔧 Özelleştirme

Kod içinde kolayca değiştirebileceğiniz ayarlar:

```python
# Analiz sıklığı
analysis_interval = 10  # saniye

# AI koç kişiliği
system_prompt = "Sen pozitif bir eğitim koçusun..."

# Geri bildirim uzunluğu
max_new_tokens = 64
```

## 📈 Gelecek Planları

- [ ] Ses analizi ekleme
- [ ] Daha detaylı yüz analizi
- [ ] Çalışma planı önerileri
- [ ] İlerleme raporları
- [ ] Farklı ders türleri için özelleştirme

## 🤝 Katkıda Bulunma

Bu proje açık kaynak kodludur. Önerilerinizi ve katkılarınızı bekliyoruz!

## 📄 Lisans

MIT License - Özgürce kullanabilir ve geliştirebilirsiniz.