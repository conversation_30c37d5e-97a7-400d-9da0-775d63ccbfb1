# 🎓 Gemma 3N AI Eğitim Koçu

## 🎯 Vizyon
*"The future of education isn't AI replacing teachers—it's AI becoming omniscient student companions that know learners better than they know themselves. Through multimodal consciousness (LLM+VLM+audio), AI achieves 360° student awareness that no human teacher could match, making traditional classroom observation as primitive as reading by candlelight."*

## ✨ Basit ve Etkili Yaklaşım

Bu proje **tek bir Jupyter notebook** ile çalışır! Karmaşık web uygulaması yerine, do<PERSON><PERSON><PERSON> Kaggle üzerinde çalışan interaktif bir AI koç sistemi.

## 🚀 Özellikler

### 🤖 Multimodal AI Analiz
- **Görsel Analiz**: Web kamerası ile öğrencinin dikkat seviyesi
- **Gerçek Zamanlı İzleme**: Sürekli durum değerlendirmesi
- **Akıllı Geri Bildirim**: Gemma 3N ile kişiselleştirilmiş öneriler

### 📊 <PERSON><PERSON><PERSON><PERSON> İstatistikler
- **Çalışma Süresi**: Otomatik zaman takibi
- **Dikkat Skoru**: Odaklanma seviyesi ölçümü
- **Motivasyon Seviyesi**: Duygusal durum analizi

### 💬 AI Koç Özellikleri
- **Pozitif Yaklaşım**: Destekleyici ve motive edici
- **Türkçe İletişim**: Doğal dil desteği
- **Kişiselleştirilmiş**: Her öğrenciye özel öneriler

## 🏗️ Teknik Detaylar

### Kullanılan Teknolojiler
- **Gemma 3N 4B**: Multimodal AI model
- **Unsloth**: Hızlı model inference
- **OpenCV**: Video işleme
- **Jupyter Widgets**: Interaktif arayüz

### Sistem Gereksinimleri
- **Kaggle Notebook** (GPU enabled)
- **Web kamerası** erişimi
- **Python 3.8+**

## 📁 Proje Yapısı

```
gemma3n_coach/
├── gemma3n_education_coach.ipynb    # Ana notebook (TEK DOSYA!)
├── test-gemma-3n-4b-multimodal-with-unsloth.ipynb  # Referans notebook
└── README.md                        # Bu dosya
```

## 🚀 Nasıl Kullanılır?

### 1. Kaggle'da Başlatın
1. `gemma3n_education_coach.ipynb` dosyasını Kaggle'a yükleyin
2. GPU'yu etkinleştirin (Settings > Accelerator > GPU)
3. Notebook'u çalıştırın

### 2. Kamerayı Etkinleştirin
1. Tarayıcınızda kamera izni verin
2. "🚀 Koçluğu Başlat" butonuna tıklayın
3. Çalışmaya başlayın!

### 3. AI Koçunuzla Çalışın
- Sistem sizi 10 saniyede bir analiz eder
- Motivasyon mesajları alırsınız
- İstatistiklerinizi takip edersiniz

## 🎮 Kullanım Senaryoları

### 📚 Ders Çalışma
- Matematik problemleri çözerken odaklanma desteği
- Kitap okurken dikkat takibi
- Not tutarken motivasyon

### 💻 Kodlama
- Programlama yaparken konsantrasyon
- Debug sürecinde sabır desteği
- Öğrenme sürecinde rehberlik

### 🎨 Yaratıcı Çalışma
- Tasarım yaparken ilham
- Yazı yazarken motivasyon
- Proje geliştirirken destek

## 🔧 Özelleştirme

Notebook içinde kolayca değiştirebileceğiniz ayarlar:

```python
# Analiz sıklığı (saniye)
analysis_interval = 10

# AI koç kişiliği
system_prompt = "Sen pozitif bir eğitim koçusun..."

# Geri bildirim uzunluğu
max_new_tokens = 64
```

## 📈 Gelecek Planları

- [ ] Ses analizi ekleme
- [ ] Daha detaylı yüz analizi
- [ ] Çalışma planı önerileri
- [ ] İlerleme raporları
- [ ] Farklı ders türleri için özelleştirme

## 🤝 Katkıda Bulunma

Bu proje açık kaynak kodludur. Önerilerinizi ve katkılarınızı bekliyoruz!

## 📄 Lisans

MIT License - Özgürce kullanabilir ve geliştirebilirsiniz.