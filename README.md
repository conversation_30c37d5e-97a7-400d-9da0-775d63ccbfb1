# Gemma 3N AI Education Coach

## 🎯 Vizyon
"The future of education isn't AI replacing teachers—it's AI becoming omniscient student companions that know learners better than they know themselves. Through multimodal consciousness (LLM+VLM+audio), AI achieves 360° student awareness that no human teacher could match, making traditional classroom observation as primitive as reading by candlelight."

## 🚀 Özellikler

### Multimodal AI Analiz
- **Görsel Analiz**: Web kamerası ile öğrencinin yüz ifadeleri, vücut dili ve dikkat seviyesi
- **Ses Analiz**: Öğrencinin konuşma tonu, nefes alışı ve çevresel sesler
- **Metin Analiz**: <PERSON><PERSON><PERSON><PERSON><PERSON> yazdığ<PERSON> notlar, sorular ve geri bildirimler

### Gerçek Zamanlı Koçluk
- **Motivasyon Sistemi**: Anlık motivasyon mesajları ve teşvik edici geri bildirimler
- **Odaklanma Uyarıları**: <PERSON><PERSON><PERSON> dağınıklığı tespit edildiğinde nazik hatırlatmalar
- **Öğrenme Önerileri**: Kişiselleştirilmiş çalışma teknikleri ve stratejiler

### Akıllı Dashboard
- **Çalışma İstatistikleri**: Detaylı performans analizi ve ilerleme takibi
- **Duygusal Durum Haritası**: Öğrenme sürecindeki duygusal değişimler
- **Başarı Metrikleri**: Hedef belirleme ve başarı ölçümü

## 🏗️ Teknik Mimari

### Backend
- **Python Flask**: Web API ve backend servisleri
- **Kaggle API**: Gemma 3N model entegrasyonu
- **WebSocket**: Gerçek zamanlı iletişim
- **OpenCV**: Video işleme ve analiz

### Frontend
- **React.js**: Modern ve responsive kullanıcı arayüzü
- **WebRTC**: Gerçek zamanlı video/ses yakalama
- **Chart.js**: İstatistik görselleştirme
- **Socket.io**: Gerçek zamanlı güncellemeler

### AI Model
- **Gemma 3N 4B**: Multimodal AI model (Kaggle üzerinde)
- **Unsloth**: Hızlı model inference
- **Transformers**: Model yönetimi

## 📁 Proje Yapısı

```
gemma3n_coach/
├── backend/
│   ├── app.py                 # Flask ana uygulama
│   ├── models/
│   │   ├── gemma_client.py    # Kaggle API client
│   │   └── analysis.py        # AI analiz motoru
│   ├── services/
│   │   ├── video_processor.py # Video işleme
│   │   ├── audio_processor.py # Ses işleme
│   │   └── feedback_engine.py # Geri bildirim motoru
│   ├── utils/
│   │   ├── config.py          # Konfigürasyon
│   │   └── helpers.py         # Yardımcı fonksiyonlar
│   └── requirements.txt       # Python bağımlılıkları
├── frontend/
│   ├── src/
│   │   ├── components/        # React bileşenleri
│   │   ├── pages/            # Sayfa bileşenleri
│   │   ├── services/         # API servisleri
│   │   └── utils/            # Yardımcı fonksiyonlar
│   ├── public/               # Statik dosyalar
│   └── package.json          # Node.js bağımlılıkları
├── notebooks/
│   └── test-gemma-3n-4b-multimodal-with-unsloth.ipynb
├── docs/                     # Dokümantasyon
└── README.md
```

## 🚀 Kurulum ve Çalıştırma

### Gereksinimler
- Python 3.8+
- Node.js 16+
- Kaggle API anahtarı
- Web kamerası ve mikrofon

### Backend Kurulumu
```bash
cd backend
pip install -r requirements.txt
python app.py
```

### Frontend Kurulumu
```bash
cd frontend
npm install
npm start
```

## 🔧 Konfigürasyon

### Kaggle API
1. Kaggle hesabınızdan API anahtarı alın
2. `backend/utils/config.py` dosyasında API bilgilerinizi güncelleyin
3. Gemma 3N notebook'unu Kaggle'da çalıştırın

### Çevre Değişkenleri
```bash
KAGGLE_USERNAME=your_username
KAGGLE_KEY=your_api_key
FLASK_ENV=development
REACT_APP_API_URL=http://localhost:5000
```

## 📊 Kullanım

1. **Başlangıç**: Web arayüzünü açın ve kamera/mikrofon izinlerini verin
2. **Çalışma Başlat**: "Çalışmaya Başla" butonuna tıklayın
3. **AI Koç Aktif**: Sistem sizi izlemeye ve analiz etmeye başlar
4. **Geri Bildirim Al**: Gerçek zamanlı motivasyon ve öneriler alın
5. **İstatistikleri İncele**: Çalışma seansınızın detaylı analizini görün

## 🤝 Katkıda Bulunma

Bu proje açık kaynak kodludur. Katkılarınızı bekliyoruz!

## 📄 Lisans

MIT License - Detaylar için LICENSE dosyasına bakın.