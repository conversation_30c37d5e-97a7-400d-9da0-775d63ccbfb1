"""
Konfigürasyon ayarları ve çevre değişkenleri
"""
import os
from dotenv import load_dotenv

# .env dosyasını yükle
load_dotenv()

class Config:
    """Ana konfigürasyon sınıfı"""
    
    # Flask ayarları
    SECRET_KEY = os.getenv('SECRET_KEY', 'dev-secret-key-change-in-production')
    DEBUG = os.getenv('FLASK_ENV', 'development') == 'development'
    HOST = os.getenv('FLASK_HOST', '0.0.0.0')
    PORT = int(os.getenv('FLASK_PORT', 5000))
    
    # Kaggle API ayarları
    KAGGLE_USERNAME = os.getenv('KAGGLE_USERNAME')
    KAGGLE_KEY = os.getenv('KAGGLE_KEY')
    KAGGLE_NOTEBOOK_SLUG = os.getenv('KAGGLE_NOTEBOOK_SLUG', 'gemma3n-coach-inference')
    
    # AI Model ayarları
    MODEL_NAME = "unsloth/gemma-3n-E4B-it"
    MAX_SEQ_LENGTH = 1024
    LOAD_IN_4BIT = True
    TEMPERATURE = 1.0
    TOP_P = 0.95
    TOP_K = 64
    MAX_NEW_TOKENS = 128
    
    # Video işleme ayarları
    VIDEO_WIDTH = 640
    VIDEO_HEIGHT = 480
    VIDEO_FPS = 30
    FRAME_SKIP = 5  # Her 5 frame'de bir analiz et
    
    # Ses işleme ayarları
    AUDIO_SAMPLE_RATE = 16000
    AUDIO_CHUNK_SIZE = 1024
    AUDIO_CHANNELS = 1
    AUDIO_FORMAT = 'wav'
    
    # Analiz ayarları
    ANALYSIS_INTERVAL = 10  # saniye
    FEEDBACK_COOLDOWN = 30  # saniye
    ATTENTION_THRESHOLD = 0.7
    EMOTION_CONFIDENCE_THRESHOLD = 0.6
    
    # Dosya yolları
    UPLOAD_FOLDER = 'uploads'
    TEMP_FOLDER = 'temp'
    LOG_FOLDER = 'logs'
    
    # Güvenlik ayarları
    MAX_CONTENT_LENGTH = 16 * 1024 * 1024  # 16MB
    ALLOWED_EXTENSIONS = {'png', 'jpg', 'jpeg', 'gif', 'wav', 'mp3', 'mp4'}
    
    # CORS ayarları
    CORS_ORIGINS = [
        "http://localhost:3000",  # React development server
        "http://127.0.0.1:3000",
    ]
    
    @staticmethod
    def validate_config():
        """Konfigürasyon doğrulaması"""
        required_vars = ['KAGGLE_USERNAME', 'KAGGLE_KEY']
        missing_vars = []
        
        for var in required_vars:
            if not os.getenv(var):
                missing_vars.append(var)
        
        if missing_vars:
            raise ValueError(f"Eksik çevre değişkenleri: {', '.join(missing_vars)}")
        
        return True

class DevelopmentConfig(Config):
    """Geliştirme ortamı konfigürasyonu"""
    DEBUG = True
    TESTING = False

class ProductionConfig(Config):
    """Üretim ortamı konfigürasyonu"""
    DEBUG = False
    TESTING = False
    
    # Üretim için güvenlik ayarları
    SECRET_KEY = os.getenv('SECRET_KEY')
    if not SECRET_KEY:
        raise ValueError("Üretim ortamında SECRET_KEY gereklidir!")

class TestingConfig(Config):
    """Test ortamı konfigürasyonu"""
    DEBUG = True
    TESTING = True
    WTF_CSRF_ENABLED = False

# Konfigürasyon seçimi
config_dict = {
    'development': DevelopmentConfig,
    'production': ProductionConfig,
    'testing': TestingConfig
}

def get_config():
    """Aktif konfigürasyonu döndür"""
    env = os.getenv('FLASK_ENV', 'development')
    return config_dict.get(env, DevelopmentConfig)
