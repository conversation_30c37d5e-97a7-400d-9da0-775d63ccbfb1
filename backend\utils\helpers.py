"""
Yardımcı fonksiyonlar ve utilities
"""
import os
import time
import base64
import logging
from datetime import datetime
from typing import Optional, Dict, Any, List
import cv2
import numpy as np
from PIL import Image
import io

def setup_logging(log_level: str = "INFO") -> logging.Logger:
    """Logging sistemini kur"""
    logger = logging.getLogger('gemma3n_coach')
    logger.setLevel(getattr(logging, log_level.upper()))
    
    if not logger.handlers:
        # Console handler
        console_handler = logging.StreamHandler()
        console_handler.setLevel(logging.INFO)
        
        # File handler
        os.makedirs('logs', exist_ok=True)
        file_handler = logging.FileHandler(
            f'logs/coach_{datetime.now().strftime("%Y%m%d")}.log'
        )
        file_handler.setLevel(logging.DEBUG)
        
        # Formatter
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        console_handler.setFormatter(formatter)
        file_handler.setFormatter(formatter)
        
        logger.addHandler(console_handler)
        logger.addHandler(file_handler)
    
    return logger

def create_directories():
    """Gerekli klasörleri oluştur"""
    directories = ['uploads', 'temp', 'logs']
    for directory in directories:
        os.makedirs(directory, exist_ok=True)

def encode_image_to_base64(image: np.ndarray) -> str:
    """OpenCV image'ı base64 string'e çevir"""
    _, buffer = cv2.imencode('.jpg', image)
    image_base64 = base64.b64encode(buffer).decode('utf-8')
    return f"data:image/jpeg;base64,{image_base64}"

def decode_base64_to_image(base64_string: str) -> np.ndarray:
    """Base64 string'i OpenCV image'a çevir"""
    if base64_string.startswith('data:image'):
        base64_string = base64_string.split(',')[1]
    
    image_data = base64.b64decode(base64_string)
    image = Image.open(io.BytesIO(image_data))
    return cv2.cvtColor(np.array(image), cv2.COLOR_RGB2BGR)

def resize_image(image: np.ndarray, max_width: int = 640, max_height: int = 480) -> np.ndarray:
    """Görüntüyü yeniden boyutlandır"""
    height, width = image.shape[:2]
    
    # Aspect ratio'yu koru
    if width > max_width or height > max_height:
        scale = min(max_width / width, max_height / height)
        new_width = int(width * scale)
        new_height = int(height * scale)
        image = cv2.resize(image, (new_width, new_height), interpolation=cv2.INTER_AREA)
    
    return image

def extract_face_region(image: np.ndarray) -> Optional[np.ndarray]:
    """Görüntüden yüz bölgesini çıkar"""
    face_cascade = cv2.CascadeClassifier(cv2.data.haarcascades + 'haarcascade_frontalface_default.xml')
    gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
    faces = face_cascade.detectMultiScale(gray, 1.1, 4)
    
    if len(faces) > 0:
        # En büyük yüzü seç
        largest_face = max(faces, key=lambda x: x[2] * x[3])
        x, y, w, h = largest_face
        return image[y:y+h, x:x+w]
    
    return None

def calculate_attention_score(face_landmarks: Dict[str, Any]) -> float:
    """Yüz landmark'larından dikkat skoru hesapla"""
    # Bu basit bir implementasyon - gerçek uygulamada daha sofistike olmalı
    if not face_landmarks:
        return 0.0
    
    # Göz açıklığı, baş pozisyonu gibi faktörleri değerlendir
    # Şimdilik sabit bir değer döndür
    return 0.8

def format_duration(seconds: int) -> str:
    """Saniyeyi okunabilir formata çevir"""
    hours = seconds // 3600
    minutes = (seconds % 3600) // 60
    seconds = seconds % 60
    
    if hours > 0:
        return f"{hours}s {minutes}d {seconds}s"
    elif minutes > 0:
        return f"{minutes}d {seconds}s"
    else:
        return f"{seconds}s"

def validate_file_extension(filename: str, allowed_extensions: set) -> bool:
    """Dosya uzantısını doğrula"""
    return '.' in filename and \
           filename.rsplit('.', 1)[1].lower() in allowed_extensions

def generate_session_id() -> str:
    """Benzersiz session ID oluştur"""
    return f"session_{int(time.time())}_{os.urandom(4).hex()}"

def safe_json_response(data: Any, status_code: int = 200) -> Dict[str, Any]:
    """Güvenli JSON response oluştur"""
    try:
        return {
            'success': True,
            'data': data,
            'timestamp': datetime.now().isoformat(),
            'status_code': status_code
        }
    except Exception as e:
        return {
            'success': False,
            'error': str(e),
            'timestamp': datetime.now().isoformat(),
            'status_code': 500
        }

def calculate_fps(frame_times: List[float], window_size: int = 30) -> float:
    """FPS hesapla"""
    if len(frame_times) < 2:
        return 0.0
    
    recent_times = frame_times[-window_size:]
    if len(recent_times) < 2:
        return 0.0
    
    time_diff = recent_times[-1] - recent_times[0]
    if time_diff == 0:
        return 0.0
    
    return (len(recent_times) - 1) / time_diff

class RateLimiter:
    """Basit rate limiter"""
    
    def __init__(self, max_calls: int, time_window: int):
        self.max_calls = max_calls
        self.time_window = time_window
        self.calls = []
    
    def is_allowed(self) -> bool:
        """Rate limit kontrolü"""
        now = time.time()
        
        # Eski çağrıları temizle
        self.calls = [call_time for call_time in self.calls 
                     if now - call_time < self.time_window]
        
        if len(self.calls) < self.max_calls:
            self.calls.append(now)
            return True
        
        return False
