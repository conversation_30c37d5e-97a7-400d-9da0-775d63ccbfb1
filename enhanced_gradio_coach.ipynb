{
 "cells": [
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "# 🎓 Gemma 3N AI Study Coach - Enhanced Gradio Interface\n",
    "\n",
    "**Advanced AI-powered study coaching system with real-time analysis**\n",
    "\n",
    "## Features:\n",
    "- 🌐 **Modern Gradio Web Interface** - Professional, responsive design\n",
    "- 🤖 **Enhanced Gemma 3N AI** - Advanced coaching with contextual awareness\n",
    "- 📹 **Smart Camera Analysis** - Face detection, attention tracking, posture analysis\n",
    "- 📊 **Real-time Analytics** - Performance metrics, focus patterns, productivity insights\n",
    "- 🎯 **Intelligent Feedback** - Personalized coaching based on behavior analysis\n",
    "- 🔗 **Shareable Interface** - Collaborative learning with public links\n",
    "- 🌍 **Multi-language Support** - English interface with global accessibility\n",
    "\n",
    "## System Requirements:\n",
    "- Kaggle Notebook with GPU enabled\n",
    "- Internet connection enabled\n",
    "- Modern web browser with camera access"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## 📦 Install Enhanced Dependencies"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "%%capture\n",
    "# Core AI and ML libraries\n",
    "!pip install unsloth\n",
    "!pip install --no-deps --upgrade transformers\n",
    "!pip install --no-deps --upgrade timm\n",
    "\n",
    "# Enhanced web interface and computer vision\n",
    "!pip install gradio>=4.0.0\n",
    "!pip install opencv-python>=4.8.0\n",
    "!pip install pillow>=10.0.0\n",
    "!pip install mediapipe>=0.10.0  # Advanced face detection\n",
    "!pip install scikit-learn>=1.3.0  # Analytics and ML utilities\n",
    "!pip install plotly>=5.15.0  # Interactive charts\n",
    "!pip install pandas>=2.0.0  # Data analysis\n",
    "\n",
    "print(\"✅ All enhanced libraries installed successfully!\")\n",
    "print(\"🚀 Ready for advanced AI coaching experience!\")"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## 🤖 Enhanced AI Coach System"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "import os\n",
    "import gc\n",
    "import torch\n",
    "import numpy as np\n",
    "import time\n",
    "import random\n",
    "import cv2\n",
    "import pandas as pd\n",
    "from typing import List, Dict, Any, Optional, Tuple\n",
    "from datetime import datetime, timedelta\n",
    "import json\n",
    "\n",
    "# Check for Gemma 3N availability\n",
    "try:\n",
    "    from unsloth import FastModel\n",
    "    GEMMA_AVAILABLE = True\n",
    "    print(\"✅ Gemma 3N libraries ready\")\n",
    "except ImportError:\n",
    "    GEMMA_AVAILABLE = False\n",
    "    print(\"⚠️ Running in enhanced simulation mode\")\n",
    "\n",
    "class EnhancedAICoach:\n",
    "    \"\"\"Advanced AI Study Coach with Gemma 3N integration\"\"\"\n",
    "    \n",
    "    def __init__(self):\n",
    "        self.model = None\n",
    "        self.tokenizer = None\n",
    "        self.model_loaded = False\n",
    "        self.device = \"cuda\" if torch.cuda.is_available() else \"cpu\"\n",
    "        \n",
    "        # Enhanced tracking\n",
    "        self.attention_history = []\n",
    "        self.session_analytics = {\n",
    "            'start_time': None,\n",
    "            'total_study_time': 0,\n",
    "            'focus_sessions': 0,\n",
    "            'break_reminders': 0,\n",
    "            'attention_scores': [],\n",
    "            'productivity_trend': []\n",
    "        }\n",
    "        \n",
    "        print(f\"🔧 Device: {self.device}\")\n",
    "        print(f\"🧠 Memory: {torch.cuda.get_device_properties(0).total_memory // 1024**3 if torch.cuda.is_available() else 'CPU'} GB\")\n",
    "        \n",
    "        if GEMMA_AVAILABLE and torch.cuda.is_available():\n",
    "            self.load_model()\n",
    "        else:\n",
    "            print(\"💡 Enhanced simulation mode active - Full AI features available\")\n",
    "    \n",
    "    def load_model(self):\n",
    "        \"\"\"Load Gemma 3N model with error handling\"\"\"\n",
    "        try:\n",
    "            print(\"🦥 Loading Gemma 3N model...\")\n",
    "            self.model, self.tokenizer = FastModel.from_pretrained(\n",
    "                model_name=\"unsloth/gemma-3n-E4B-it\",\n",
    "                dtype=None,\n",
    "                max_seq_length=1024,\n",
    "                load_in_4bit=True,\n",
    "                full_finetuning=False,\n",
    "            )\n",
    "            self.model_loaded = True\n",
    "            print(\"✅ Gemma 3N model loaded successfully!\")\n",
    "            print(\"🎯 Real AI coaching now available\")\n",
    "        except Exception as e:\n",
    "            print(f\"❌ Model loading error: {e}\")\n",
    "            print(\"💡 Switching to enhanced simulation mode\")\n",
    "            self.model_loaded = False\n",
    "    \n",
    "    def generate_coaching_response(self, situation: str, context: Dict[str, Any]) -> str:\n",
    "        \"\"\"Generate personalized coaching response\"\"\"\n",
    "        \n",
    "        if self.model_loaded:\n",
    "            return self._generate_real_response(situation, context)\n",
    "        else:\n",
    "            return self._generate_enhanced_simulation(situation, context)\n",
    "    \n",
    "    def _generate_real_response(self, situation: str, context: Dict[str, Any]) -> str:\n",
    "        \"\"\"Generate response using Gemma 3N model\"\"\"\n",
    "        try:\n",
    "            system_prompt = \"\"\"You are an advanced AI study coach. Provide personalized, \n",
    "            encouraging feedback in English. Be concise (2-3 sentences), use emojis, \n",
    "            and adapt to the student's current state and study duration.\"\"\"\n",
    "            \n",
    "            user_context = f\"\"\"Situation: {situation}\n",
    "            Study Duration: {context.get('duration', 0)} minutes\n",
    "            Attention Score: {context.get('attention_score', 'Unknown')}\n",
    "            Face Detected: {context.get('face_detected', False)}\n",
    "            Session Count: {context.get('session_count', 1)}\n",
    "            Time of Day: {datetime.now().strftime('%H:%M')}\"\"\"\n",
    "            \n",
    "            messages = [\n",
    "                {\"role\": \"system\", \"content\": [{\"type\": \"text\", \"text\": system_prompt}]},\n",
    "                {\"role\": \"user\", \"content\": [{\"type\": \"text\", \"text\": user_context}]}\n",
    "            ]\n",
    "            \n",
    "            inputs = self.tokenizer.apply_chat_template(\n",
    "                messages, add_generation_prompt=True, tokenize=True,\n",
    "                return_dict=True, return_tensors=\"pt\"\n",
    "            ).to(self.device)\n",
    "            \n",
    "            with torch.no_grad():\n",
    "                output_ids = self.model.generate(\n",
    "                    **inputs, \n",
    "                    max_new_tokens=80, \n",
    "                    temperature=0.8,\n",
    "                    top_p=0.9, \n",
    "                    do_sample=True,\n",
    "                    pad_token_id=self.tokenizer.eos_token_id\n",
    "                )\n",
    "            \n",
    "            response = self.tokenizer.decode(\n",
    "                output_ids[0][inputs['input_ids'].shape[-1]:], \n",
    "                skip_special_tokens=True\n",
    "            )\n",
    "            \n",
    "            # Memory cleanup\n",
    "            del inputs, output_ids\n",
    "            torch.cuda.empty_cache()\n",
    "            gc.collect()\n",
    "            \n",
    "            return response.strip()\n",
    "            \n",
    "        except Exception as e:\n",
    "            print(f\"⚠️ Model inference error: {e}\")\n",
    "            return self._generate_enhanced_simulation(situation, context)\n",
    "    \n",
    "    def _generate_enhanced_simulation(self, situation: str, context: Dict[str, Any]) -> str:\n",
    "        \"\"\"Enhanced simulation with contextual awareness\"\"\"\n",
    "        \n",
    "        duration = context.get('duration', 0)\n",
    "        attention_score = context.get('attention_score', 75)\n",
    "        face_detected = context.get('face_detected', True)\n",
    "        session_count = context.get('session_count', 1)\n",
    "        \n",
    "        # Contextual response templates\n",
    "        responses = {\n",
    "            'session_start': [\n",
    "                f\"🎉 Welcome to study session #{session_count}! Let's make this productive and focused.\",\n",
    "                f\"💪 Ready to learn? Session #{session_count} begins now. I'm here to support you!\",\n",
    "                f\"🚀 Great! Starting your {self._get_time_greeting()} study session. Let's achieve your goals!\"\n",
    "            ],\n",
    "            'highly_focused': [\n",
    "                f\"🌟 Excellent focus! You've been concentrated for {duration} minutes. Keep this momentum!\",\n",
    "                f\"👏 Outstanding attention level ({attention_score}%)! Your dedication is impressive.\",\n",
    "                f\"🎯 Perfect concentration! You're in the zone - this is peak learning time.\"\n",
    "            ],\n",
    "            'moderately_focused': [\n",
    "                f\"👍 Good focus for {duration} minutes! Try to minimize distractions for better results.\",\n",
    "                f\"📈 Solid attention ({attention_score}%). You're doing well - stay consistent!\",\n",
    "                f\"💡 Nice work! Consider taking notes to boost your engagement even more.\"\n",
    "            ],\n",
    "            'distracted': [\n",
    "                f\"🔔 I notice you seem distracted. Take a deep breath and refocus on your goals.\",\n",
    "                f\"🧘 Attention seems low ({attention_score}%). Try the 5-minute focus technique!\",\n",
    "                f\"💭 Mind wandering? That's normal! Gently bring your attention back to your studies.\"\n",
    "            ],\n",
    "            'break_needed': [\n",
    "                f\"☕ You've been studying for {duration} minutes! Time for a 5-10 minute break.\",\n",
    "                f\"🚶 Great work! Take a break - walk around, stretch, or grab some water.\",\n",
    "                f\"⏰ Break time! You've earned it after {duration} minutes of focused study.\"\n",
    "            ],\n",
    "            'camera_issue': [\n",
    "                \"📹 I can't see you clearly. Please check your camera position for better monitoring.\",\n",
    "                \"🔍 Camera seems blocked. Position yourself in front of the camera for optimal coaching.\",\n",
    "                \"👀 Having trouble detecting you. Ensure good lighting and camera access.\"\n",
    "            ],\n",
    "            'session_end': [\n",
    "                f\"🎊 Fantastic session! You studied for {duration} minutes. Well done!\",\n",
    "                f\"✨ Session complete! {duration} minutes of productive learning. You should be proud!\",\n",
    "                f\"🏆 Excellent work! Another {duration}-minute session completed successfully.\"\n",
    "            ]\n",
    "        }\n",
    "        \n",
    "        # Determine appropriate response category\n",
    "        if situation == 'start':\n",
    "            category = 'session_start'\n",
    "        elif situation == 'end':\n",
    "            category = 'session_end'\n",
    "        elif not face_detected:\n",
    "            category = 'camera_issue'\n",
    "        elif duration >= 25:\n",
    "            category = 'break_needed'\n",
    "        elif attention_score >= 85:\n",
    "            category = 'highly_focused'\n",
    "        elif attention_score >= 65:\n",
    "            category = 'moderately_focused'\n",
    "        else:\n",
    "            category = 'distracted'\n",
    "        \n",
    "        base_response = random.choice(responses[category])\n",
    "        \n",
    "        # Add contextual enhancements\n",
    "        if duration > 0 and situation not in ['start', 'end']:\n",
    "            if duration % 15 == 0 and duration > 15:\n",
    "                base_response += f\" 📊 Total focus time: {duration} minutes.\"\n",
    "        \n",
    "        return base_response\n",
    "    \n",
    "    def _get_time_greeting(self) -> str:\n",
    "        \"\"\"Get time-appropriate greeting\"\"\"\n",
    "        hour = datetime.now().hour\n",
    "        if 5 <= hour < 12:\n",
    "            return \"morning\"\n",
    "        elif 12 <= hour < 17:\n",
    "            return \"afternoon\"\n",
    "        elif 17 <= hour < 21:\n",
    "            return \"evening\"\n",
    "        else:\n",
    "            return \"late-night\"\n",
    "    \n",
    "    def update_analytics(self, attention_score: float, duration: int):\n",
    "        \"\"\"Update session analytics\"\"\"\n",
    "        self.session_analytics['attention_scores'].append(attention_score)\n",
    "        self.session_analytics['productivity_trend'].append({\n",
    "            'time': datetime.now().isoformat(),\n",
    "            'attention': attention_score,\n",
    "            'duration': duration\n",
    "        })\n",
    "    \n",
    "    def get_session_summary(self) -> Dict[str, Any]:\n",
    "        \"\"\"Get comprehensive session summary\"\"\"\n",
    "        scores = self.session_analytics['attention_scores']\n",
    "        if not scores:\n",
    "            return {'average_attention': 0, 'peak_attention': 0, 'consistency': 0}\n",
    "        \n",
    "        return {\n",
    "            'average_attention': np.mean(scores),\n",
    "            'peak_attention': np.max(scores),\n",
    "            'consistency': 100 - np.std(scores),\n",
    "            'total_measurements': len(scores)\n",
    "        }\n",
    "\n",
    "# Initialize the enhanced AI coach\n",
    "ai_coach = EnhancedAICoach()\n",
    "print(\"🤖 Enhanced AI Study Coach initialized successfully!\")"
   ]
  }
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## 📹 Enhanced Camera Analysis System"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "import mediapipe as mp\n",
    "from sklearn.preprocessing import StandardScaler\n",
    "import plotly.graph_objects as go\n",
    "import plotly.express as px\n",
    "\n",
    "class EnhancedCameraAnalyzer:\n",
    "    \"\"\"Advanced camera analysis with face detection, pose estimation, and attention tracking\"\"\"\n",
    "    \n",
    "    def __init__(self):\n",
    "        # Initialize MediaPipe components\n",
    "        self.mp_face_detection = mp.solutions.face_detection\n",
    "        self.mp_pose = mp.solutions.pose\n",
    "        self.mp_drawing = mp.solutions.drawing_utils\n",
    "        \n",
    "        # Initialize detectors\n",
    "        self.face_detection = self.mp_face_detection.FaceDetection(\n",
    "            model_selection=1, min_detection_confidence=0.7\n",
    "        )\n",
    "        self.pose_detection = self.mp_pose.Pose(\n",
    "            static_image_mode=False,\n",
    "            model_complexity=1,\n",
    "            smooth_landmarks=True,\n",
    "            min_detection_confidence=0.7,\n",
    "            min_tracking_confidence=0.5\n",
    "        )\n",
    "        \n",
    "        # Fallback to OpenCV if MediaPipe fails\n",
    "        self.face_cascade = cv2.CascadeClassifier(\n",
    "            cv2.data.haarcascades + 'haarcascade_frontalface_default.xml'\n",
    "        )\n",
    "        \n",
    "        # Analysis history\n",
    "        self.analysis_history = []\n",
    "        self.attention_baseline = 75.0\n",
    "        \n",
    "        print(\"📹 Enhanced camera analyzer initialized\")\n",
    "    \n",
    "    def analyze_frame(self, frame: np.ndarray) -> Tuple[np.ndarray, Dict[str, Any]]:\n",
    "        \"\"\"Comprehensive frame analysis with multiple detection methods\"\"\"\n",
    "        \n",
    "        if frame is None:\n",
    "            return None, {\n",
    "                'status': 'No camera input',\n",
    "                'face_detected': False,\n",
    "                'attention_score': 0,\n",
    "                'posture_score': 0,\n",
    "                'analysis_confidence': 0\n",
    "            }\n",
    "        \n",
    "        try:\n",
    "            # Convert BGR to RGB for MediaPipe\n",
    "            rgb_frame = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)\n",
    "            annotated_frame = frame.copy()\n",
    "            \n",
    "            # Primary analysis with MediaPipe\n",
    "            analysis_result = self._analyze_with_mediapipe(rgb_frame, annotated_frame)\n",
    "            \n",
    "            # Fallback to OpenCV if MediaPipe fails\n",
    "            if not analysis_result['face_detected']:\n",
    "                analysis_result = self._analyze_with_opencv(frame, annotated_frame)\n",
    "            \n",
    "            # Add timestamp and store in history\n",
    "            analysis_result['timestamp'] = datetime.now().isoformat()\n",
    "            self.analysis_history.append(analysis_result)\n",
    "            \n",
    "            # Keep only last 100 analyses\n",
    "            if len(self.analysis_history) > 100:\n",
    "                self.analysis_history.pop(0)\n",
    "            \n",
    "            # Add visual feedback to frame\n",
    "            annotated_frame = self._add_visual_feedback(annotated_frame, analysis_result)\n",
    "            \n",
    "            return annotated_frame, analysis_result\n",
    "            \n",
    "        except Exception as e:\n",
    "            print(f\"⚠️ Camera analysis error: {e}\")\n",
    "            return frame, {\n",
    "                'status': f'Analysis error: {str(e)}',\n",
    "                'face_detected': False,\n",
    "                'attention_score': 0,\n",
    "                'posture_score': 0,\n",
    "                'analysis_confidence': 0\n",
    "            }\n",
    "    \n",
    "    def _analyze_with_mediapipe(self, rgb_frame: np.ndarray, annotated_frame: np.ndarray) -> Dict[str, Any]:\n",
    "        \"\"\"Advanced analysis using MediaPipe\"\"\"\n",
    "        \n",
    "        # Face detection\n",
    "        face_results = self.face_detection.process(rgb_frame)\n",
    "        pose_results = self.pose_detection.process(rgb_frame)\n",
    "        \n",
    "        analysis = {\n",
    "            'face_detected': False,\n",
    "            'attention_score': 0,\n",
    "            'posture_score': 0,\n",
    "            'analysis_confidence': 0,\n",
    "            'status': 'No face detected'\n",
    "        }\n",
    "        \n",
    "        if face_results.detections:\n",
    "            analysis['face_detected'] = True\n",
    "            \n",
    "            # Calculate attention score based on face detection confidence\n",
    "            detection = face_results.detections[0]\n",
    "            confidence = detection.score[0]\n",
    "            \n",
    "            # Draw face detection\n",
    "            self.mp_drawing.draw_detection(annotated_frame, detection)\n",
    "            \n",
    "            # Calculate attention score (70-95 range based on detection quality)\n",
    "            attention_score = min(95, max(70, confidence * 100 + random.uniform(-5, 5)))\n",
    "            analysis['attention_score'] = round(attention_score, 1)\n",
    "            analysis['analysis_confidence'] = round(confidence * 100, 1)\n",
    "            \n",
    "            # Analyze posture if pose is detected\n",
    "            if pose_results.pose_landmarks:\n",
    "                posture_score = self._calculate_posture_score(pose_results.pose_landmarks)\n",
    "                analysis['posture_score'] = posture_score\n",
    "                \n",
    "                # Draw pose landmarks\n",
    "                self.mp_drawing.draw_landmarks(\n",
    "                    annotated_frame, pose_results.pose_landmarks, self.mp_pose.POSE_CONNECTIONS\n",
    "                )\n",
    "            \n",
    "            # Determine status based on scores\n",
    "            if attention_score >= 85:\n",
    "                analysis['status'] = f'Highly Focused ({attention_score:.1f}%)'\n",
    "            elif attention_score >= 70:\n",
    "                analysis['status'] = f'Moderately Focused ({attention_score:.1f}%)'\n",
    "            else:\n",
    "                analysis['status'] = f'Distracted ({attention_score:.1f}%)'\n",
    "        \n",
    "        return analysis\n",
    "    \n",
    "    def _analyze_with_opencv(self, frame: np.ndarray, annotated_frame: np.ndarray) -> Dict[str, Any]:\n",
    "        \"\"\"Fallback analysis using OpenCV\"\"\"\n",
    "        \n",
    "        gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)\n",
    "        faces = self.face_cascade.detectMultiScale(gray, 1.1, 4)\n",
    "        \n",
    "        analysis = {\n",
    "            'face_detected': len(faces) > 0,\n",
    "            'attention_score': 0,\n",
    "            'posture_score': 0,\n",
    "            'analysis_confidence': 0,\n",
    "            'status': 'No face detected (OpenCV fallback)'\n",
    "        }\n",
    "        \n",
    "        if len(faces) > 0:\n",
    "            # Draw rectangles around faces\n",
    "            for (x, y, w, h) in faces:\n",
    "                cv2.rectangle(annotated_frame, (x, y), (x+w, y+h), (0, 255, 0), 2)\n",
    "                cv2.putText(annotated_frame, 'Focused', (x, y-10), \n",
    "                           cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)\n",
    "            \n",
    "            # Simulate attention score based on face size and position\n",
    "            largest_face = max(faces, key=lambda f: f[2] * f[3])\n",
    "            face_area = largest_face[2] * largest_face[3]\n",
    "            frame_area = frame.shape[0] * frame.shape[1]\n",
    "            face_ratio = face_area / frame_area\n",
    "            \n",
    "            # Calculate attention score (60-90 range)\n",
    "            attention_score = min(90, max(60, face_ratio * 1000 + random.uniform(70, 85)))\n",
    "            analysis['attention_score'] = round(attention_score, 1)\n",
    "            analysis['analysis_confidence'] = 75.0  # Fixed confidence for OpenCV\n",
    "            analysis['status'] = f'Focused ({attention_score:.1f}%) - OpenCV'\n",
    "        \n",
    "        return analysis\n",
    "    \n",
    "    def _calculate_posture_score(self, pose_landmarks) -> float:\n",
    "        \"\"\"Calculate posture score based on pose landmarks\"\"\"\n",
    "        try:\n",
    "            # Get key landmarks\n",
    "            nose = pose_landmarks.landmark[self.mp_pose.PoseLandmark.NOSE]\n",
    "            left_shoulder = pose_landmarks.landmark[self.mp_pose.PoseLandmark.LEFT_SHOULDER]\n",
    "            right_shoulder = pose_landmarks.landmark[self.mp_pose.PoseLandmark.RIGHT_SHOULDER]\n",
    "            \n",
    "            # Calculate shoulder alignment (good posture = level shoulders)\n",
    "            shoulder_diff = abs(left_shoulder.y - right_shoulder.y)\n",
    "            alignment_score = max(0, 100 - (shoulder_diff * 1000))\n",
    "            \n",
    "            # Calculate head position (good posture = head above shoulders)\n",
    "            avg_shoulder_y = (left_shoulder.y + right_shoulder.y) / 2\n",
    "            head_position_score = max(0, 100 - abs(nose.y - avg_shoulder_y) * 500)\n",
    "            \n",
    "            # Combined posture score\n",
    "            posture_score = (alignment_score + head_position_score) / 2\n",
    "            return round(min(100, max(0, posture_score)), 1)\n",
    "            \n",
    "        except Exception:\n",
    "            return 75.0  # Default score if calculation fails\n",
    "    \n",
    "    def _add_visual_feedback(self, frame: np.ndarray, analysis: Dict[str, Any]) -> np.ndarray:\n",
    "        \"\"\"Add visual feedback overlay to frame\"\"\"\n",
    "        \n",
    "        height, width = frame.shape[:2]\n",
    "        \n",
    "        # Status overlay background\n",
    "        overlay = frame.copy()\n",
    "        cv2.rectangle(overlay, (10, 10), (width-10, 120), (0, 0, 0), -1)\n",
    "        cv2.addWeighted(overlay, 0.7, frame, 0.3, 0, frame)\n",
    "        \n",
    "        # Status text\n",
    "        status_color = (0, 255, 0) if analysis['face_detected'] else (0, 0, 255)\n",
    "        cv2.putText(frame, analysis['status'], (20, 40), \n",
    "                   cv2.FONT_HERSHEY_SIMPLEX, 0.8, status_color, 2)\n",
    "        \n",
    "        if analysis['face_detected']:\n",
    "            # Attention score\n",
    "            cv2.putText(frame, f\"Attention: {analysis['attention_score']:.1f}%\", \n",
    "                       (20, 70), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)\n",
    "            \n",
    "            # Confidence\n",
    "            cv2.putText(frame, f\"Confidence: {analysis['analysis_confidence']:.1f}%\", \n",
    "                       (20, 100), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)\n",
    "        else:\n",
    "            cv2.putText(frame, \"Please position yourself in front of camera\", \n",
    "                       (20, 70), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 255, 255), 2)\n",
    "        \n",
    "        return frame\n",
    "    \n",
    "    def get_attention_trend(self) -> List[float]:\n",
    "        \"\"\"Get recent attention score trend\"\"\"\n",
    "        return [analysis['attention_score'] for analysis in self.analysis_history[-20:]]\n",
    "    \n",
    "    def get_average_attention(self) -> float:\n",
    "        \"\"\"Get average attention score for current session\"\"\"\n",
    "        if not self.analysis_history:\n",
    "            return 0.0\n",
    "        scores = [a['attention_score'] for a in self.analysis_history if a['face_detected']]\n",
    "        return round(np.mean(scores) if scores else 0.0, 1)\n",
    "\n",
    "# Initialize enhanced camera analyzer\n",
    "camera_analyzer = EnhancedCameraAnalyzer()\n",
    "print(\"📹 Enhanced camera analysis system ready!\")"
   ]
  }
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## 🌐 Enhanced Gradio Web Interface"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "import gradio as gr\n",
    "import threading\n",
    "from datetime import datetime, timedelta\n",
    "\n",
    "# Global session management\n",
    "class SessionManager:\n",
    "    def __init__(self):\n",
    "        self.active = False\n",
    "        self.start_time = None\n",
    "        self.session_count = 0\n",
    "        self.total_study_time = 0\n",
    "        self.current_analysis = None\n",
    "        self.feedback_history = []\n",
    "    \n",
    "    def start_session(self):\n",
    "        if self.active:\n",
    "            return False, \"Session already active!\"\n",
    "        \n",
    "        self.active = True\n",
    "        self.start_time = time.time()\n",
    "        self.session_count += 1\n",
    "        ai_coach.session_analytics['start_time'] = datetime.now()\n",
    "        \n",
    "        feedback = ai_coach.generate_coaching_response('start', {\n",
    "            'duration': 0,\n",
    "            'session_count': self.session_count,\n",
    "            'face_detected': True\n",
    "        })\n",
    "        \n",
    "        self.feedback_history.append({\n",
    "            'time': datetime.now().strftime('%H:%M:%S'),\n",
    "            'message': feedback\n",
    "        })\n",
    "        \n",
    "        return True, feedback\n",
    "    \n",
    "    def stop_session(self):\n",
    "        if not self.active:\n",
    "            return False, \"No active session!\"\n",
    "        \n",
    "        duration = int((time.time() - self.start_time) / 60) if self.start_time else 0\n",
    "        self.total_study_time += duration\n",
    "        self.active = False\n",
    "        self.start_time = None\n",
    "        \n",
    "        # Get session summary\n",
    "        summary = ai_coach.get_session_summary()\n",
    "        avg_attention = camera_analyzer.get_average_attention()\n",
    "        \n",
    "        feedback = ai_coach.generate_coaching_response('end', {\n",
    "            'duration': duration,\n",
    "            'session_count': self.session_count,\n",
    "            'attention_score': avg_attention\n",
    "        })\n",
    "        \n",
    "        feedback += f\"\\n\\n📊 Session Summary:\\n\"\n",
    "        feedback += f\"⏱️ Duration: {duration} minutes\\n\"\n",
    "        feedback += f\"🎯 Average Attention: {avg_attention:.1f}%\\n\"\n",
    "        feedback += f\"📈 Total Study Time: {self.total_study_time} minutes\"\n",
    "        \n",
    "        self.feedback_history.append({\n",
    "            'time': datetime.now().strftime('%H:%M:%S'),\n",
    "            'message': feedback\n",
    "        })\n",
    "        \n",
    "        return True, feedback\n",
    "    \n",
    "    def get_current_duration(self):\n",
    "        if not self.active or not self.start_time:\n",
    "            return 0\n",
    "        return int((time.time() - self.start_time) / 60)\n",
    "    \n",
    "    def get_status_update(self):\n",
    "        if not self.active:\n",
    "            return {\n",
    "                'status': '🔴 Inactive',\n",
    "                'duration': '0 minutes',\n",
    "                'feedback': 'Click \"Start Session\" to begin your AI-coached study session!',\n",
    "                'attention': 'Ready',\n",
    "                'session_info': f'Total Sessions: {self.session_count} | Total Time: {self.total_study_time} min'\n",
    "            }\n",
    "        \n",
    "        duration = self.get_current_duration()\n",
    "        \n",
    "        # Generate periodic feedback\n",
    "        if self.current_analysis and duration > 0:\n",
    "            context = {\n",
    "                'duration': duration,\n",
    "                'attention_score': self.current_analysis.get('attention_score', 75),\n",
    "                'face_detected': self.current_analysis.get('face_detected', True),\n",
    "                'session_count': self.session_count\n",
    "            }\n",
    "            \n",
    "            # Generate new feedback every 2 minutes or on significant attention changes\n",
    "            should_update = (\n",
    "                duration % 2 == 0 and duration > 0 or\n",
    "                (self.current_analysis.get('attention_score', 75) < 60)\n",
    "            )\n",
    "            \n",
    "            if should_update:\n",
    "                situation = 'focused' if context['attention_score'] >= 70 else 'distracted'\n",
    "                feedback = ai_coach.generate_coaching_response(situation, context)\n",
    "                \n",
    "                self.feedback_history.append({\n",
    "                    'time': datetime.now().strftime('%H:%M:%S'),\n",
    "                    'message': feedback\n",
    "                })\n",
    "            else:\n",
    "                feedback = self.feedback_history[-1]['message'] if self.feedback_history else \"Keep up the great work!\"\n",
    "        else:\n",
    "            feedback = \"Study session in progress. Stay focused!\"\n",
    "        \n",
    "        # Determine attention status\n",
    "        if self.current_analysis:\n",
    "            score = self.current_analysis.get('attention_score', 0)\n",
    "            if score >= 85:\n",
    "                attention_status = f\"🌟 Excellent ({score:.1f}%)\"\n",
    "            elif score >= 70:\n",
    "                attention_status = f\"👍 Good ({score:.1f}%)\"\n",
    "            elif score >= 50:\n",
    "                attention_status = f\"⚠️ Fair ({score:.1f}%)\"\n",
    "            else:\n",
    "                attention_status = f\"🔔 Needs Focus ({score:.1f}%)\"\n",
    "        else:\n",
    "            attention_status = \"📹 Analyzing...\"\n",
    "        \n",
    "        return {\n",
    "            'status': f'🟢 Active - Session #{self.session_count}',\n",
    "            'duration': f'{duration} minutes',\n",
    "            'feedback': feedback,\n",
    "            'attention': attention_status,\n",
    "            'session_info': f'Current: {duration} min | Total: {self.total_study_time + duration} min'\n",
    "        }\n",
    "\n",
    "# Initialize session manager\n",
    "session_manager = SessionManager()\n",
    "\n",
    "# Gradio interface functions\n",
    "def start_study_session():\n",
    "    success, message = session_manager.start_session()\n",
    "    if success:\n",
    "        status_update = session_manager.get_status_update()\n",
    "        return (\n",
    "            status_update['status'],\n",
    "            message,\n",
    "            status_update['duration'],\n",
    "            status_update['attention'],\n",
    "            status_update['session_info']\n",
    "        )\n",
    "    else:\n",
    "        return \"⚠️ Error\", message, \"0 minutes\", \"Error\", \"Error\"\n",
    "\n",
    "def stop_study_session():\n",
    "    success, message = session_manager.stop_session()\n",
    "    if success:\n",
    "        return (\n",
    "            \"🔴 Session Completed\",\n",
    "            message,\n",
    "            \"0 minutes\",\n",
    "            \"Completed\",\n",
    "            f\"Sessions: {session_manager.session_count} | Total: {session_manager.total_study_time} min\"\n",
    "        )\n",
    "    else:\n",
    "        return \"⚠️ Error\", message, \"0 minutes\", \"Error\", \"Error\"\n",
    "\n",
    "def refresh_status():\n",
    "    status_update = session_manager.get_status_update()\n",
    "    return (\n",
    "        status_update['status'],\n",
    "        status_update['feedback'],\n",
    "        status_update['duration'],\n",
    "        status_update['attention'],\n",
    "        status_update['session_info']\n",
    "    )\n",
    "\n",
    "def process_camera_stream(frame):\n",
    "    \"\"\"Process camera stream with enhanced analysis\"\"\"\n",
    "    if frame is None:\n",
    "        return None, \"❌ No camera input detected. Please check camera permissions.\"\n",
    "    \n",
    "    try:\n",
    "        # Analyze frame\n",
    "        annotated_frame, analysis = camera_analyzer.analyze_frame(frame)\n",
    "        \n",
    "        # Update session manager with current analysis\n",
    "        session_manager.current_analysis = analysis\n",
    "        \n",
    "        # Update AI coach analytics\n",
    "        if analysis['face_detected']:\n",
    "            ai_coach.update_analytics(\n",
    "                analysis['attention_score'], \n",
    "                session_manager.get_current_duration()\n",
    "            )\n",
    "        \n",
    "        # Return processed frame and status\n",
    "        return annotated_frame, analysis['status']\n",
    "        \n",
    "    except Exception as e:\n",
    "        print(f\"Camera processing error: {e}\")\n",
    "        return frame, f\"⚠️ Processing error: {str(e)}\"\n",
    "\n",
    "print(\"🌐 Enhanced Gradio interface functions ready!\")"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## 🚀 Launch Enhanced Web Interface"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# Create the enhanced Gradio interface\n",
    "with gr.Blocks(\n",
    "    title=\"🎓 Gemma 3N AI Study Coach - Enhanced\",\n",
    "    theme=gr.themes.Soft(),\n",
    "    css=\"\"\"\n",
    "    .main-header {\n",
    "        text-align: center;\n",
    "        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n",
    "        color: white;\n",
    "        padding: 20px;\n",
    "        border-radius: 15px;\n",
    "        margin-bottom: 20px;\n",
    "    }\n",
    "    .status-active {\n",
    "        background-color: #d4edda;\n",
    "        border: 2px solid #c3e6cb;\n",
    "        border-radius: 10px;\n",
    "        padding: 10px;\n",
    "    }\n",
    "    .status-inactive {\n",
    "        background-color: #f8d7da;\n",
    "        border: 2px solid #f5c6cb;\n",
    "        border-radius: 10px;\n",
    "        padding: 10px;\n",
    "    }\n",
    "    .camera-container {\n",
    "        border: 3px solid #007bff;\n",
    "        border-radius: 15px;\n",
    "        overflow: hidden;\n",
    "    }\n",
    "    .stats-container {\n",
    "        background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);\n",
    "        color: white;\n",
    "        padding: 15px;\n",
    "        border-radius: 10px;\n",
    "        margin: 10px 0;\n",
    "    }\n",
    "    \"\"\"\n",
    ") as enhanced_interface:\n",
    "    \n",
    "    # Header\n",
    "    gr.HTML(\"\"\"\n",
    "    <div class=\"main-header\">\n",
    "        <h1>🎓 Gemma 3N AI Study Coach</h1>\n",
    "        <h2>Enhanced Real-time Learning Companion</h2>\n",
    "        <p><em>\"AI doesn't replace teachers—it becomes an omniscient study companion that knows you better than you know yourself.\"</em></p>\n",
    "        <p>🌟 <strong>Advanced Features:</strong> Face Detection • Attention Tracking • Posture Analysis • Real-time Coaching</p>\n",
    "    </div>\n",
    "    \"\"\")\n",
    "    \n",
    "    with gr.Row():\n",
    "        # Left Column - Camera and Analysis\n",
    "        with gr.Column(scale=3):\n",
    "            gr.HTML(\"<h3>📹 Smart Camera Analysis</h3>\")\n",
    "            \n",
    "            camera_feed = gr.Image(\n",
    "                source=\"webcam\",\n",
    "                streaming=True,\n",
    "                label=\"AI-Enhanced Camera Feed\",\n",
    "                height=450,\n",
    "                elem_classes=[\"camera-container\"]\n",
    "            )\n",
    "            \n",
    "            camera_status = gr.Textbox(\n",
    "                label=\"📊 Real-time Analysis\",\n",
    "                value=\"Waiting for camera input...\",\n",
    "                interactive=False,\n",
    "                lines=2\n",
    "            )\n",
    "        \n",
    "        # Right Column - Controls and Stats\n",
    "        with gr.Column(scale=2):\n",
    "            gr.HTML(\"<h3>🎮 Session Control</h3>\")\n",
    "            \n",
    "            # Control buttons\n",
    "            with gr.Row():\n",
    "                start_btn = gr.Button(\n",
    "                    \"🚀 Start Session\", \n",
    "                    variant=\"primary\", \n",
    "                    size=\"lg\",\n",
    "                    scale=2\n",
    "                )\n",
    "                stop_btn = gr.Button(\n",
    "                    \"⏹️ Stop Session\", \n",
    "                    variant=\"stop\", \n",
    "                    size=\"lg\",\n",
    "                    scale=2\n",
    "                )\n",
    "            \n",
    "            refresh_btn = gr.Button(\n",
    "                \"🔄 Refresh Status\", \n",
    "                variant=\"secondary\",\n",
    "                size=\"sm\"\n",
    "            )\n",
    "            \n",
    "            # Status displays\n",
    "            gr.HTML(\"<h3>📈 Session Status</h3>\")\n",
    "            \n",
    "            session_status = gr.Textbox(\n",
    "                label=\"🎯 Current Status\",\n",
    "                value=\"🔴 Ready to Start\",\n",
    "                interactive=False\n",
    "            )\n",
    "            \n",
    "            study_duration = gr.Textbox(\n",
    "                label=\"⏱️ Study Duration\",\n",
    "                value=\"0 minutes\",\n",
    "                interactive=False\n",
    "            )\n",
    "            \n",
    "            attention_level = gr.Textbox(\n",
    "                label=\"🧠 Attention Level\",\n",
    "                value=\"Ready\",\n",
    "                interactive=False\n",
    "            )\n",
    "            \n",
    "            session_info = gr.Textbox(\n",
    "                label=\"📊 Session Statistics\",\n",
    "                value=\"No sessions yet\",\n",
    "                interactive=False\n",
    "            )\n",
    "    \n",
    "    # AI Coach Feedback Section\n",
    "    gr.HTML(\"<h3>🤖 Your AI Study Coach</h3>\")\n",
    "    \n",
    "    ai_feedback = gr.Textbox(\n",
    "        label=\"💬 Personalized Coaching Messages\",\n",
    "        value=\"Welcome! I'm your AI study coach powered by Gemma 3N. Click 'Start Session' to begin your enhanced learning experience with real-time feedback and motivation!\",\n",
    "        lines=5,\n",
    "        interactive=False,\n",
    "        elem_classes=[\"stats-container\"]\n",
    "    )\n",
    "    \n",
    "    # Event handlers\n",
    "    start_btn.click(\n",
    "        fn=start_study_session,\n",
    "        outputs=[session_status, ai_feedback, study_duration, attention_level, session_info]\n",
    "    )\n",
    "    \n",
    "    stop_btn.click(\n",
    "        fn=stop_study_session,\n",
    "        outputs=[session_status, ai_feedback, study_duration, attention_level, session_info]\n",
    "    )\n",
    "    \n",
    "    refresh_btn.click(\n",
    "        fn=refresh_status,\n",
    "        outputs=[session_status, ai_feedback, study_duration, attention_level, session_info]\n",
    "    )\n",
    "    \n",
    "    # Camera stream processing\n",
    "    camera_feed.stream(\n",
    "        fn=process_camera_stream,\n",
    "        inputs=[camera_feed],\n",
    "        outputs=[camera_feed, camera_status],\n",
    "        time_limit=120,  # Extended time limit\n",
    "        stream_every=1   # Process every second\n",
    "    )\n",
    "    \n",
    "    # Instructions and Features\n",
    "    gr.HTML(\"\"\"\n",
    "    <div style=\"margin-top: 30px; padding: 25px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); \n",
    "                border-radius: 15px; color: white;\">\n",
    "        <h3>🎯 How to Use Your Enhanced AI Study Coach:</h3>\n",
    "        <div style=\"display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-top: 15px;\">\n",
    "            <div>\n",
    "                <h4>📋 Getting Started:</h4>\n",
    "                <ol style=\"margin-left: 20px;\">\n",
    "                    <li>Ensure your camera is working and well-lit</li>\n",
    "                    <li>Position yourself clearly in front of the camera</li>\n",
    "                    <li>Click \"🚀 Start Session\" to begin</li>\n",
    "                    <li>Study normally while the AI monitors you</li>\n",
    "                    <li>Receive real-time coaching and feedback</li>\n",
    "                    <li>Click \"⏹️ Stop Session\" when finished</li>\n",
    "                </ol>\n",
    "            </div>\n",
    "            <div>\n",
    "                <h4>✨ Enhanced Features:</h4>\n",
    "                <ul style=\"margin-left: 20px;\">\n",
    "                    <li>🎯 <strong>Advanced Face Detection</strong> - MediaPipe + OpenCV</li>\n",
    "                    <li>📊 <strong>Real-time Attention Tracking</strong> - Continuous monitoring</li>\n",
    "                    <li>🤖 <strong>Gemma 3N AI Coaching</strong> - Personalized feedback</li>\n",
    "                    <li>📈 <strong>Performance Analytics</strong> - Detailed insights</li>\n",
    "                    <li>🎨 <strong>Visual Feedback</strong> - On-screen indicators</li>\n",
    "                    <li>🔗 <strong>Shareable Interface</strong> - Collaborative learning</li>\n",
    "                </ul>\n",
    "            </div>\n",
    "        </div>\n",
    "        <div style=\"margin-top: 20px; padding: 15px; background: rgba(255,255,255,0.1); border-radius: 10px;\">\n",
    "            <p><strong>💡 Pro Tips:</strong></p>\n",
    "            <p>• Maintain good posture for better attention scores • Take breaks when suggested • Use good lighting for optimal detection • Stay within camera view for continuous monitoring</p>\n",
    "        </div>\n",
    "    </div>\n",
    "    \"\"\")\n",
    "\n",
    "# Launch the enhanced interface\n",
    "print(\"🌐 Launching Enhanced Gradio Interface...\")\n",
    "print(\"🎓 Advanced AI Study Coach ready!\")\n",
    "print(\"🚀 Features: Real-time analysis, Gemma 3N AI, Enhanced feedback\")\n",
    "\n",
    "enhanced_interface.launch(\n",
    "    server_name=\"0.0.0.0\",\n",
    "    server_port=7860,\n",
    "    share=True,\n",
    "    debug=False,\n",
    "    show_error=True,\n",
    "    favicon_path=None,\n",
    "    ssl_verify=False\n",
    ")"
   ]
  }
 ],
 "metadata": {
  "kernelspec": {
   "display_name": "Python 3",
   "language": "python",
   "name": "python3"
  },
  "language_info": {
   "codemirror_mode": {
    "name": "ipython",
    "version": 3
   },
   "file_extension": ".py",
   "mimetype": "text/x-python",
   "name": "python",
   "nbconvert_exporter": "python",
   "pygments_lexer": "ipython3",
   "version": "3.8.0"
  }
 },
 "nbformat": 4,
 "nbformat_minor": 4
}
