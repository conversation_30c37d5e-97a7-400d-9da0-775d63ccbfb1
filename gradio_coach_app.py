"""
🎓 Gemma 3N AI Eğitim Koçu - Gradio Web Arayüzü
Kaggle üzerinde çalışan AI destekli eğitim koçluk sistemi
"""

import gradio as gr
import cv2
import numpy as np
import time
import threading
from datetime import datetime
import base64
from PIL import Image
import io
import json

# Global değişkenler
coaching_active = False
start_time = None
session_stats = {
    'study_time': 0,
    'attention_score': 0,
    'motivation_level': 'Hazır',
    'total_sessions': 0
}
feedback_history = []

class SimpleAICoach:
    """Basit AI koç simülasyonu (Gemma 3N entegrasyonu için hazır)"""
    
    def __init__(self):
        self.responses = {
            'başlangıç': [
                "🎉 Harika! Çalışma seansınız başladı. Size eşlik etmekten mutluyum!",
                "💪 Motive olun! Bu seansı birlikte verimli geçirelim.",
                "🚀 Hazırsınız! Hedeflerinize odaklanın, ben yanınızdayım."
            ],
            'odaklanmış': [
                "👍 Harika gidiyorsunuz! Konsantrasyonunuz mükemmel.",
                "🎯 Odaklanmanız çok iyi. Bu tempoda devam edin!",
                "⭐ Performansınız harika! Böyle devam edelim."
            ],
            'dikkatsiz': [
                "🔔 Dikkatinizi toplamaya çalışın. Derin bir nefes alın.",
                "🧘 Biraz odaklanma zamanı. Hedeflerinizi hatırlayın.",
                "💡 Konsantrasyonunuzu artırmak için kısa bir mola verin."
            ],
            'mola': [
                "☕ Mola zamanı! 5 dakika dinlenin, sonra devam edelim.",
                "🚶 Ayağa kalkıp biraz hareket edin. Beyin için iyi olacak.",
                "💧 Su içmeyi unutmayın. Hidrasyon önemli!"
            ],
            'bitiş': [
                "🎊 Tebrikler! Harika bir çalışma seansıydı.",
                "✨ Bugün çok iyi çalıştınız. Kendinizle gurur duyun!",
                "🏆 Başarılı bir seans tamamladınız. Harikasınız!"
            ]
        }
    
    def get_feedback(self, situation, study_duration=0):
        """Duruma göre AI koç geri bildirimi"""
        import random
        
        if study_duration > 25:  # 25 dakikadan fazla
            situation = 'mola'
        
        responses = self.responses.get(situation, self.responses['odaklanmış'])
        return random.choice(responses)

# AI koç instance
ai_coach = SimpleAICoach()

def analyze_webcam_frame(frame):
    """Web kamerası frame'ini analiz et"""
    if frame is None:
        return "Kamera görüntüsü alınamadı"
    
    # Basit yüz tespiti
    face_cascade = cv2.CascadeClassifier(cv2.data.haarcascades + 'haarcascade_frontalface_default.xml')
    gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
    faces = face_cascade.detectMultiScale(gray, 1.1, 4)
    
    if len(faces) > 0:
        # Yüz tespit edildi - dikkat skoru hesapla
        attention_score = np.random.randint(75, 95)  # Simüle edilmiş skor
        return f"✅ Odaklanmış (Dikkat: %{attention_score})"
    else:
        return "⚠️ Kamera karşısında görünmüyorsunuz"

def start_coaching_session():
    """Koçluk seansını başlat"""
    global coaching_active, start_time
    
    if coaching_active:
        return "⚠️ Zaten aktif bir seans var!", None, None, None
    
    coaching_active = True
    start_time = time.time()
    session_stats['total_sessions'] += 1
    
    feedback = ai_coach.get_feedback('başlangıç')
    
    return (
        "🟢 Koçluk Aktif - Çalışmaya başlayabilirsiniz!",
        feedback,
        "0 dakika",
        "Başlangıç"
    )

def stop_coaching_session():
    """Koçluk seansını durdur"""
    global coaching_active, start_time
    
    if not coaching_active:
        return "⚠️ Aktif seans yok!", None, None, None
    
    # Seans süresini hesapla
    duration = int((time.time() - start_time) / 60) if start_time else 0
    session_stats['study_time'] += duration
    
    coaching_active = False
    start_time = None
    
    feedback = ai_coach.get_feedback('bitiş')
    
    return (
        "🔴 Seans Tamamlandı",
        f"{feedback}\n\n📊 Bu seans: {duration} dakika\n📈 Toplam: {session_stats['study_time']} dakika",
        f"{duration} dakika",
        "Tamamlandı"
    )

def get_current_status():
    """Mevcut durumu al"""
    if not coaching_active:
        return (
            "🔴 Koçluk Aktif Değil",
            "Çalışmaya başlamak için 'Koçluğu Başlat' butonuna tıklayın.",
            "0 dakika",
            "Beklemede"
        )
    
    # Çalışma süresini hesapla
    duration = int((time.time() - start_time) / 60) if start_time else 0
    
    # Periyodik geri bildirim (her 5 dakikada)
    if duration > 0 and duration % 5 == 0:
        if duration > 25:
            feedback = ai_coach.get_feedback('mola', duration)
            motivation = "Mola Zamanı"
        else:
            feedback = ai_coach.get_feedback('odaklanmış', duration)
            motivation = "Devam Ediyor"
    else:
        feedback = "Çalışmanız devam ediyor. Harika gidiyorsunuz! 💪"
        motivation = "Aktif"
    
    return (
        f"🟢 Aktif - {duration} dakika",
        feedback,
        f"{duration} dakika",
        motivation
    )

def process_webcam_feed(frame):
    """Web kamerası beslemesini işle"""
    if frame is None:
        return None, "Kamera bağlantısı yok"
    
    # Frame'i analiz et
    analysis = analyze_webcam_frame(frame)
    
    # Yüz tespiti için dikdörtgen çiz
    face_cascade = cv2.CascadeClassifier(cv2.data.haarcascades + 'haarcascade_frontalface_default.xml')
    gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
    faces = face_cascade.detectMultiScale(gray, 1.1, 4)
    
    for (x, y, w, h) in faces:
        cv2.rectangle(frame, (x, y), (x+w, y+h), (0, 255, 0), 2)
        cv2.putText(frame, "Odakli", (x, y-10), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)
    
    return frame, analysis

def create_gradio_interface():
    """Gradio arayüzünü oluştur"""
    
    with gr.Blocks(
        title="🎓 Gemma 3N AI Eğitim Koçu",
        theme=gr.themes.Soft(),
        css="""
        .main-header { text-align: center; color: #2E86AB; margin-bottom: 20px; }
        .status-box { padding: 15px; border-radius: 10px; margin: 10px 0; }
        .active-status { background-color: #d4edda; border: 1px solid #c3e6cb; }
        .inactive-status { background-color: #f8d7da; border: 1px solid #f5c6cb; }
        """
    ) as interface:
        
        gr.HTML("""
        <div class="main-header">
            <h1>🎓 Gemma 3N AI Eğitim Koçu</h1>
            <p><em>"AI öğretmenlerin yerini almıyor - öğrencileri kendilerinden daha iyi tanıyan arkadaşlar oluyor."</em></p>
        </div>
        """)
        
        with gr.Row():
            with gr.Column(scale=2):
                # Web kamerası bölümü
                gr.HTML("<h3>📹 Çalışma Alanınız</h3>")
                webcam = gr.Image(
                    source="webcam",
                    streaming=True,
                    label="Web Kamerası"
                )
                
                camera_analysis = gr.Textbox(
                    label="📊 Kamera Analizi",
                    value="Kamera bekleniyor...",
                    interactive=False
                )
            
            with gr.Column(scale=2):
                # Kontrol paneli
                gr.HTML("<h3>🎮 Kontrol Paneli</h3>")
                
                with gr.Row():
                    start_btn = gr.Button("🚀 Koçluğu Başlat", variant="primary")
                    stop_btn = gr.Button("⏹️ Durdur", variant="stop")
                    status_btn = gr.Button("📊 Durum Güncelle", variant="secondary")
                
                status_display = gr.Textbox(
                    label="📈 Seans Durumu",
                    value="🔴 Koçluk Aktif Değil",
                    interactive=False
                )
                
                # İstatistikler
                gr.HTML("<h3>📊 Çalışma İstatistikleri</h3>")
                study_time_display = gr.Textbox(
                    label="⏱️ Çalışma Süresi",
                    value="0 dakika",
                    interactive=False
                )
                
                motivation_display = gr.Textbox(
                    label="💪 Motivasyon Durumu",
                    value="Hazır",
                    interactive=False
                )
        
        # AI Koç Geri Bildirim Alanı
        gr.HTML("<h3>🤖 AI Koçunuz</h3>")
        feedback_display = gr.Textbox(
            label="💬 AI Koç Mesajları",
            value="Merhaba! Size eşlik etmeye hazırım. Çalışmaya başlamak için yukarıdaki butonu kullanın.",
            lines=4,
            interactive=False
        )
        
        # Event handlers
        start_btn.click(
            fn=start_coaching_session,
            outputs=[status_display, feedback_display, study_time_display, motivation_display]
        )
        
        stop_btn.click(
            fn=stop_coaching_session,
            outputs=[status_display, feedback_display, study_time_display, motivation_display]
        )
        
        status_btn.click(
            fn=get_current_status,
            outputs=[status_display, feedback_display, study_time_display, motivation_display]
        )
        
        webcam.stream(
            fn=process_webcam_feed,
            inputs=[webcam],
            outputs=[webcam, camera_analysis],
            time_limit=30
        )
        
        # Bilgi paneli
        gr.HTML("""
        <div style="margin-top: 30px; padding: 20px; background-color: #f8f9fa; border-radius: 10px;">
            <h4>🎯 Nasıl Kullanılır:</h4>
            <ol>
                <li>Web kameranızın çalıştığından emin olun</li>
                <li>"🚀 Koçluğu Başlat" butonuna tıklayın</li>
                <li>Normal şekilde çalışmaya devam edin</li>
                <li>AI koçunuz sizi izleyecek ve motivasyon mesajları verecek</li>
                <li>İstatistiklerinizi takip edin</li>
            </ol>
            <p><strong>💡 İpucu:</strong> En iyi sonuç için kameranın sizi net görebildiğinden emin olun.</p>
        </div>
        """)
    
    return interface

if __name__ == "__main__":
    # Gradio arayüzünü oluştur ve başlat
    interface = create_gradio_interface()
    
    print("🎓 Gemma 3N AI Eğitim Koçu başlatılıyor...")
    print("🌐 Web arayüzü hazırlanıyor...")
    
    # Kaggle ortamında çalıştırma
    interface.launch(
        server_name="0.0.0.0",  # Tüm IP'lerden erişim
        server_port=7860,       # Standart Gradio portu
        share=True,             # Genel erişim linki oluştur
        debug=True,             # Debug modu
        show_error=True         # Hataları göster
    )
